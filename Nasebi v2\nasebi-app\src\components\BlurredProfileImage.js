import React, { useState, useEffect, useRef } from 'react';
import {
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { useRTL } from '../hooks/useRTL';


const BlurredProfileImage = ({
  source,
  style,
  blurIntensity = 25, // Consistent blur intensity across the app
  onPress,
  imageType = 'regular', // 'regular' or 'match'
  resizeMode = 'cover',
  showEyeIcon = false,
}) => {
  const rtl = useRTL();

  // Always start with the image blurred
  const [revealed, setRevealed] = useState(false);
  const timerRef = useRef(null);

  // Clear the timer when component unmounts
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // Function to handle eye icon press with 3-second timer
  const handleEyeIconPress = (event) => {
    event.stopPropagation();

    // Only reveal if not already revealed
    if (!revealed) {
      setRevealed(true);

      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      // Set timer to re-blur the image after exactly 3 seconds
      timerRef.current = setTimeout(() => {
        setRevealed(false);
      }, 3000);
    }
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  const containerStyle = imageType === 'match'
    ? [styles.matchContainer, style]
    : [styles.container, style];

  const imageStyle = imageType === 'match'
    ? styles.matchImage
    : styles.image;

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      style={containerStyle}
      onPress={handlePress}
    >
      <Image
        source={source}
        style={imageStyle}
        resizeMode={resizeMode}
      />
      {!revealed && (
        <BlurView
          style={StyleSheet.absoluteFill}
          intensity={blurIntensity}
          tint="light"
        />
      )}
      {showEyeIcon && (
        <TouchableOpacity
          style={styles.eyeIconContainer}
          onPress={handleEyeIconPress}
        >
          <Ionicons name="eye-outline" size={24} color="#fff" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    borderRadius: 8,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  matchContainer: {
    overflow: 'hidden',
    borderRadius: 50,
    width: 70,
    height: 70,
    margin: 5,
  },
  matchImage: {
    width: 70,
    height: 70,
  },
  eyeIconContainer: {
    position: 'absolute',
    top: 15,
    start: 15,
    backgroundColor: 'rgba(0,0,0,0.4)',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
});

export default BlurredProfileImage;