{"appName": "<PERSON><PERSON><PERSON>", "welcome": "Welcome to Nasebi", "findMatch": "Find your perfect match", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "yes": "Yes", "no": "No", "block": "Block", "report": "Report", "km": "km", "cm": "cm", "ok": "OK", "edit": "Edit", "delete": "Delete", "done": "Done", "search": "Search", "continue": "Continue", "english": "English", "englishShort": "EN", "arabic": "عربي", "depends": "Depends", "justNow": "Just now", "minutesAgo": "mins ago", "hoursAgo": "hours ago", "yesterday": "Yesterday", "daysAgo": "days ago", "weeksAgo": "{{count}} weeks ago", "monthsAgo": "{{count}} months ago", "yearsAgo": "{{count}} years ago", "male": "Male", "female": "Female", "height": "Height", "weight": "Weight", "nationality": "Nationality", "religion": "Religion", "education": "Education", "occupation": "Occupation", "maritalStatus": "Marital Status", "children": "Children", "smoking": "Smoking", "drinking": "Drinking", "interests": "Interests", "languages": "Languages", "about": "About", "photos": "Photos", "settings": "Settings", "account": "Account", "privacy": "Privacy", "notifications": "Notifications", "help": "Help", "logout": "Logout", "deleteAccount": "Delete Account", "editProfile": "Edit Profile", "viewProfile": "View Profile", "completeProfile": "Complete Profile", "skip": "<PERSON><PERSON>", "profileComplete": "Profile Complete", "profileIncomplete": "Profile Incomplete", "emailNotVerified": "Email verification pending", "updateSuccess": "Profile updated successfully", "updateError": "Failed to update profile", "loadError": "Failed to load profile data", "logoutTitle": "Logout", "logoutConfirm": "Are you sure you want to logout?", "notAuthenticated": "Please log in to view your profile", "bioPlaceholder": "Write something about yourself...", "partnerDescriptionPlaceholder": "Describe your ideal partner...", "namePlaceholder": "Enter your full name", "agePlaceholder": "Enter your age", "birthDate": "Date of Birth", "selectBirthDate": "Select your date of birth", "heightPlaceholder": "Enter your height in cm", "weightPlaceholder": "Enter your weight in kg", "nationalityPlaceholder": "Enter your nationality", "locationPlaceholder": "Enter your city, country", "specificLocationPlaceholder": "Enter neighborhood, area, or address", "specificLocation": "Location", "religiousLevelPlaceholder": "Describe your religious level", "languagesPlaceholder": "Languages you prefer for chatting", "chatLanguages": "Chat Language", "vipMembership": "VIP Membership", "vipDescription": "Upgrade to VIP now and get unlimited messages and exclusive features!", "subscribe": "Subscribe Now", "aboutMe": "About Me", "aboutYou": "About You", "partnerDescription": "Ideal Partner Description", "basicInfo": "Basic Information", "religiousInfo": "Religious Information", "educationAndWork": "Education & Work", "marriagePreferences": "Marriage Preferences", "basicSection": "Basic", "religionSection": "Religion", "educationSection": "Education", "marriageSection": "Marriage", "preferencesSection": "Preferences", "basic": "Basic", "religious": "Religious", "marriage": "Marriage", "bio": "Bio", "name": "Name", "age": "Age", "location": "Location", "gender": "Gender", "phone": "Phone", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "changePassword": "Change Password", "oldPassword": "Current Password", "newPassword": "New Password", "passwordChanged": "Password changed successfully", "passwordError": "Failed to change password", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters", "passwordRequirements": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character", "enterPassword": "Enter your password", "enterNewPassword": "Enter new password", "confirmNewPassword": "Confirm new password", "enterCurrentPassword": "Enter current password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "resetPasswordInstructions": "Enter your email address and we'll send you instructions to reset your password.", "resetPasswordSuccess": "Password reset email sent. Please check your inbox.", "resetPasswordError": "Failed to send password reset email. Please try again.", "login": "<PERSON><PERSON>", "register": "Register", "signUp": "Sign Up", "signIn": "Sign In", "noAccount": "Don't have an account?", "haveAccount": "Already have an account?", "enterEmail": "Enter your email", "enterName": "Enter your name", "enterPhone": "Enter your phone number", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "requiredField": "This field is required", "agreeToTerms": "I agree to the Terms of Service and Privacy Policy", "mustAgreeToTerms": "You must agree to the Terms of Service and Privacy Policy", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed. Please check your credentials and try again.", "registerSuccess": "Registration successful", "registerError": "Registration failed. Please try again.", "networkError": "Network error. Please check your connection and try again.", "serverError": "Server error. Please try again later.", "unexpectedError": "An unexpected error occurred. Please try again.", "sessionExpired": "Your session has expired. Please log in again.", "unauthorized": "Unauthorized. Please log in.", "forbidden": "You don't have permission to access this resource.", "notFound": "Resource not found.", "conflict": "Conflict with current state of the resource.", "tooManyRequests": "Too many requests. Please try again later.", "internalServerError": "Internal server error. Please try again later.", "serviceUnavailable": "Service unavailable. Please try again later.", "timeoutError": "Request timed out. Please try again.", "validationError": "Validation error. Please check your input and try again.", "authError": "Authentication error. Please log in again.", "permissionError": "Permission error. You don't have access to this feature.", "dataError": "Data error. Please try again.", "uploadError": "Upload error. Please try again.", "downloadError": "Download error. Please try again.", "fileError": "File error. Please try again.", "imageError": "Image error. Please try again.", "videoError": "Video error. Please try again.", "audioError": "Audio error. Please try again.", "mediaError": "Media error. Please try again.", "cameraError": "Camera error. Please try again.", "microphoneError": "Microphone error. Please try again.", "locationError": "Location error. Please try again.", "bluetoothError": "Bluetooth error. Please try again.", "wifiError": "Wi-Fi error. Please try again.", "networkConnectionError": "Network connection error. Please try again.", "storageError": "Storage error. Please try again.", "databaseError": "Database error. Please try again.", "cacheError": "Cache error. Please try again.", "memoryError": "Memory error. Please try again.", "batteryError": "Battery error. Please try again.", "deviceError": "Device error. Please try again.", "browserError": "Browser error. Please try again.", "appError": "App error. Please try again.", "systemError": "System error. Please try again.", "unknownError": "Unknown error. Please try again.", "title": "Matches", "matchesTab": "Matches", "likesTab": "<PERSON>s", "newMatches": "New Matches", "messages": "Messages", "seeAll": "See All", "noMessages": "No messages yet", "moreAboutMe": "More About Me", "emptyMatchesTitle": "No matches yet", "emptyMatchesDesc": "Start swiping to find your matches!", "emptyLikesTitle": "No likes yet", "emptyLikesDesc": "When someone likes you, they'll appear here.", "emptyVisitedTitle": "No Visited Profiles", "emptyVisitedDesc": "You haven't visited any profiles yet. Start swiping to discover people.", "emptyLikedTitle": "No Liked Profiles", "emptyLikedDesc": "You haven't liked any profiles yet. Start swiping to find people you like.", "emptyProfilesTitle": "No Profiles", "emptyProfilesDesc": "No profiles to display. Start swiping to discover people.", "visited": "Visited", "youLiked": "You liked", "startSwiping": "Start Swiping", "likeHint": "They liked you! Start chatting now.", "noMatchesFound": "No matches found", "noMoreProfiles": "No more profiles to show right now", "tryAgainLater": "Please try again later", "searchMatches": "Search matches", "noMatches": "No matches yet. Keep swiping!", "startConversation": "Start a conversation", "preferences": "Preferences", "preferencesUpdated": "Preferences updated successfully", "preferencesTitle": "Matching Preferences", "ageRange": "Age Range", "heightRange": "Height Range", "maxDistance": "Maximum Distance", "locations": "Preferred Locations", "searchMessages": "Search messages", "typeMessage": "Type a message...", "send": "Send", "tabLabel": "Messages", "sendImage": "Send Image", "shareLocation": "Share Location", "messageRead": "Read", "messageDelivered": "Delivered", "minuteAgo": "min ago", "hourAgo": "hour ago", "dayAgo": "day ago", "searchPlaceholder": "Search conversations", "noMessagesTitle": "No messages yet", "noMessagesDesc": "When you match with someone, you can start a conversation here.", "viewMatches": "View Matches", "you": "You", "sending": "Sending...", "failedToSend": "Failed to send", "errorFetchingConversations": "Error fetching conversations", "noMoreProfilesAvailable": "No more profiles available at the moment", "findMoreProfiles": "Find more profiles", "discover": "Discover", "compatibility": "{{percentage}}% Compatible", "like": "LIKE", "nope": "NOPE", "loadingProfiles": "Loading profiles...", "swipeLeftToDislike": "Swipe left to dislike", "swipeRightToLike": "Swipe right to like", "superLikeAlert": "You have used a Super Like!", "superLikeMessage": "A 'Hi' message will be sent to this profile", "tapToReveal": "Tap to view (3s)", "errorFetchingProfiles": "Error fetching profiles. Please try again.", "messageSent": "Hi message sent!", "hiMessageWillBeSent": "A \"Hi\" message will be sent to this profile", "version": "Version", "description": "Find your perfect match with <PERSON><PERSON><PERSON>, the leading matrimonial app for Muslims.", "info": "Information", "followUs": "Follow Us", "technical": "Technical Information", "appId": "App ID", "deviceInfo": "Device Info", "website": "Website", "contactUs": "Contact Us", "termsOfService": "Terms of Service", "viewTerms": "View Terms", "privacyPolicy": "Privacy Policy", "viewPrivacy": "View Privacy", "rateApp": "Rate this App", "allRightsReserved": "All Rights Reserved", "developerInfo": "Developed by Nasebi Team", "darkMode": "Dark Mode", "language": "Language", "notificationsSettings": "Notifications Settings", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "newMessages": "New Messages", "appUpdates": "App Updates", "marketingEmails": "Marketing Emails", "accountSettings": "Account <PERSON><PERSON>", "privacySettings": "Privacy Settings", "visibilitySettings": "Visibility Settings", "blockList": "Block List", "hideOnlineStatus": "Hide Online Status", "hideLastSeen": "Hide Last Seen", "hideProfile": "Hide Profile", "appPermissions": "App Permissions", "allowLocationAccess": "Allow Location Access", "allowCameraAccess": "Allow Camera Access", "allowContactsAccess": "Allow Contacts Access", "advancedPrivacy": "Advanced Privacy", "incognitoMode": "Incognito Mode", "privacyDisclaimer": "We respect your privacy and are committed to protecting your personal data in accordance with our Privacy Policy.", "enable": "Enable", "accountDeletedDescription": "Your account has been successfully deleted.", "deleteAccountTitle": "Delete Account", "reportedUsers": "Reported Users", "dataUsage": "Data Usage", "deleteMyAccount": "Delete My Account", "deleteAccountWarning": "This action cannot be undone. All your data will be permanently deleted.", "deleteAccountConfirm": "Are you sure you want to delete your account?", "deleteAccountSuccess": "Your account has been deleted successfully.", "deleteAccountError": "Failed to delete your account. Please try again.", "premiumMembership": "Premium Membership", "currentPlan": "Current Plan", "upgradePlan": "Upgrade Plan", "freePlan": "Free Plan", "basicPlan": "Basic Plan", "premiumPlan": "Premium Plan", "vipPlan": "VIP Plan", "monthlyPlan": "Monthly Plan", "yearlyPlan": "Yearly Plan", "lifetimePlan": "Lifetime Plan", "planFeatures": "Plan Features", "unlimitedLikes": "Unlimited Likes", "unlimitedMatches": "Unlimited Matches", "unlimitedMessages": "Unlimited Messages", "seeWhoLikesYou": "See Who Likes You", "advancedFilters": "Advanced Filters", "apply": "Apply", "reset": "Reset", "from": "From", "to": "To", "any": "Any", "hasProfilePhoto": "Has Profile Photo", "heightInCm": "Height (cm)", "weightInKg": "Weight (kg)", "enterNationality": "Enter Nationality", "enterEthnicity": "Enter Ethnicity", "chooseSkinColor": "Choose Skin Color", "chooseEducationLevel": "Choose Education Level", "chooseJobLevel": "Choose Job Level", "chooseIncomeLevel": "Choose Income Level", "chooseReligiousLevel": "Choose Religious Level", "choosePrayerLevel": "Choose Prayer Level", "chooseFastingLevel": "Choose Fasting Level", "chooseHajjStatus": "<PERSON><PERSON>", "chooseMaritalStatus": "<PERSON>ose Marital Status", "chooseHasChildren": "<PERSON><PERSON> Has Children", "chooseWantsChildren": "Choose Wants Children", "chooseHealthStatus": "Choose Health Status", "lifestyle": "Lifestyle", "marriageReadiness": "Marriage Readiness", "chooseMarriageReadiness": "Choose Marriage Readiness", "preferredResidence": "Preferred Residence", "chooseResidence": "Choose Residence", "allowsWifeToWork": "Allows Wife to Work", "chooseWifeWorkStatus": "Choose Wife Work Status", "tribalAffiliation": "Tribal Affiliation", "healthStatus": "Health Status", "common_yes": "Yes", "common_no": "No", "religious_very_religious": "Very Religious", "religious_religious": "Religious", "religious_somewhat_religious": "Somewhat Religious", "religious_not_religious": "Not Religious", "prayer_daily": "Daily", "prayer_weekly": "Weekly", "prayer_sometimes": "Sometimes", "prayer_religious_occasions": "Religious Occasions Only", "prayer_never": "Never", "fasting_always": "Always", "fasting_sometimes": "Sometimes", "fasting_never": "Never", "fasting_prefer_not_to_say": "Prefer Not to Say", "priorityMatches": "Priority Matches", "boostProfile": "Boost Profile", "hideAds": "Hide Ads", "premiumSupport": "Premium Support", "exclusiveFeatures": "Exclusive Features", "subscribeNow": "Subscribe Now", "manageSubscription": "Manage Subscription", "cancelSubscription": "Cancel Subscription", "subscriptionDetails": "Subscription Details", "nextBillingDate": "Next Billing Date", "paymentMethod": "Payment Method", "updatePaymentMethod": "Update Payment Method", "billingHistory": "Billing History", "receiptNumber": "Receipt Number", "transactionDate": "Transaction Date", "amount": "Amount", "status": "Status", "subscriptionActive": "Subscription Active", "subscriptionInactive": "Subscription Inactive", "subscriptionExpired": "Subscription Expired", "subscriptionCancelled": "Subscription Cancelled", "subscriptionPending": "Subscription Pending", "subscriptionFailed": "Subscription Failed", "subscriptionSuccess": "Subscription Successful", "subscriptionError": "Subscription Error", "countryOfResidence": "Country of Residence", "city": "City", "selectCountry": "Select Country", "selectCity": "Select City", "selectCountryFirst": "Select Country First", "skinColor": "Skin Color", "bodyType": "Body Type", "hairColor": "Hair Color", "eyeColor": "Eye Color", "ethnicity": "Ethnicity", "religiousLevel": "Religious Level", "religiousSect": "Religious Sect", "hajjStatus": "Hajj Status", "prayerFrequency": "Prayer Frequency", "fastingFrequency": "Fasting Frequency", "educationLevel": "Education Level", "jobLevel": "Job Level", "income": "Income", "incomeLevel": "Income Level", "haveChildren": "Have Children", "wantChildren": "Want Children", "diet": "Diet", "pets": "Pets", "politicalViews": "Political Views", "relationshipType": "Relationship Type", "marriageTimeline": "Marriage Timeline", "relocate": "Willing to Relocate", "marriageType": "Marriage Type", "public": "Public", "private": "Private", "noNotifications": "No Notifications", "markAllAsRead": "<PERSON> as <PERSON>", "clearAll": "Clear All", "newMatch": "New Match", "newLike": "New Like", "newMessage": "New Message", "newVisitor": "New Visitor", "profileUpdate": "Profile Update", "accountUpdate": "Account Update", "systemNotification": "System Notification", "educationLessThanHighschool": "Less than High School", "educationHighschool": "High School", "educationCollegeDegree": "College Degree", "educationBachelors": "Bachelor's Degree", "educationMasters": "Master's Degree", "educationDoctorate": "Doctorate", "educationReligiousEducation": "Religious Education", "religiousVeryReligious": "Very Religious", "religiousReligious": "Religious", "religiousSomewhatReligious": "Somewhat Religious", "religiousNotReligious": "Not Religious", "prayerDaily": "Daily", "prayerWeekly": "Weekly", "prayerSometimes": "Sometimes", "prayerReligiousOccasions": "Religious Occasions Only", "prayerRarely": "Rarely", "prayerNever": "Never", "fastingAlways": "Always", "fastingSometimes": "Sometimes", "fastingNever": "Never", "fastingPreferNotToSay": "Prefer Not to Say", "hajjCompleted": "Completed", "hajjPlanningSoon": "Planning Soon", "hajjPlanningFuture": "Planning in the Future", "hajjNotPlanned": "Not Planned", "maritalStatusSingle": "Single", "maritalStatusDivorced": "Divorced", "maritalStatusWidowed": "Widowed", "maritalStatusMarried": "Married", "marriageReadinessImmediately": "Immediately", "marriageReadinessWithinYear": "Within a Year", "marriageReadinessAfterTwoYears": "After Two Years", "marriageReadinessNotDecided": "Not Decided", "residenceOwnHome": "Own Home", "residenceFamilyHome": "Family Home", "residenceFamilyHomeTemporarily": "Family Home Temporarily", "residenceUndecided": "Undecided", "childrenSoon": "Soon", "childrenAfterTwoYears": "After Two Years", "childrenDepends": "Depends", "childrenNo": "No", "workYes": "Yes", "workYesFromHome": "Yes, From Home", "workDepends": "Depends", "workNo": "No", "healthGoodHealth": "Good Health", "healthSpecialNeeds": "Special Needs", "healthChronicDisease": "Chronic Disease", "healthInfertile": "Infertile", "smokingYes": "Yes", "smokingSometimes": "Sometimes", "smokingNo": "No", "jobStudent": "Student", "jobEmployee": "Employee", "jobSeniorEmployee": "Senior Employee", "jobManager": "Manager", "jobUnemployed": "Unemployed", "jobPreferNotToSay": "Prefer Not to Say", "incomeNoIncome": "No Income", "incomeLow": "Low", "incomeAverage": "Average", "incomeHigh": "High", "religiousSectSunni": "Sunni", "religiousSectShia": "Shia", "religiousSectOther": "Other", "genderMale": "Male", "genderFemale": "Female", "languageArabic": "Arabic", "languageEnglish": "English", "languageFrench": "French", "languageSpanish": "Spanish", "languageTurkish": "Turkish", "languageUrdu": "Urdu", "languageHindi": "Hindi", "languagePersian": "Persian", "skinColor_very_fair": "Very Fair", "skinColor_fair": "Fair", "skinColor_medium": "Medium", "skinColor_tan": "<PERSON>", "skinColor_dark": "Dark", "skinColor_very_dark": "Very Dark"}