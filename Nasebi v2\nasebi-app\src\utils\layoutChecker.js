/**
 * Utility to help check for RTL and theme issues in components
 * This is for development purposes only
 */
import { StyleSheet } from 'react-native';

/**
 * Scans a StyleSheet object for potential RTL issues
 * @param {Object} styles StyleSheet object to check
 * @return {Array} Array of issues found
 */
export const checkStylesForRTLIssues = (styles) => {
  const issues = [];
  
  // Properties that should use logical equivalents
  const rtlSensitiveProps = {
    left: 'start',
    right: 'end',
    marginLeft: 'marginStart',
    marginRight: 'marginEnd',
    paddingLeft: 'paddingStart',
    paddingRight: 'paddingEnd',
    borderLeftWidth: 'borderStartWidth',
    borderRightWidth: 'borderEndWidth',
    borderLeftColor: 'borderStartColor',
    borderRightColor: 'borderEndColor',
  };
  
  // Check each style object
  Object.entries(styles).forEach(([styleName, styleObj]) => {
    // Check each property in the style
    Object.entries(styleObj).forEach(([propName, propValue]) => {
      if (rtlSensitiveProps[propName]) {
        issues.push({
          styleName,
          property: propName,
          suggestedReplacement: rtlSensitiveProps[propName],
          value: propValue,
        });
      }
      
      // Check for RTL-sensitive transform properties
      if (propName === 'transform' && Array.isArray(propValue)) {
        propValue.forEach((transform, index) => {
          if (transform.translateX) {
            issues.push({
              styleName,
              property: `transform[${index}].translateX`,
              suggestedFix: 'Use RTL-aware code: useRTL().reverseIfRTL(value)',
              value: transform.translateX,
            });
          }
        });
      }
    });
  });
  
  return issues;
};

/**
 * Scans a StyleSheet object for hardcoded colors that should use theme
 * @param {Object} styles StyleSheet object to check
 * @return {Array} Array of issues found
 */
export const checkStylesForThemeIssues = (styles) => {
  const issues = [];
  
  // Common color values that should use theme
  const colorValues = [
    '#fff', '#ffffff', 'white', 
    '#000', '#000000', 'black',
    '#333', '#666', '#999', '#ccc', '#ddd', '#eee', '#f5f5f5',
    'red', 'green', 'blue', 'yellow', 'orange', 'purple',
  ];
  
  // Properties that often need theming
  const themeProperties = [
    'color', 'backgroundColor', 'borderColor', 'tintColor',
    'shadowColor', 'textShadowColor'
  ];
  
  Object.entries(styles).forEach(([styleName, styleObj]) => {
    Object.entries(styleObj).forEach(([propName, propValue]) => {
      // Check if this is a color property with a hardcoded value
      if (
        themeProperties.includes(propName) && 
        typeof propValue === 'string' && 
        (colorValues.includes(propValue.toLowerCase()) || propValue.startsWith('#'))
      ) {
        issues.push({
          styleName,
          property: propName,
          suggestedFix: 'Use theme color: { color: colors.text }',
          value: propValue,
        });
      }
    });
  });
  
  return issues;
};

/**
 * Helper function to print issues to console during development
 */
export const debugLayoutIssues = (componentName, styles) => {
  if (__DEV__) {
    const rtlIssues = checkStylesForRTLIssues(styles);
    const themeIssues = checkStylesForThemeIssues(styles);
    
    if (rtlIssues.length > 0 || themeIssues.length > 0) {
      console.log(`====== Layout Issues in ${componentName} ======`);
      
      if (rtlIssues.length > 0) {
        console.log('--- RTL Issues ---');
        rtlIssues.forEach(issue => {
          console.log(`Style: ${issue.styleName}, Property: ${issue.property}`);
          console.log(`Suggestion: Replace with ${issue.suggestedReplacement || issue.suggestedFix}`);
        });
      }
      
      if (themeIssues.length > 0) {
        console.log('--- Theme Issues ---');
        themeIssues.forEach(issue => {
          console.log(`Style: ${issue.styleName}, Property: ${issue.property}`);
          console.log(`Value: ${issue.value}, Suggestion: ${issue.suggestedFix}`);
        });
      }
      
      console.log('==============================');
    }
  }
}; 