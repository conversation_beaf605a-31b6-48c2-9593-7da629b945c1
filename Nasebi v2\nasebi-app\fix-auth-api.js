/**
 * Direct Fix for Authentication API Connection Issues
 * 
 * This script directly modifies the necessary files to fix the authentication API connection issues.
 * 
 * Usage:
 * 1. Update the YOUR_IP_ADDRESS constant with your actual IP address
 * 2. Run this script with Node.js:
 *    node fix-auth-api.js
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// ============================================================================
// STEP 1: Update this constant with your actual IP address
// ============================================================================
const YOUR_IP_ADDRESS = '************'; // Replace with your actual IP address
// ============================================================================

// Paths to the files we need to modify
const CONFIG_PATH = path.join(__dirname, 'src', 'config', 'config.js');
const API_PATH = path.join(__dirname, 'src', 'services', 'api.js');

// Backup the original files
const CONFIG_BACKUP_PATH = CONFIG_PATH + '.backup';
const API_BACKUP_PATH = API_PATH + '.backup';

// Create backups if they don't exist
if (!fs.existsSync(CONFIG_BACKUP_PATH)) {
  fs.copyFileSync(CONFIG_PATH, CONFIG_BACKUP_PATH);
  console.log(`Created backup of config.js at ${CONFIG_BACKUP_PATH}`);
}

if (!fs.existsSync(API_BACKUP_PATH)) {
  fs.copyFileSync(API_PATH, API_BACKUP_PATH);
  console.log(`Created backup of api.js at ${API_BACKUP_PATH}`);
}

// New content for config.js
const configContent = `import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Default API URL based on direct IP address
// This ensures consistent access from both emulators and physical devices
const API_URL = 'http://${YOUR_IP_ADDRESS}:3000';

console.log('Using direct IP address for API:', API_URL);
console.log('Platform:', Platform.OS);

// Override with environment variables if available
if (Constants.expoConfig?.extra?.apiUrl) {
  API_URL = Constants.expoConfig.extra.apiUrl;
  console.log('Overriding API URL from environment:', API_URL);
}

// Environment settings
const isDevelopment = process.env.NODE_ENV === 'development' || __DEV__;

// Stripe configuration
const STRIPE_PUBLISHABLE_KEY =
  Constants.expoConfig?.extra?.stripePublishableKey ||
  'pk_test_51OpGOZFzxLuwKT1JYzL55vTzxr8iUP8FqQeN1ofUWeSZAuvsfgsLCmQDToy4wJXnEurXlR6VVRKdEYZ6cz1VZtjw00IWMZrpKb';

// App configuration
const config = {
  API_URL,
  STRIPE_PUBLISHABLE_KEY,
  isDevelopment,
  // Set to false to use real data from the backend
  useMockData: false, // Disable mock data to use real database
  // App settings
  defaultLanguage: 'ar',
  supportedLanguages: ['ar', 'en'],
  apiTimeout: 30000, // 30 seconds
  profileCompletionMinimum: 70, // Percentage required to be considered "complete"
  maxPhotos: 6, // Maximum number of photos allowed per user
  requestTimeoutMs: 30000, // 30 seconds (matching apiTimeout)
};

export default config;`;

// New content for api.js (first part)
const apiContentPart1 = `import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import config from '../config/config';

// Use direct IP address for all API requests
const API_URL = 'http://${YOUR_IP_ADDRESS}:3000';

console.log(\`Using direct IP address for API: \${API_URL}\`);

// Create axios instance with direct IP
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: config.apiTimeout,
});

// Add a request interceptor to include auth token in all requests
api.interceptors.request.use(
  async (config) => {
    try {
      // Try to get the token from AsyncStorage
      const token = await AsyncStorage.getItem('auth-token');

      // If token exists, add it to the headers
      if (token) {
        config.headers.Authorization = \`Bearer \${token}\`;
        console.log('Added auth token to request:', config.url);
      } else {
        console.log('No auth token available for request:', config.url);
      }
    } catch (error) {
      console.error('Error adding auth token to request:', error);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);`;

// Write the new content to the files
fs.writeFileSync(CONFIG_PATH, configContent);
console.log(`Updated ${CONFIG_PATH} with direct IP address: ${YOUR_IP_ADDRESS}`);

// For api.js, we need to read the file first and then modify it
let apiContent = fs.readFileSync(API_PATH, 'utf8');

// Replace the beginning of the file with our new content
apiContent = apiContent.replace(/import axios.*?timeout: config\.apiTimeout,\s*\}\);/s, apiContentPart1);

// Replace any occurrences of ******** with the direct IP
apiContent = apiContent.replace(/10\.0\.2\.2/g, YOUR_IP_ADDRESS);

// Write the modified content back to the file
fs.writeFileSync(API_PATH, apiContent);
console.log(`Updated ${API_PATH} with direct IP address: ${YOUR_IP_ADDRESS}`);

console.log('\nFix applied successfully!');
console.log('\nPlease restart your app to apply the changes.');
console.log('\nIf you still experience issues, try the following:');
console.log('1. Make sure your backend server is running at http://localhost:3000');
console.log('2. Make sure your firewall allows incoming connections to port 3000');
console.log('3. Make sure your device is connected to the same network as your computer');
console.log('4. Try using a different IP address if you have multiple network adapters');

// Get the local IP addresses for reference
const networkInterfaces = os.networkInterfaces();
console.log('\nYour local IP addresses:');
for (const interfaceName in networkInterfaces) {
  const interfaceInfo = networkInterfaces[interfaceName];
  for (const info of interfaceInfo) {
    if (info.family === 'IPv4' && !info.internal) {
      console.log(`- ${interfaceName}: ${info.address}`);
    }
  }
}
