<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\src\main\res"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\build\generated\res\rs\release"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\src\main\res"><file path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\src\main\res\values\styles.xml" qualifiers=""><style name="SpinnerDatePickerDialogBase" ns1:targetApi="lollipop" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="android:datePickerStyle">@style/SpinnerDatePickerStyle</item>
    </style><style name="SpinnerDatePickerDialog" parent="SpinnerDatePickerDialogBase"/><style name="SpinnerDatePickerStyle" ns1:targetApi="lollipop" parent="android:Widget.Material.DatePicker">
        <item name="android:datePickerMode">spinner</item>
    </style><style name="SpinnerTimePickerDialogBase" ns1:targetApi="lollipop" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="android:timePickerStyle">@style/SpinnerTimePickerStyle</item>
    </style><style name="SpinnerTimePickerDialog" parent="SpinnerTimePickerDialogBase"/><style name="SpinnerTimePickerStyle" ns1:targetApi="lollipop" parent="android:Widget.Material.TimePicker">
        <item name="android:timePickerMode">spinner</item>
    </style></file><file path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="SpinnerDatePickerDialog" parent="SpinnerDatePickerDialogBase">
		<item name="android:windowBackground">#424242</item>
	</style><style name="SpinnerTimePickerDialog" parent="SpinnerTimePickerDialogBase">
		<item name="android:windowBackground">#424242</item>
	</style></file></source><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\build\generated\res\rs\release"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-community\datetimepicker\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>