import React from 'react';
import { Text, StyleSheet, Platform } from 'react-native';
import { useRTL } from '../hooks/useRTL';
import { useLanguage } from '../context/LanguageContext';

const RTLText = ({ style, children, ...props }) => {
  const { align, isRTL } = useRTL();
  const { language } = useLanguage();
  
  return (
    <Text
      {...props}
      style={[
        styles.text,
        align,
        language === 'ar' ? styles.arabicText : styles.englishText,
        style,
      ]}
    >
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  text: {
    fontSize: 16,
    color: '#333',
    textAlign: 'auto', // Let the system decide based on content
  },
  arabicText: {
    fontFamily: Platform.OS === 'ios' 
      ? 'ArialHebrew' // iOS Arabic font
      : Platform.OS === 'android' 
        ? 'sans-serif' // Android default font that supports Arabic
        : 'Arial', // Web fallback
    lineHeight: 24, // Adjusted for Arabic text
    writingDirection: 'rtl',
  },
  englishText: {
    fontFamily: Platform.OS === 'ios' 
      ? 'System' 
      : Platform.OS === 'android' 
        ? 'Roboto' 
        : 'Arial',
    lineHeight: 22,
    writingDirection: 'ltr',
  }
});

export default RTLText;