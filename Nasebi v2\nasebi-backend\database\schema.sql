-- Nasebi Database Schema
-- Core User Tables
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  is_verified <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
  email_verification_token VARCHAR(255),
  phone_number VARCHAR(20),
  phone_verified BOOLEAN DEFAULT FALSE,
  account_status ENUM('active', 'suspended', 'deactivated') DEFAULT 'active',
  last_active TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_profiles (
  user_id INT PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  gender ENUM('male', 'female') NOT NULL,
  birth_date DATE,
  height INT,
  weight INT,
  nationality VARCHAR(100),
  country_of_residence VARCHAR(100),
  city VARCHAR(100),
  ethnicity VARCHAR(100),
  language VARCHAR(50) DEFAULT 'en',
  location VARCHAR(255),
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  occupation VARCHAR(255),
  job_title VARCHAR(100),
  job_level ENUM('student', 'employee', 'senior_employee', 'manager', 'unemployed', 'prefer_not_to_say') DEFAULT NULL,
  education_level ENUM('less_than_highschool', 'highschool', 'college_degree', 'masters', 'doctorate') DEFAULT NULL,
  education_field VARCHAR(255),
  company VARCHAR(255),
  income_level ENUM('no_income', 'low', 'average', 'high') DEFAULT NULL,
  bio TEXT,
  marital_status ENUM('single', 'divorced', 'widowed', 'married') DEFAULT NULL,
  has_children BOOLEAN DEFAULT FALSE,
  number_of_children INT DEFAULT 0,
  wants_children ENUM('soon', 'after_two_years', 'depends', 'no') DEFAULT NULL,
  allows_wife_to_work ENUM('yes', 'yes_from_home', 'depends', 'no') DEFAULT NULL,
  health_status ENUM('good_health', 'special_needs', 'chronic_disease', 'infertile') DEFAULT NULL,
  skin_color ENUM('very_fair', 'fair', 'medium', 'tan', 'dark', 'very_dark') DEFAULT NULL,
  tribal_affiliation BOOLEAN DEFAULT NULL,
  marriage_readiness ENUM('immediately', 'within_year', 'after_two_years', 'not_decided') DEFAULT NULL,
  religious_level VARCHAR(50),
  religious_sect VARCHAR(50),
  prayer_level ENUM('daily', 'weekly', 'sometimes', 'religious_occasions', 'never') DEFAULT NULL,
  fasting_level ENUM('always', 'sometimes', 'never', 'prefer_not_to_say') DEFAULT NULL,
  hajj_status ENUM('completed', 'planning_soon', 'planning_future', 'not_planned') DEFAULT NULL,
  smoking ENUM('yes', 'sometimes', 'no') DEFAULT NULL,
  preferred_residence ENUM('own_home', 'family_home', 'family_home_temporarily', 'undecided') DEFAULT NULL,
  preferred_marriage_type TEXT,
  chat_languages TEXT,
  partner_description TEXT,
  profile_image TEXT,
  profile_completion_percentage INT DEFAULT 0,
  is_profile_public BOOLEAN DEFAULT TRUE,
  last_active TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Photos and Media
CREATE TABLE IF NOT EXISTS user_photos (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  photo_url TEXT NOT NULL,
  photo_type ENUM('profile', 'gallery') DEFAULT 'gallery',
  is_primary BOOLEAN DEFAULT FALSE,
  is_verified BOOLEAN DEFAULT FALSE,
  moderation_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Matching System
CREATE TABLE IF NOT EXISTS matching_preferences (
  user_id INT PRIMARY KEY,
  min_age INT,
  max_age INT,
  preferred_height_min INT,
  preferred_height_max INT,
  preferred_weight_min INT,
  preferred_weight_max INT,
  preferred_locations TEXT,
  max_distance INT,
  preferred_nationalities TEXT,
  preferred_ethnicities TEXT,
  preferred_skin_colors TEXT,
  preferred_education_levels TEXT,
  preferred_job_levels TEXT,
  preferred_income_levels TEXT,
  preferred_marital_status TEXT,
  accept_children BOOLEAN,
  preferred_religious_level TEXT,
  preferred_religious_sect TEXT,
  preferred_prayer_level TEXT,
  preferred_fasting_level TEXT,
  preferred_hajj_status TEXT,
  accept_smoker BOOLEAN,
  marriage_type_preferences TEXT,
  tribal_preference BOOLEAN,
  preferred_languages TEXT,
  deal_breakers TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS user_likes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  liked_user_id INT NOT NULL,
  like_type ENUM('like', 'super_like') DEFAULT 'like',
  message TEXT,
  is_anonymous BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_like (user_id, liked_user_id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (liked_user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS user_blocks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  blocked_user_id INT NOT NULL,
  reason TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_block (user_id, blocked_user_id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (blocked_user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS matches (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user1_id INT NOT NULL,
  user2_id INT NOT NULL,
  match_score DECIMAL(5,2),
  status ENUM('active', 'archived', 'unmatched') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_match (user1_id, user2_id),
  FOREIGN KEY (user1_id) REFERENCES users(id),
  FOREIGN KEY (user2_id) REFERENCES users(id)
);

-- Messaging System
CREATE TABLE IF NOT EXISTS conversations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  match_id INT NOT NULL,
  last_message_id INT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (match_id) REFERENCES matches(id)
);

CREATE TABLE IF NOT EXISTS messages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  conversation_id INT NOT NULL,
  sender_id INT NOT NULL,
  message_type ENUM('text', 'image', 'voice', 'location') DEFAULT 'text',
  content TEXT NOT NULL,
  media_url TEXT,
  is_edited BOOLEAN DEFAULT FALSE,
  is_deleted BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (conversation_id) REFERENCES conversations(id),
  FOREIGN KEY (sender_id) REFERENCES users(id)
);

-- Subscription and Payments
CREATE TABLE IF NOT EXISTS subscription_plans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  duration_months INT NOT NULL,
  features TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_subscriptions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  plan_id INT NOT NULL,
  status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
  stripe_subscription_id VARCHAR(255),
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  auto_renew BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
);

CREATE TABLE IF NOT EXISTS payments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  subscription_id INT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  payment_method VARCHAR(50),
  stripe_payment_id VARCHAR(255),
  status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id)
);

-- Verification and Security
CREATE TABLE IF NOT EXISTS identity_verifications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  document_type VARCHAR(50),
  document_url TEXT,
  verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
  verified_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Reports and Moderation
CREATE TABLE IF NOT EXISTS user_reports (
  id INT AUTO_INCREMENT PRIMARY KEY,
  reporter_id INT NOT NULL,
  reported_user_id INT NOT NULL,
  reason VARCHAR(255) NOT NULL,
  description TEXT,
  status ENUM('pending', 'investigating', 'resolved', 'dismissed') DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  resolved_at TIMESTAMP NULL,
  FOREIGN KEY (reporter_id) REFERENCES users(id),
  FOREIGN KEY (reported_user_id) REFERENCES users(id)
);

-- Settings and Preferences
CREATE TABLE IF NOT EXISTS user_settings (
  user_id INT PRIMARY KEY,
  notifications_enabled BOOLEAN DEFAULT TRUE,
  email_notifications BOOLEAN DEFAULT TRUE,
  push_notifications BOOLEAN DEFAULT TRUE,
  hide_profile BOOLEAN DEFAULT FALSE,
  hide_online_status BOOLEAN DEFAULT FALSE,
  hide_last_active BOOLEAN DEFAULT FALSE,
  hide_photos BOOLEAN DEFAULT FALSE,
  language_preference VARCHAR(10) DEFAULT 'en',
  theme_preference VARCHAR(20) DEFAULT 'light',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Activity Tracking
CREATE TABLE IF NOT EXISTS user_activities (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  activity_type VARCHAR(50) NOT NULL,
  activity_data JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Success Stories
CREATE TABLE IF NOT EXISTS success_stories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user1_id INT NOT NULL,
  user2_id INT NOT NULL,
  story_text TEXT,
  wedding_date DATE,
  is_published BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user1_id) REFERENCES users(id),
  FOREIGN KEY (user2_id) REFERENCES users(id)
);