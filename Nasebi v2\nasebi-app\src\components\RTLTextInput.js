import React from 'react';
import { TextInput, StyleSheet, Platform } from 'react-native';
import { useRTL } from '../hooks/useRTL';
import { useLanguage } from '../context/LanguageContext';

const RTLTextInput = ({ style, ...props }) => {
  const { align, isRTL } = useRTL();
  const { language } = useLanguage();
  
  // Determine if the input should be in RTL mode based on content language
  const textInputDirection = language === 'ar' ? 'rtl' : 'ltr';
  
  return (
    <TextInput
      {...props}
      style={[
        styles.input,
        align,
        language === 'ar' ? styles.arabicInput : styles.englishInput,
        style,
      ]}
      textAlign={language === 'ar' ? 'right' : 'left'}
      writingDirection={textInputDirection}
    />
  );
};

const styles = StyleSheet.create({
  input: {
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
  },
  arabicInput: {
    fontFamily: Platform.OS === 'ios' 
      ? 'ArialHebrew' // iOS Arabic font
      : Platform.OS === 'android' 
        ? 'sans-serif' // Android default font that supports Arabic
        : 'Arial', // Web fallback
    lineHeight: 24, // Adjusted for Arabic text
    textAlign: 'right',
  },
  englishInput: {
    fontFamily: Platform.OS === 'ios' 
      ? 'System' 
      : Platform.OS === 'android' 
        ? 'Roboto' 
        : 'Arial',
    lineHeight: 22,
    textAlign: 'left',
  }
});

export default RTLTextInput;