import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { useRTL } from '../../hooks/useRTL';
import api from '../../services/api';
import BlurredProfileImage from '../../components/BlurredProfileImage';

const ChatScreen = ({ route, navigation }) => {
  // Get params from navigation with validation
  const { matchId: rawMatchId, name, photo, userId: rawUserId } = route.params || {};
  const { user } = useAuth();
  const { colors, isDark } = useTheme();
  const { t } = useTranslationFlat();
  const rtl = useRTL();

  // Validate and sanitize the matchId and userId
  const userId = rawUserId || user?.id;

  // Make sure matchId is valid and not 'new_undefined'
  let matchId = rawMatchId;
  if (!matchId || matchId === 'new_undefined') {
    console.error('Invalid matchId received:', rawMatchId);
    // If we have a valid userId, create a new match ID format
    if (userId) {
      matchId = `new_${userId}`;
      console.log('Created new matchId:', matchId);
    } else {
      console.error('Cannot create valid matchId, no userId available');
    }
  }

  // Log the parameters for debugging
  console.log('Chat screen params:', {
    rawMatchId,
    sanitizedMatchId: matchId,
    rawUserId,
    sanitizedUserId: userId,
    name,
    photo,
    currentUser: user?.id
  });

  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const flatListRef = useRef(null);

  useEffect(() => {
    navigation.setOptions({
      headerTitle: () => (
        <View style={[styles.headerTitle, rtl.direction]}>
          <BlurredProfileImage
            source={{ uri: photo }}
            style={styles.headerAvatar}
            showEyeIcon={false}
            blurIntensity={25}
          />
          <Text style={[styles.headerName, { color: colors.text }]}>{name}</Text>
        </View>
      ),
      headerStyle: {
        backgroundColor: colors.background,
      },
      headerTintColor: colors.primary,
    });

    loadMessages();
    const interval = setInterval(loadNewMessages, 5000); // Poll for new messages
    return () => clearInterval(interval);
  }, [matchId, colors]);

  const loadMessages = async () => {
    setLoading(true);
    let retryCount = 0;
    const maxRetries = 3;

    const attemptFetch = async () => {
      try {
        console.log(`Loading messages for matchId ${matchId}... (Attempt ${retryCount + 1}/${maxRetries})`);

        // Check if matchId is valid
        if (!matchId) {
          console.error('Invalid matchId:', matchId);
          setMessages([]);
          return false;
        }

        // Special handling for new matches
        if (matchId && matchId.toString().startsWith('new_')) {
          console.log('This is a new match, no previous messages expected');
          setMessages([]);
          return true;
        }

        // Log the request details for debugging
        console.log('Request URL:', `/api/messages/${matchId}`);

        // Safely make the API call with error handling
        let response;
        try {
          // Make the API call with explicit timeout
          response = await api.get(`/api/messages/${matchId}`, {
            timeout: 10000 // 10 seconds timeout
          });
        } catch (apiError) {
          // Handle API error specifically
          console.error('API call failed:', apiError.message || 'Unknown error');

          // For 404 errors, just set empty messages (this is normal for new matches)
          if (apiError.response && apiError.response.status === 404) {
            console.log('404 error - this is expected for new matches');
            setMessages([]);
            return true;
          }

          // Re-throw to be caught by the outer catch
          throw apiError;
        }

        // If we get here, we have a successful response
        console.log('Messages loaded successfully, count:', Array.isArray(response.data) ? response.data.length : 'Not an array');

        // Ensure we have an array of messages
        let messagesData = [];

        if (Array.isArray(response.data)) {
          messagesData = response.data;
        } else if (response.data && Array.isArray(response.data.messages)) {
          messagesData = response.data.messages;
        } else if (response.data && Array.isArray(response.data.data)) {
          messagesData = response.data.data;
        } else {
          console.warn('Response data does not contain messages array:', response.data);
          // If we can't find messages, set an empty array
          messagesData = [];
        }

        // Process messages to ensure all required properties exist
        const processedMessages = messagesData.map(msg => {
          return {
            ...msg,
            id: msg.id || Math.random().toString(36).substring(2, 9),
            content: msg.content || msg.text || '',
            text: msg.text || msg.content || '',
            timestamp: msg.timestamp || msg.created_at || new Date().toISOString(),
            isMine: msg.isMine || msg.sender_id === user?.id,
            isRead: msg.isRead !== undefined ? msg.isRead : true,
            sender_id: msg.sender_id || (msg.isMine ? user?.id : userId)
          };
        });

        setMessages(processedMessages);

        return true; // Success
      } catch (error) {
        // Log detailed error information
        console.error(`Error loading messages (Attempt ${retryCount + 1}/${maxRetries}):`);
        console.error('Error details:', error.message || 'Unknown error');

        try {
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
            console.error('Response headers:', error.response.headers);
          } else if (error.request) {
            // The request was made but no response was received
            console.error('No response received:', error.request);
          } else {
            // Something happened in setting up the request that triggered an Error
            console.error('Request setup error:', error.message || 'Unknown error');
          }
        } catch (logError) {
          console.error('Error while logging error details:', logError.message || 'Unknown error');
        }

        if (retryCount < maxRetries - 1) {
          retryCount++;
          console.log(`Retrying in 1 second... (${retryCount}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
          return attemptFetch(); // Recursive retry
        }

        // All retries failed - set empty messages array to avoid crashing
        console.log('All retry attempts failed, setting empty messages array');
        setMessages([]);

        // Only show error for non-new matches
        if (!matchId.toString().startsWith('new_')) {
          // Show a more specific error message if available
          let errorMessage = 'Failed to load messages. Please check your connection and try again.';
          try {
            if (error.response && error.response.data && error.response.data.message) {
              errorMessage = error.response.data.message;
            }
          } catch (msgError) {
            console.error('Error getting error message:', msgError.message || 'Unknown error');
          }

          Alert.alert('Error', errorMessage);
        }

        return false; // Failed after all retries
      }
    };

    try {
      await attemptFetch();
    } catch (error) {
      console.error('Unhandled error in loadMessages:', error.message || 'Unknown error');
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  const loadNewMessages = async () => {
    // Skip if we don't have any messages yet or if we're already refreshing
    if (messages.length === 0 || refreshing) return;

    // Skip if matchId is invalid or this is a new match
    if (!matchId || (matchId && matchId.toString().startsWith('new_'))) {
      return;
    }

    let retryCount = 0;
    const maxRetries = 2; // Fewer retries for polling to avoid too many requests

    const attemptFetch = async () => {
      try {
        const lastMessageId = messages[0]?.id;
        if (!lastMessageId) return false; // No messages yet

        // Safely make the API call with error handling
        let response;
        try {
          // Make the API call with explicit timeout
          response = await api.get(`/api/messages/${matchId}/new?after=${lastMessageId}`, {
            timeout: 5000 // 5 seconds timeout for polling
          });
        } catch (apiError) {
          // Handle API error specifically
          console.error('API call failed for new messages:', apiError.message || 'Unknown error');

          // For 404 errors, just return (this is normal for new matches)
          if (apiError.response && apiError.response.status === 404) {
            return true;
          }

          // Re-throw to be caught by the outer catch
          throw apiError;
        }

        // Check if we got any new messages
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          console.log(`Found ${response.data.length} new messages`);
          setMessages(prev => [...response.data, ...prev]);
        }

        return true; // Success
      } catch (error) {
        // Only log the error, don't show alerts for polling
        console.error(`Error loading new messages (Attempt ${retryCount + 1}/${maxRetries}):`);
        console.error('Error details:', error.message || 'Unknown error');

        try {
          if (error.response) {
            console.error('Response status:', error.response.status);
          } else if (error.request) {
            console.error('No response received for new messages');
          } else {
            console.error('Request setup error for new messages:', error.message || 'Unknown error');
          }
        } catch (logError) {
          console.error('Error while logging error details for new messages:', logError.message || 'Unknown error');
        }

        if (retryCount < maxRetries - 1) {
          retryCount++;
          // No delay for polling to avoid blocking the UI
          return attemptFetch(); // Recursive retry
        }

        // All retries failed - silently fail for polling
        return false;
      }
    };

    try {
      await attemptFetch();
    } catch (error) {
      console.error('Unhandled error in loadNewMessages:', error.message || 'Unknown error');
      // Silently fail for polling
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMessages();
    setRefreshing(false);
  };

  const handleSend = async () => {
    if (!inputText.trim()) return;

    setSending(true);
    let retryCount = 0;
    const maxRetries = 3;
    const messageToSend = inputText.trim();

    const attemptSend = async () => {
      try {
        console.log(`Sending message to matchId ${matchId}... (Attempt ${retryCount + 1}/${maxRetries})`);
        console.log('Message content:', messageToSend);

        // Check if matchId is valid
        if (!matchId) {
          console.error('Invalid matchId:', matchId);
          Alert.alert('Error', 'Invalid match ID. Please try again later.');
          return false;
        }

        // Create a temporary message to show immediately
        const tempMessage = {
          id: `temp_${Date.now()}`,
          content: messageToSend,
          messageType: 'text',
          timestamp: new Date().toISOString(),
          isMine: true,
          isRead: false,
          isPending: true
        };

        // Add the temporary message to the UI immediately
        setMessages(prev => [tempMessage, ...prev]);

        // Clear the input field right away for better UX
        setInputText('');

        // Log the full request details for debugging
        console.log('Request URL:', `/api/messages/${matchId}`);
        console.log('Request payload:', { content: messageToSend, messageType: 'text' });

        // Safely make the API call with error handling
        let response;
        try {
          // Make the API call with explicit timeout
          response = await api.post(`/api/messages/${matchId}`, {
            content: messageToSend,
            messageType: 'text',
          }, {
            timeout: 10000 // 10 seconds timeout
          });
        } catch (apiError) {
          // Handle API error specifically
          console.error('API call failed for sending message:', apiError.message || 'Unknown error');

          // Re-throw to be caught by the outer catch
          throw apiError;
        }

        console.log('Message sent successfully, response:', response.data);

        // Process the response data
        let messageData = response.data;

        // Handle different response formats
        if (response.data && response.data.message) {
          messageData = response.data.message;
        } else if (response.data && response.data.data) {
          messageData = response.data.data;
        }

        // Ensure the message has all required properties
        const processedMessage = {
          ...messageData,
          id: messageData.id || tempMessage.id,
          content: messageData.content || messageData.text || tempMessage.content,
          text: messageData.text || messageData.content || tempMessage.content,
          timestamp: messageData.timestamp || messageData.created_at || tempMessage.timestamp,
          isMine: true,
          isRead: messageData.isRead || false,
          sender_id: user?.id
        };

        // Replace the temporary message with the processed one from the server
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id ? processedMessage : msg
          )
        );

        return true; // Success
      } catch (error) {
        // Log detailed error information
        console.error(`Error sending message (Attempt ${retryCount + 1}/${maxRetries}):`);
        console.error('Error details:', error.message || 'Unknown error');

        try {
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
            console.error('Response headers:', error.response.headers);
          } else if (error.request) {
            // The request was made but no response was received
            console.error('No response received:', error.request);
          } else {
            // Something happened in setting up the request that triggered an Error
            console.error('Request setup error:', error.message || 'Unknown error');
          }
        } catch (logError) {
          console.error('Error while logging error details for sending message:', logError.message || 'Unknown error');
        }

        if (retryCount < maxRetries - 1) {
          retryCount++;
          console.log(`Retrying in 1 second... (${retryCount}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
          return attemptSend(); // Recursive retry
        }

        // All retries failed
        console.log('All retry attempts failed');

        // Mark the temporary message as failed
        setMessages(prev =>
          prev.map(msg =>
            msg.isPending ? { ...msg, isFailed: true, isPending: false } : msg
          )
        );

        // Show a more specific error message if available
        let errorMessage = 'Failed to send message. Please check your connection and try again.';
        try {
          if (error.response && error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message;
          }
        } catch (msgError) {
          console.error('Error getting error message for sending:', msgError.message || 'Unknown error');
        }

        Alert.alert('Error', errorMessage);
        return false; // Failed after all retries
      }
    };

    try {
      await attemptSend();
    } catch (error) {
      console.error('Unhandled error in handleSend:', error.message || 'Unknown error');

      // Mark any pending messages as failed
      setMessages(prev =>
        prev.map(msg =>
          msg.isPending ? { ...msg, isFailed: true, isPending: false } : msg
        )
      );
    } finally {
      setSending(false);
    }
  };

  // Removed image picker and location sharing functions to simplify the interface

  const renderMessage = ({ item }) => {
    // Check if the message is from the current user
    const isOwnMessage = item.sender_id === user?.id || item.isMine;

    // Get timestamp from either created_at or timestamp fields
    const timestamp = item.created_at || item.timestamp;

    // Get content from either content or text fields
    const content = item.content || item.text || '';

    // Check if message is pending or failed
    const isPending = item.isPending === true;
    const isFailed = item.isFailed === true;

    // Define message bubble colors based on theme
    const ownMessageBgColor = colors.primary;
    const otherMessageBgColor = isDark ? colors.surface : colors.surface;
    const ownTextColor = '#FFFFFF'; // White text on primary color
    const otherTextColor = colors.text;

    // Adjust message alignment based on RTL and message ownership
    const messageAlignment = rtl.isRTL
      ? isOwnMessage ? 'flex-start' : 'flex-end'
      : isOwnMessage ? 'flex-end' : 'flex-start';

    // Adjust message border radius based on RTL and message ownership
    const messageBorderRadius = rtl.isRTL
      ? isOwnMessage ? { borderTopLeftRadius: 4 } : { borderTopRightRadius: 4 }
      : isOwnMessage ? { borderTopRightRadius: 4 } : { borderTopLeftRadius: 4 };

    return (
      <View style={[
        styles.messageContainer,
        { alignSelf: messageAlignment },
        { backgroundColor: isOwnMessage ? ownMessageBgColor : otherMessageBgColor },
        messageBorderRadius,
        isPending && styles.pendingMessage,
        isFailed && styles.failedMessage,
      ]}>
        <Text style={[
          styles.messageText,
          rtl.align,
          { color: isOwnMessage ? ownTextColor : otherTextColor },
          isPending && { opacity: 0.7 },
          isFailed && { opacity: 0.5, textDecorationLine: 'line-through' }
        ]}>
          {content}
        </Text>

        <View style={[
          styles.messageFooter,
          rtl.direction
        ]}>
          <Text style={[
            styles.messageTime,
            { color: isOwnMessage
              ? 'rgba(255,255,255,0.7)'
              : colors.subtext
            }
          ]}>
            {isPending ? t('sending') : isFailed ? t('failedToSend') : formatMessageTime(timestamp)}
          </Text>

          {isOwnMessage && !isPending && !isFailed && (item.read_at || item.isRead) && (
            <Image
              source={require('../../../assets/icons/Nasebi/messagenotify.png')}
              style={[
                styles.readIcon,
                { tintColor: 'rgba(255,255,255,0.7)' },
                rtl.iconTransform
              ]}
            />
          )}

          {isPending && (
            <ActivityIndicator
              size="small"
              color={isOwnMessage ? "#fff" : colors.primary}
              style={styles.statusIcon}
            />
          )}

          {isFailed && (
            <Image
              source={require('../../../assets/icons/logo.png')}
              style={[
                styles.statusIcon,
                { tintColor: colors.error },
                rtl.iconTransform
              ]}
            />
          )}
        </View>
      </View>
    );
  };

  const formatMessageTime = (timestamp) => {
    if (!timestamp) {
      return '';
    }

    try {
      const date = new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid timestamp:', timestamp);
        return '';
      }

      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return '';
    }
  };

  if (loading && !refreshing && messages.length === 0) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <SafeAreaView
      style={[
        styles.container,
        {
          backgroundColor: colors.background,
          paddingBottom: 10 // Add padding to the bottom of the SafeAreaView
        }
      ]}
      edges={['top', 'left', 'right']} // Don't include bottom edge to avoid navigation bar overlap
    >
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id.toString()}
          inverted
          refreshing={refreshing}
          onRefresh={handleRefresh}
          contentContainerStyle={styles.messagesList}
          showsVerticalScrollIndicator={true}
          initialNumToRender={15}
          windowSize={15}
          maxToRenderPerBatch={10}
          removeClippedSubviews={false}
        />

        <View style={[
          styles.inputContainer,
          rtl.direction,
          {
            backgroundColor: colors.background,
            borderTopColor: colors.border
          }
        ]}>
          <TextInput
            style={[
              styles.input,
              rtl.align,
              {
                backgroundColor: isDark ? colors.surface : colors.surface,
                color: colors.text
              }
            ]}
            value={inputText}
            onChangeText={setInputText}
            placeholder={t('typeMessage')}
            placeholderTextColor={colors.subtext}
            multiline
            maxLength={1000}
            textAlign={rtl.isRTL ? 'right' : 'left'}
            writingDirection={rtl.isRTL ? 'rtl' : 'ltr'}
          />

          <TouchableOpacity
            style={[
              styles.sendButton,
              !inputText.trim() && styles.sendButtonDisabled,
              { backgroundColor: inputText.trim() ? colors.primary : 'transparent' }
            ]}
            onPress={handleSend}
            disabled={!inputText.trim() || sending}
          >
            <Image
              source={require('../../../assets/icons/Nasebi/send.png')}
              style={[
                styles.sendIcon,
                { tintColor: inputText.trim() ? '#FFFFFF' : colors.subtext },
                rtl.iconTransform
              ]}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerTitle: {
    alignItems: 'center',
  },
  headerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginEnd: 8,
    overflow: 'hidden',
  },
  headerName: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messagesList: {
    padding: 15,
    paddingBottom: 100, // Increased padding to account for navigation bar and input area
  },
  messageContainer: {
    maxWidth: '80%',
    marginBottom: 12,
    padding: 12,
    borderRadius: 20,
  },
  // ownMessage and otherMessage styles are now handled dynamically in renderMessage
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
  },
  readIcon: {
    width: 16,
    height: 16,
    marginStart: 4,
  },
  statusIcon: {
    width: 16,
    height: 16,
    marginStart: 4,
  },
  messageFooter: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 4,
  },
  pendingMessage: {
    opacity: 0.8,
  },
  failedMessage: {
    borderWidth: 1,
  },
  inputContainer: {
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
  },
  input: {
    flex: 1,
    marginHorizontal: 10,
    padding: 10,
    borderRadius: 20,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    padding: 12,
    borderRadius: 30,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginStart: 8,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  sendIcon: {
    width: 28,
    height: 28,
  },
});

export default ChatScreen;