import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  Linking,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../context/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Constants from 'expo-constants';
import { useRTL } from '../../hooks/useRTL';


const AboutScreen = () => {
  const { t } = useTranslation();
  const { colors } = useTheme();
  const appVersion = Constants.manifest?.version || '1.0.0';

  const openLink = (url) => {
    Linking.openURL(url).catch(err => {
      console.error('An error occurred', err);
    });
  };

  const InfoItem = ({ icon, title, value, isLink = false, url }) => (
    <View style={[styles.infoItem, { borderBottomColor: colors.border }]}>
      <View style={styles.infoLeft}>
        <Ionicons name={icon} size={22} color={colors.primary} style={styles.infoIcon} />
        <Text style={[styles.infoTitle, { color: colors.text }]}>{title}</Text>
      </View>

      {isLink ? (
        <TouchableOpacity onPress={() => openLink(url)}>
          <Text style={[styles.infoLink, { color: colors.primary }]}>{value}</Text>
        </TouchableOpacity>
      ) : (
        <Text style={[styles.infoValue, { color: colors.subtext }]}>{value}</Text>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.headerContainer}>
          <Image
            source={require('../../../assets/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={[styles.appName, { color: colors.text }]}>{t('common.appName')}</Text>
          <Text style={[styles.appVersion, { color: colors.subtext }]}>
            {t('about.version')} {appVersion}
          </Text>
          <Text style={[styles.appDescription, { color: colors.subtext }]}>
            {t('about.description')}
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={[styles.title, { color: colors.subtext }]}>
            {t('about.info')}
          </Text>

          <View style={[styles.infoContainer, { backgroundColor: colors.card }]}>
            <InfoItem
              icon="globe-outline"
              title={t('about.website')}
              value="nasebi.com"
              isLink={true}
              url="https://nasebi.com"
            />

            <InfoItem
              icon="mail-outline"
              title={t('about.contactUs')}
              value="<EMAIL>"
              isLink={true}
              url="mailto:<EMAIL>"
            />

            <InfoItem
              icon="document-text-outline"
              title={t('about.termsOfService')}
              value={t('about.viewTerms')}
              isLink={true}
              url="https://nasebi.com/terms"
            />

            <InfoItem
              icon="shield-checkmark-outline"
              title={t('about.privacyPolicy')}
              value={t('about.viewPrivacy')}
              isLink={true}
              url="https://nasebi.com/privacy"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.title, { color: colors.subtext }]}>
            {t('about.followUs')}
          </Text>

          <View style={[styles.infoContainer, { backgroundColor: colors.card }]}>
            <InfoItem
              icon="logo-instagram"
              title="Instagram"
              value="@nasebiapp"
              isLink={true}
              url="https://instagram.com/nasebiapp"
            />

            <InfoItem
              icon="logo-twitter"
              title="Twitter"
              value="@nasebiapp"
              isLink={true}
              url="https://twitter.com/nasebiapp"
            />

            <InfoItem
              icon="logo-facebook"
              title="Facebook"
              value="Nasebi"
              isLink={true}
              url="https://facebook.com/nasebiapp"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.title, { color: colors.subtext }]}>
            {t('about.technical')}
          </Text>

          <View style={[styles.infoContainer, { backgroundColor: colors.card }]}>
            <InfoItem
              icon="hardware-chip-outline"
              title={t('about.appId')}
              value={Constants.manifest?.extra?.appIdentifier || 'com.nasebi.app'}
            />

            <InfoItem
              icon="phone-portrait-outline"
              title={t('about.deviceInfo')}
              value={`${Platform.OS} ${Platform.Version}`}
            />
          </View>
        </View>

        <TouchableOpacity
          style={[styles.rateButton, { backgroundColor: colors.card }]}
          onPress={() => {
            const storeUrl = Platform.OS === 'ios'
              ? 'https://apps.apple.com/app/id1234567890'
              : 'https://play.google.com/store/apps/details?id=com.nasebi.app';
            openLink(storeUrl);
          }}
        >
          <Ionicons name="star" size={22} color={colors.primary} style={styles.rateIcon} />
          <Text style={[styles.rateText, { color: colors.text }]}>
            {t('about.rateApp')}
          </Text>
        </TouchableOpacity>

        <Text style={[styles.copyright, { color: colors.subtext }]}>
          © {new Date().getFullYear()} Nasebi. {t('about.allRightsReserved')}
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    padding: 20,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 10,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 10,
  },
  appName: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    marginBottom: 5,
  },
  appVersion: {
    fontSize: 16,
    fontFamily: 'Roboto',
    marginBottom: 15,
  },
  appDescription: {
    fontSize: 14,
    fontFamily: 'Roboto',
    textAlign: 'center',
    lineHeight: 22,
  },
  section: {
    marginBottom: 20,
  },
  title: {
    fontSize: 14,
    fontFamily: 'Roboto',
    marginHorizontal: 5,
    marginBottom: 8,
  },
  infoContainer: {
    borderRadius: 10,
    overflow: 'hidden',
  },
  infoItem: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
  },
  infoLeft: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  infoIcon: {
    marginEnd: 14,
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  infoValue: {
    fontSize: 14,
    fontFamily: 'Roboto',
  },
  infoLink: {
    fontSize: 14,
    fontFamily: 'Roboto-Medium',
  },
  rateButton: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    paddingVertical: 16,
    marginTop: 10,
    marginBottom: 20,
  },
  rateIcon: {
    marginEnd: 10,
  },
  rateText: {
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
  },
  copyend: {
    fontSize: 12,
    fontFamily: 'Roboto',
    textAlign: 'center',
    marginBottom: 20,
  },
});

export default AboutScreen;