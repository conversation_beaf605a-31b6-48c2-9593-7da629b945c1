import { useTranslation } from 'react-i18next';
import { removePeriodAndWordsBefore } from '../utils/textUtils';
import { enSubscription, arSubscription } from '../translations/subscription';

/**
 * Custom translation hook that removes subscription prefix and handles text formatting
 * @returns {Object} - Custom translation functions and i18n instance
 */
export const useCustomTranslation = () => {
  const { t: originalT, i18n } = useTranslation();

  /**
   * Custom translation function that removes subscription prefix
   * @param {string} key - The translation key
   * @param {Object} options - Translation options
   * @returns {string} - The translated text with subscription prefix removed if needed
   */
  const t = (key, options) => {
    // Check if it's a subscription key
    if (key.startsWith('subscription.')) {
      // Extract the key without the prefix
      const subscriptionKey = key.replace('subscription.', '');

      // Get the translation from our custom subscription translations
      const subscriptionTranslations = i18n.language === 'ar' ? arSubscription : enSubscription;

      if (subscriptionTranslations[subscriptionKey]) {
        // If we have a direct translation, use it
        return subscriptionTranslations[subscriptionKey];
      }
    }

    // For all other keys, use the original translation function
    const translatedText = originalT(key, options);

    // If it's a subscription key and in English, remove periods and words before them
    if (key.startsWith('subscription.') && i18n.language === 'en') {
      return removePeriodAndWordsBefore(translatedText);
    }

    return translatedText;
  };

  return { t, i18n };
};

export default useCustomTranslation;
