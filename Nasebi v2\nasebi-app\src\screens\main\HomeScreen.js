
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Animated,
  PanResponder,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../context/AuthContext';
import api from '../../services/api';
import config from '../../config/config';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { useTheme } from '../../context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSearchFilters } from '../../context/SearchFiltersContext';
import { useRTL } from '../../hooks/useRTL';
import BlurredProfileImage from '../../components/BlurredProfileImage';
import ScrollableProfileCard from '../../components/ScrollableProfileCard';
import AsyncStorage from '@react-native-async-storage/async-storage';

const SCREEN_WIDTH = Dimensions.get('window').width;
const SCREEN_HEIGHT = Dimensions.get('window').height;
const SWIPE_THRESHOLD = 120;

const HomeScreen = ({ navigation }) => {
  const { t } = useTranslationFlat();
  const { colors, isDark } = useTheme();
  const { userData, userToken, isAuthenticated, login } = useAuth();
  const { filters } = useSearchFilters();
  const { isRTL } = useRTL();
  const [profiles, setProfiles] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [authChecked, setAuthChecked] = useState(false);

  // Create animated position values
  const position = useRef(new Animated.ValueXY()).current;

  // Adjust rotation direction based on RTL
  const rotation = position.x.interpolate({
    inputRange: [-SCREEN_WIDTH / 2, 0, SCREEN_WIDTH / 2],
    outputRange: isRTL ? ['10deg', '0deg', '-10deg'] : ['-10deg', '0deg', '10deg'],
    extrapolate: 'clamp',
  });

  // Adjust like/dislike indicators based on RTL
  const likeOpacity = position.x.interpolate({
    inputRange: isRTL ? [-SCREEN_WIDTH / 5, 0] : [0, SCREEN_WIDTH / 5],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const dislikeOpacity = position.x.interpolate({
    inputRange: isRTL ? [0, SCREEN_WIDTH / 5] : [-SCREEN_WIDTH / 5, 0],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const nextCardOpacity = position.x.interpolate({
    inputRange: [-SCREEN_WIDTH / 2, 0, SCREEN_WIDTH / 2],
    outputRange: [1, 0.5, 1],
    extrapolate: 'clamp',
  });
  const nextCardScale = position.x.interpolate({
    inputRange: [-SCREEN_WIDTH / 2, 0, SCREEN_WIDTH / 2],
    outputRange: [1, 0.9, 1],
    extrapolate: 'clamp',
  });

  // Check authentication status when component mounts
  useEffect(() => {
    // Clear any stored mock tokens first
    clearStoredAuthData();
    checkAuthAndFetchProfiles();
  }, [isAuthenticated]);

  // Function to clear stored authentication data
  const clearStoredAuthData = async () => {
    try {
      const token = await AsyncStorage.getItem('auth-token');
      if (token && token.includes('mock-token')) {
        console.log('Found stored mock token, clearing it');
        await AsyncStorage.removeItem('auth-token');
        await AsyncStorage.removeItem('user-data');
        // Force a reload of the app
        Alert.alert(
          'Authentication Reset',
          'Mock authentication data has been cleared. Please restart the app.',
          [{
            text: 'OK',
            onPress: () => navigation.navigate('Auth', { screen: 'Login' })
          }]
        );
      }
    } catch (error) {
      console.log('Error clearing stored auth data:', error);
    }
  };

  // Re-fetch profiles when filters change
  useEffect(() => {
    if (isAuthenticated) {
      fetchProfiles();
    }
  }, [filters]);

  // Function to check authentication and fetch profiles
  const checkAuthAndFetchProfiles = async () => {
    setLoading(true);
    setAuthChecked(true);

    if (!isAuthenticated) {
      console.log('User not authenticated, redirecting to login screen');
      // Navigate to login screen if not authenticated
      navigation.navigate('Auth', { screen: 'Login' });
      setLoading(false);
    } else {
      console.log('User is already authenticated, fetching profiles');
      fetchProfiles();
    }
  };

  // Create PanResponder for card swiping
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (event, gesture) => {
        // Update position directly for a smoother feel
        position.setValue({ x: gesture.dx, y: gesture.dy });
      },
      onPanResponderRelease: (event, gesture) => {
        // Calculate velocity for more natural swipe
        // Adjust swipe direction logic for RTL
        const swipeValue = isRTL ? -gesture.dx : gesture.dx;
        const swipeDirection = swipeValue > 0 ? 'right' : 'left';
        const isSwipeIntentional = Math.abs(gesture.dx) > SWIPE_THRESHOLD ||
                                  Math.abs(gesture.vx) > 0.3;

        if (swipeDirection === 'right' && isSwipeIntentional) {
          // Swiped right (like)
          swipeRight();
        } else if (swipeDirection === 'left' && isSwipeIntentional) {
          // Swiped left (dislike)
          swipeLeft();
        } else {
          // Return to center with spring animation for more natural feel
          resetPosition();
        }
      }
    })
  ).current;

  const swipeLeft = () => {
    // Animation for swiping left - adjust direction for RTL
    const xValue = isRTL ? SCREEN_WIDTH + 100 : -SCREEN_WIDTH - 100;
    Animated.spring(position, {
      toValue: { x: xValue, y: 0 },
      friction: 4,
      tension: 40,
      useNativeDriver: true,
    }).start(() => {
      // After animation completes, handle the dislike action
      const currentProfile = profiles[currentIndex];
      if (currentProfile) {
        const profileId = currentProfile.user_id || currentProfile.id;
        console.log(`Disliked profile ${profileId}`);

        // We don't need to send dislikes to the backend, just skip to the next card
        // This is because dislikes are tracked implicitly by not being in the user_likes table
      }

      // Move to next card
      goToNextCard();
    });
  };

  const swipeRight = async () => {
    // Animation for swiping right - adjust direction for RTL
    const xValue = isRTL ? -SCREEN_WIDTH - 100 : SCREEN_WIDTH + 100;
    Animated.spring(position, {
      toValue: { x: xValue, y: 0 },
      friction: 4,
      tension: 40,
      useNativeDriver: true,
    }).start(async () => {
      // After animation completes, handle the like action
      const currentProfile = profiles[currentIndex];
      if (currentProfile) {
        // Use user_id if available, otherwise use id
        const profileId = currentProfile.user_id || currentProfile.id;

        if (!profileId) {
          console.error('Invalid profile ID for like:', profileId);
          Alert.alert(t('error'), t('unexpectedError'));
          goToNextCard();
          return;
        }

        console.log(`Liked profile ${profileId}`);

        try {
          // Send like to backend
          await api.post('/api/likes', {
            liked_user_id: profileId,
            like_type: 'like'
          });

          console.log(`Successfully sent like to backend for profile ${profileId}`);
        } catch (error) {
          console.error('Error sending like to backend:', error);
          // Continue even if the API call fails - we don't want to block the user experience
        }
      }

      // Move to next card
      goToNextCard();
    });
  };

  const resetPosition = () => {
    // Animation to return to center
    Animated.spring(position, {
      toValue: { x: 0, y: 0 },
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const goToNextCard = () => {
    // Check if there are more cards
    if (currentIndex < profiles.length - 1) {
      // Reset position for next card
      position.setValue({ x: 0, y: 0 });
      // Move to next card
      setCurrentIndex(currentIndex + 1);
    } else {
      // No more cards, reset position
      position.setValue({ x: 0, y: 0 });
      // Option to refresh or show empty state
      // For now, we'll just keep the index as is (on the last card)
    }
  };

  const handleLike = async () => {
    if (profiles.length > currentIndex) {
      const currentProfile = profiles[currentIndex];
      if (currentProfile) {
        // Use user_id if available, otherwise use id
        const profileId = currentProfile.user_id || currentProfile.id;

        if (!profileId) {
          console.error('Invalid profile ID for like:', profileId);
          Alert.alert(t('error'), t('unexpectedError'));
          return;
        }

        try {
          // Send like to backend before animation
          await api.post('/api/likes', {
            liked_user_id: profileId,
            like_type: 'like'
          });

          console.log(`Successfully sent like to backend for profile ${profileId}`);
        } catch (error) {
          console.error('Error sending like to backend:', error);
          // Continue even if the API call fails - we don't want to block the user experience
        }
      }

      // Perform the swipe animation
      swipeRight();
    }
  };

  const handleDislike = () => {
    if (profiles.length > currentIndex) {
      // We don't need to send dislikes to the backend
      // Just perform the swipe animation
      swipeLeft();
    }
  };

  const handleSuperLike = async () => {
    if (profiles.length > currentIndex) {
      const currentProfile = profiles[currentIndex];
      try {
        console.log('Current profile for super like:', currentProfile);

        // Make sure we have a valid ID
        if (!currentProfile.id && !currentProfile.user_id) {
          console.error('Profile has no ID, cannot send super like');
          Alert.alert(t('error'), t('unexpectedError'));
          return;
        }

        // Use user_id if available, otherwise use id
        const profileId = currentProfile.user_id || currentProfile.id;

        // Double check that we have a valid ID
        if (!profileId) {
          console.error('Invalid profile ID for super like:', profileId);
          Alert.alert(t('error'), t('unexpectedError'));
          return;
        }

        console.log(`Sending super like to user ID: ${profileId}`);

        // Send super like with message
        await api.post('/api/likes', {
          liked_user_id: profileId,
          like_type: 'super_like'
        });

        // Also send a message with hand emoji
        try {
          const matchId = currentProfile.match_id || `new_${profileId}`;
          console.log(`Sending initial message to match ID: ${matchId}`);

          // Verify matchId is valid before sending
          if (!matchId || matchId === 'new_undefined') {
            console.error('Invalid matchId for message:', matchId);
            throw new Error('Invalid match ID');
          }

          const response = await api.post(`/api/messages/${matchId}`, {
            content: 'مرحباً 👋',
            messageType: 'text',
          }, {
            timeout: 10000 // 10 seconds timeout
          });

          console.log('Initial message sent successfully, response:', response.data);
        } catch (messageError) {
          console.error('Error sending initial message:', messageError);
          // Continue even if message fails - user was still liked
        }

        // Show success message with information about sending "Hi"
        Alert.alert(
          t('messageSent'),
          t('hiMessageWillBeSent')
        );

        // Animate the card away
        const xValue = isRTL ? -SCREEN_WIDTH - 100 : SCREEN_WIDTH + 100;
        Animated.timing(position, {
          toValue: { x: xValue, y: 0 },
          duration: 300,
          useNativeDriver: true,
        }).start(() => {
          // Move to next card
          goToNextCard();
        });
      } catch (error) {
        console.error('Error sending super like:', error);
        console.error('Error details:', error.message);

        if (error.response) {
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
        }

        Alert.alert(t('error'), t('unexpectedError'));
      }
    }
  };

  const handleViewProfile = (profile) => {
    // Get the correct profile ID (user_id or id)
    const profileId = profile.user_id || profile.id;

    // Convert match data to profile format for the ScrollableProfileCard
    const profileData = {
      id: profileId,
      name: profile.name || profile.first_name || 'Unknown',
      age: profile.age || calculateAge(profile.birth_date),
      nationality: profile.nationality,
      location: profile.location || profile.city || '',
      profileImage: profile.images && profile.images.length > 0 ? profile.images[0] :
                   profile.photo ? profile.photo : null,
      bio: profile.bio || profile.about_me || '',
      religious: profile.religious || profile.religion || '',
      education: profile.education || profile.education_level || '',
      height: profile.height || '',
      weight: profile.weight || '',
      prayer: profile.prayer || profile.prayer_frequency || '',
      employment: profile.employment || profile.job_level || '',
      smoker: profile.smoker || false,
      fasting: profile.fasting || profile.fasting_frequency || '',
      skin_color: profile.skin_color || '',
      marriageType: profile.marriageType || profile.marriage_type || '',
      wifeAfterMarriage: profile.wifeAfterMarriage || profile.wife_after_marriage || '',
      workAfterMarriage: profile.workAfterMarriage || profile.work_after_marriage || '',
      financialContribution: profile.financialContribution || profile.financial_contribution || '',
      premaritalItems: profile.premaritalItems || profile.premarital_items || '',
      relocationAfterMarriage: profile.relocationAfterMarriage || profile.relocation_after_marriage || '',
      healthStatus: profile.healthStatus || profile.health_status || '',
      hajjStatus: profile.hajjStatus || profile.hajj_status || '',
      fastingStatus: profile.fastingStatus || profile.fasting_status || '',
      partnerDescription: profile.partnerDescription || profile.partner_description || '',
    };

    setSelectedProfile(profileData);
    setShowProfileModal(true);
  };

  const handleCloseProfile = () => {
    setShowProfileModal(false);
    setSelectedProfile(null);
  };

  const fetchProfiles = async () => {
    setLoading(true);
    let retryCount = 0;
    const maxRetries = 3;

    const attemptFetch = async () => {
      try {
        console.log(`Fetching profiles from backend... (Attempt ${retryCount + 1}/${maxRetries})`);

        // Make sure we have a token
        if (!userToken) {
          console.log('No auth token available, cannot fetch profiles');
          throw new Error('Authentication required');
        }

        console.log('Using auth token:', userToken.substring(0, 10) + '...');

        // First try the correct endpoint
        let response;
        try {
          response = await api.get('/api/profiles');
          console.log('Successfully fetched profiles from /api/profiles');
        } catch (apiError) {
          console.log('Error fetching from /api/profiles:', apiError.message);
          console.log('Trying /api/matches/suggestions...');
          // If that fails, try the alternative endpoint
          response = await api.get('/api/matches/suggestions');
          console.log('Successfully fetched profiles from /api/matches/suggestions');
        }

        // Log the response data structure for debugging
        console.log('Response data structure:',
          Array.isArray(response.data)
            ? `Array with ${response.data.length} items`
            : typeof response.data === 'object' && response.data.profiles
              ? `Object with profiles array (${response.data.profiles.length} items)`
              : typeof response.data
        );

        // Extract profiles from response based on structure
        let profilesData = [];
        if (Array.isArray(response.data)) {
          profilesData = response.data;
        } else if (response.data && Array.isArray(response.data.profiles)) {
          profilesData = response.data.profiles;
        } else if (response.data && Array.isArray(response.data.data)) {
          profilesData = response.data.data;
        }

        if (profilesData.length > 0) {
          console.log('Sample profile structure:', JSON.stringify(profilesData[0], null, 2));
        }

        // Ensure each profile has an interests array and proper image URLs
        let processedData = profilesData.map(profile => {
          // Determine the image URLs based on the response structure
          let imageUrls = [];

          if (profile.photos && profile.photos.length > 0) {
            // If photos is an array of objects with url property
            if (typeof profile.photos[0] === 'object' && profile.photos[0].url) {
              imageUrls = profile.photos.map(photo => photo.url);
            }
            // If photos is an array of strings
            else if (typeof profile.photos[0] === 'string') {
              imageUrls = profile.photos;
            }
          } else if (profile.photo) {
            // Single photo field
            imageUrls = [profile.photo];
          } else if (profile.primary_photo) {
            // Some APIs might return a primary_photo field
            imageUrls = [profile.primary_photo];
          } else if (profile.profile_photo) {
            // Some APIs might return a profile_photo field
            imageUrls = [profile.profile_photo];
          } else if (profile.images && profile.images.length > 0) {
            // If the API already returns an images array
            imageUrls = profile.images;
          } else if (profile.avatar) {
            // Some APIs might return an avatar field
            imageUrls = [profile.avatar];
          }

          // If no images were found, use a default
          if (imageUrls.length === 0) {
            const defaultImage = profile.gender === 'female'
              ? 'https://randomuser.me/api/portraits/women/1.jpg'
              : 'https://randomuser.me/api/portraits/men/1.jpg';
            imageUrls = [defaultImage];
          }

          // Ensure we have a user_id field
          const userId = profile.user_id || profile.id || profile.userId;

          return {
            ...profile,
            user_id: userId,
            id: userId,
            interests: profile.interests || [],
            images: imageUrls,
            // Ensure we have these fields for filtering
            age: profile.age || 30,
            location: profile.location || 'Unknown',
            height: profile.height || 170,
            weight: profile.weight || 70
          };
        });

        console.log(`Processed ${processedData.length} profiles`);

        // Apply filters to the profiles
        processedData = applyFilters(processedData);
        console.log(`After filtering: ${processedData.length} profiles`);

        setProfiles(processedData);
        // Reset current index when filters change
        setCurrentIndex(0);
        return true; // Success
      } catch (error) {
        console.log(`Error fetching profiles (Attempt ${retryCount + 1}/${maxRetries}):`, error);
        console.log('Error details:', error.message);

        if (retryCount < maxRetries - 1) {
          retryCount++;
          console.log(`Retrying in 1 second... (${retryCount}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
          return attemptFetch(); // Recursive retry
        }

        // All retries failed
        console.log('All retry attempts failed, showing empty state');
        setProfiles([]);

        Alert.alert(
          t('common.error'),
          t('home.errorFetchingProfiles'),
          [{ text: t('common.ok') }]
        );
        return false; // Failed after all retries
      }
    };

    try {
      await attemptFetch();
    } finally {
      setLoading(false);
    }
  };

  // Function to calculate age from birth date
  const calculateAge = (birthDate) => {
    if (!birthDate) return '';

    // Handle different date formats
    let dateObj;
    if (typeof birthDate === 'string') {
      // Try to parse the date string
      dateObj = new Date(birthDate);
    } else if (birthDate instanceof Date) {
      dateObj = birthDate;
    } else {
      return '';
    }

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    // Calculate age
    const today = new Date();
    let age = today.getFullYear() - dateObj.getFullYear();
    const monthDiff = today.getMonth() - dateObj.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateObj.getDate())) {
      age--;
    }

    return age.toString();
  };

  // Function to apply filters to profiles
  const applyFilters = (profilesData) => {
    return profilesData.filter(profile => {
      // Basic Information Filters

      // Filter by age range
      if (profile.age < filters.ageRange[0] || profile.age > filters.ageRange[1]) {
        return false;
      }

      // Distance filter removed as requested

      // Filter by profile photo
      if (filters.hasProfilePhoto && (!profile.images || profile.images.length === 0)) {
        return false;
      }

      // Filter by height range if specified
      if (profile.height &&
          (parseInt(profile.height, 10) < filters.height[0] ||
           parseInt(profile.height, 10) > filters.height[1])) {
        return false;
      }

      // Filter by weight range if specified
      if (profile.weight &&
          (parseInt(profile.weight, 10) < filters.weight[0] ||
           parseInt(profile.weight, 10) > filters.weight[1])) {
        return false;
      }

      // Filter by nationality if specified
      if (filters.nationality && filters.nationality !== '' &&
          profile.nationality &&
          !profile.nationality.toLowerCase().includes(filters.nationality.toLowerCase())) {
        return false;
      }

      // Ethnicity filter removed as requested

      // Filter by skin color if specified
      if (filters.skinColor && filters.skinColor !== '' &&
          profile.skin_color &&
          profile.skin_color !== filters.skinColor) {
        return false;
      }

      // Education and Career Filters

      // Filter by education level if specified
      if (filters.educationLevel && filters.educationLevel !== '' &&
          profile.education &&
          profile.education !== filters.educationLevel) {
        return false;
      }

      // Filter by job level if specified
      if (filters.jobLevel && filters.jobLevel !== '' &&
          profile.jobLevel &&
          profile.jobLevel !== filters.jobLevel) {
        return false;
      }

      // Filter by income level if specified
      if (filters.incomeLevel && filters.incomeLevel !== '' &&
          profile.incomeLevel &&
          profile.incomeLevel !== filters.incomeLevel) {
        return false;
      }

      // Religious Attributes Filters

      // Filter by religious level if specified
      if (filters.religiousLevel && filters.religiousLevel !== '' &&
          profile.religiousLevel &&
          profile.religiousLevel !== filters.religiousLevel) {
        return false;
      }

      // Filter by religious sect if specified
      if (filters.religiousSect && filters.religiousSect !== '' &&
          profile.religiousSect &&
          profile.religiousSect !== filters.religiousSect) {
        return false;
      }

      // Filter by prayer level if specified
      if (filters.prayerLevel && filters.prayerLevel !== '' &&
          profile.prayerLevel &&
          profile.prayerLevel !== filters.prayerLevel) {
        return false;
      }

      // Filter by fasting level if specified
      if (filters.fastingLevel && filters.fastingLevel !== '' &&
          profile.fastingLevel &&
          profile.fastingLevel !== filters.fastingLevel) {
        return false;
      }

      // Filter by hajj status if specified
      if (filters.hajjStatus && filters.hajjStatus !== '' &&
          profile.hajjStatus &&
          profile.hajjStatus !== filters.hajjStatus) {
        return false;
      }

      // Lifestyle Filters

      // Filter by marital status if specified
      if (filters.maritalStatus && filters.maritalStatus !== '' &&
          profile.maritalStatus &&
          profile.maritalStatus !== filters.maritalStatus) {
        return false;
      }

      // Filter by has children if specified
      if (filters.hasChildren !== null &&
          profile.hasChildren !== undefined &&
          profile.hasChildren !== filters.hasChildren) {
        return false;
      }

      // Filter by wants children if specified
      if (filters.wantsChildren && filters.wantsChildren !== '' &&
          profile.wantsChildren &&
          profile.wantsChildren !== filters.wantsChildren) {
        return false;
      }

      // Filter by smoking if specified
      if (filters.smoking !== null &&
          profile.smoking !== undefined &&
          profile.smoking !== filters.smoking) {
        return false;
      }

      // Marriage Preferences Filters

      // Filter by marriage readiness if specified
      if (filters.marriageReadiness && filters.marriageReadiness !== '' &&
          profile.marriageReadiness &&
          profile.marriageReadiness !== filters.marriageReadiness) {
        return false;
      }

      // Filter by preferred residence if specified
      if (filters.preferredResidence && filters.preferredResidence !== '' &&
          profile.preferredResidence &&
          profile.preferredResidence !== filters.preferredResidence) {
        return false;
      }

      // Filter by allows wife to work if specified
      if (filters.allowsWifeToWork && filters.allowsWifeToWork !== '' &&
          profile.allowsWifeToWork &&
          profile.allowsWifeToWork !== filters.allowsWifeToWork) {
        return false;
      }

      // Filter by tribal affiliation if specified
      if (filters.tribalAffiliation !== null &&
          profile.tribalAffiliation !== undefined &&
          profile.tribalAffiliation !== filters.tribalAffiliation) {
        return false;
      }

      // Filter by health status if specified
      if (filters.healthStatus && filters.healthStatus !== '' &&
          profile.healthStatus &&
          profile.healthStatus !== filters.healthStatus) {
        return false;
      }

      return true;
    });
  };

  const renderProfiles = () => {
    if (profiles.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="heart-dislike" size={64} color={colors.subtext} />
          <Text style={[styles.emptyText, { color: colors.text }]}>
            {t('home.noMoreProfiles')}
          </Text>
          <TouchableOpacity
            style={[styles.refreshButton, { backgroundColor: colors.primary }]}
            onPress={fetchProfiles}
          >
            <Text style={styles.refreshButtonText}>{t('home.refresh')}</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return profiles.map((profile, index) => {
      // Don't render cards that have been swiped already
      if (index < currentIndex) {
        return null;
      }

      // Render current card with swipe gesture
      if (index === currentIndex) {
        return (
          <Animated.View
            key={profile.user_id || profile.id || index}
            style={[
              styles.card,
              {
                transform: [
                  { rotate: rotation },
                  ...position.getTranslateTransform(),
                ],
                backgroundColor: colors.card,
              },
            ]}
            {...panResponder.panHandlers}
          >
            <TouchableOpacity
              activeOpacity={0.9}
              style={{ width: '100%', height: '100%' }}
              onPress={() => handleViewProfile(profile)}
            >
              <BlurredProfileImage
                source={{ uri: profile.images && profile.images.length > 0 ? profile.images[0] : 'https://randomuser.me/api/portraits/men/1.jpg' }}
                style={styles.image}
                showEyeIcon={true}
                blurIntensity={110}
              />
              <View style={styles.overlay}>
                <LinearGradient
                  colors={['transparent', 'rgba(0,0,0,0.8)']}
                  style={styles.gradient}
                >
                  <View style={styles.profileInfo}>
                    <Text style={styles.name}>{profile.name || 'Unknown'}, {profile.age || '?'}</Text>
                    <Text style={styles.location}>{profile.location || 'Unknown location'}</Text>
                    {profile.profile_details && (
                      <Text style={styles.profileDetails}>{profile.profile_details}</Text>
                    )}

                    <View style={styles.interestsContainer}>
                      {profile.interests && Array.isArray(profile.interests) ?
                        profile.interests.map((interest, i) => (
                          <View key={i} style={styles.interestBadge}>
                            <Text style={styles.interestText}>{interest}</Text>
                          </View>
                        )) : null
                      }
                    </View>

                    {/* Action buttons inside the card */}
                    <View style={styles.cardButtonsContainer}>
                      <TouchableOpacity
                        style={[styles.cardButton, styles.cardDislikeButton]}
                        onPress={(e) => {
                          e.stopPropagation();
                          handleDislike();
                        }}
                      >
                        <Image
                          source={require('../../../assets/icons/dislike.png')}
                          style={styles.dislikeIcon} />
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.cardButton, styles.cardMessageButton]}
                        onPress={(e) => {
                          e.stopPropagation();
                          handleMessageUser();
                        }}
                      >
                        <Image
                          source={require('../../../assets/icons/message.png')}
                          style={styles.cardActionIcon}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.cardButton, styles.cardSuperLikeButton]}
                        onPress={(e) => {
                          e.stopPropagation();
                          handleSuperLike();
                        }}
                      >
                        <Image
                          source={require('../../../assets/icons/Heart.png')}
                          style={styles.cardActionIcon}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.cardButton, styles.cardLikeButton]}
                        onPress={(e) => {
                          e.stopPropagation();
                          handleLike();
                        }}
                      >
                        <Image
                          source={require('../../../assets/icons/like.png')}
                          style={styles.cardActionIcon}
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                </LinearGradient>
              </View>
            </TouchableOpacity>

            {/* <Animated.View style={[styles.likeContainer, { opacity: likeOpacity }]}>
              <Text style={styles.likeText}>{t('like')}</Text>
            </Animated.View>

            <Animated.View style={[styles.dislikeContainer, { opacity: dislikeOpacity }]}>
              <Text style={styles.dislikeText}>{t('nope')}</Text>
            </Animated.View> */}
          </Animated.View>
        );
      }

      // Render next card below current one (for the peek effect)
      if (index === currentIndex + 1) {
        return (
          <Animated.View
            key={`next-${profile.user_id || profile.id || index}`}
            style={[
              styles.card,
              {
                opacity: nextCardOpacity,
                transform: [{ scale: nextCardScale }],
                zIndex: -index,
                backgroundColor: colors.card,
              },
            ]}
          >
            <TouchableOpacity
              activeOpacity={0.9}
              style={{ width: '100%', height: '100%' }}
              onPress={() => {}}
            >
              <BlurredProfileImage
                source={{ uri: profile.images && profile.images.length > 0 ?
                  profile.images[0] : 'https://randomuser.me/api/portraits/men/1.jpg' }}
                style={styles.image}
                showEyeIcon={true}
                blurIntensity={110}
              />
              <View style={styles.overlay}>
                <LinearGradient
                  colors={['transparent', 'rgba(0,0,0,0.8)']}
                  style={styles.gradient}
                >
                  <View style={styles.profileInfo}>
                    <Text style={styles.name}>{profile.name || 'Unknown'}, {profile.age || '?'}</Text>
                    <Text style={styles.location}>{profile.location || 'Unknown location'}</Text>
                    {profile.profile_details && (
                      <Text style={styles.profileDetails}>{profile.profile_details}</Text>
                    )}

                    {/* Action buttons inside the card */}
                    <View style={styles.cardButtonsContainer}>
                      <TouchableOpacity
                        style={[styles.cardButton, styles.cardDislikeButton]}
                        onPress={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Image
                          source={require('../../../assets/icons/dislike.png')}
                          style={styles.dislikeIcon} />
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.cardButton, styles.cardMessageButton]}
                        onPress={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Image
                          source={require('../../../assets/icons/message.png')}
                          style={styles.cardActionIcon}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.cardButton, styles.cardSuperLikeButton]}
                        onPress={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Image
                          source={require('../../../assets/icons/Heart.png')}
                          style={styles.cardActionIcon}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.cardButton, styles.cardLikeButton]}
                        onPress={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Image
                          source={require('../../../assets/icons/like.png')}
                          style={styles.cardActionIcon}
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                </LinearGradient>
              </View>
            </TouchableOpacity>
          </Animated.View>
        );
      }

      // For any other cards, don't render them
      return null;
    }).reverse();
  };

  // Add a new function to handle messaging directly
  const handleMessageUser = () => {
    if (profiles.length > currentIndex) {
      const currentProfile = profiles[currentIndex];

      console.log('Current profile for messaging:', currentProfile);

      // Make sure we have a valid ID
      if (!currentProfile.id && !currentProfile.user_id) {
        console.error('Profile has no ID, cannot open chat');
        Alert.alert(t('common.error'), t('common.unexpectedError'));
        return;
      }

      // Use user_id if available, otherwise use id
      const profileId = currentProfile.user_id || currentProfile.id;

      // Double check that we have a valid ID
      if (!profileId) {
        console.error('Invalid profile ID:', profileId);
        Alert.alert(t('common.error'), t('common.unexpectedError'));
        return;
      }

      console.log('Opening chat with user ID:', profileId);

      // Get a valid photo URL
      let photoUrl = null;
      if (currentProfile.images && currentProfile.images.length > 0) {
        photoUrl = currentProfile.images[0];
      } else if (currentProfile.photos && currentProfile.photos.length > 0) {
        photoUrl = currentProfile.photos[0];
      } else if (currentProfile.photo) {
        photoUrl = currentProfile.photo;
      }

      // Navigate to chat screen with proper parameters
      navigation.navigate('Chat', {
        matchId: currentProfile.match_id || `new_${profileId}`,
        name: currentProfile.name || 'User',
        userId: profileId,
        photo: photoUrl || 'https://randomuser.me/api/portraits/lego/1.jpg'
      });
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            {t('home.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.cardContainer}>{renderProfiles()}</View>

      {/* Profile Modal */}
      <Modal
        visible={showProfileModal}
        animationType="slide"
        onRequestClose={handleCloseProfile}
        statusBarTranslucent
      >
        {selectedProfile && (
          <ScrollableProfileCard
            profile={selectedProfile}
            onClose={handleCloseProfile}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    display: 'none',
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -80,
  },
  card: {
    position: 'absolute',
    width: SCREEN_WIDTH * 0.9,
    height: SCREEN_HEIGHT * 0.8,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    bottom: 0,
    start: 0,
    end: 0,
  },
  gradient: {
    paddingHorizontal: 20,
    paddingVertical: 30,
  },
  profileInfo: {
    justifyContent: 'flex-end',
  },
  name: {
    color: 'white',
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  location: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Roboto',
    marginBottom: 5,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  profileDetails: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Roboto',
    marginBottom: 10,
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  interestsContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  interestBadge: {
    backgroundColor: 'rgba(1105, 1105, 1105, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginEnd: 8,
    marginBottom: 8,
  },
  interestText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Roboto',
  },
  cardButtonsContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'center',
    marginTop: 15,
  },
  cardButton: {
    width: Math.min(60, SCREEN_WIDTH * 0.15),
    height: Math.min(60, SCREEN_WIDTH * 0.15),
    borderRadius: Math.min(30, SCREEN_WIDTH * 0.075),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    margin: 8,
  },
  cardDislikeButton: {
    backgroundColor: '#fff',
    shadowColor: 'transparent',
    elevation: 0,
    opacity: 0.8,
  },
  cardMessageButton: {
    backgroundColor: '#fff',
    shadowColor: 'transparent',
    elevation: 0,
    opacity: 0.8,
  },
  cardLikeButton: {
    backgroundColor: '#fff',
    shadowColor: 'transparent',
    elevation: 0,
    opacity: 0.8,
  },
  cardSuperLikeButton: {
    backgroundColor: '#fff',
    shadowColor: 'transparent',
    elevation: 0,
    opacity: 0.8,
  },
  cardActionIcon: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
  },
  dislikeIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  swipeInstructions: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 10,
    marginTop: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 15,
  },
  swipeInstructionItem: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  swipeInstructionText: {
    fontSize: 12,
    marginHorizontal: 5,
    fontFamily: 'Roboto',
  },
  swipeInstructionDivider: {
    width: 1,
    height: '80%',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },

  likeContainer: {
    position: 'absolute',
    top: 40,
    end: 20,
    transform: [{ rotate: '30deg' }],
    borderWidth: 4,
    borderRadius: 12,
    borderColor: '#4CAF50',
    padding: 10,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  dislikeContainer: {
    position: 'absolute',
    top: 40,
    start: 20,
    transform: [{ rotate: '-30deg' }],
    borderWidth: 4,
    borderRadius: 12,
    borderColor: '#FF3B30',
    padding: 10,
    backgroundColor: 'rgba(1105, 59, 48, 0.1)',
  },
  likeText: {
    color: '#4CAF50',
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    textShadowColor: 'rgba(1105, 1105, 1105, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  dislikeText: {
    color: '#FF3B30',
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    textShadowColor: 'rgba(1105, 1105, 1105, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontFamily: 'Roboto',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  refreshButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 110,
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
  },
  tapHintContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginTop: 10,
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  tapIcon: {
    marginEnd: 5,
  },
  tapHintText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Roboto',
  },
  languageSwitcher: {
    alignSelf: 'flex-end',
  },
});

export default HomeScreen;


