/**
 * These functions were previously used to prevent screenshots and screen recordings,
 * but have been disabled as per requirements.
 */

/**
 * Empty function that previously prevented screenshots
 */
export const preventScreenshots = () => {
  console.log('Screenshot prevention is disabled');
};

/**
 * Empty function that previously allowed screenshots
 */
export const allowScreenshots = () => {
  console.log('Screenshot prevention is already disabled');
};
