import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useTranslationFlat } from '../hooks/useTranslationFlat';
import { useLanguage } from '../context/LanguageContext';
import { useTheme } from '../context/ThemeContext';
import BlurredProfileImage from './BlurredProfileImage';
import { useRTL } from '../hooks/useRTL';


const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ScrollableProfileCard = ({ profile, onClose }) => {
  const rtl = useRTL();

  const { t } = useTranslationFlat();
  const { isRTL } = useLanguage();
  const { colors, isDark } = useTheme();

  const renderProfileAttribute = (label, value, icon) => {
    if (!value) return null;

    return (
      <View style={[
        styles.attributeContainer,
        isRTL && styles.attributeContainerRTL,
        { backgroundColor: isDark ? '#1E1532' : '#f3e6ff' }
      ]}>
        <View style={[
          styles.attributeIconContainer,
          { backgroundColor: colors.primary }
        ]}>
          {icon && <Ionicons name={icon} size={22} color="#fff" />}
        </View>
        <View style={[styles.attributeContent, isRTL && styles.attributeContentRTL]}>
          <Text style={[
            styles.attributeLabel,
            isRTL && styles.textRTL,
            { color: isDark ? '#aaa' : '#666' }
          ]}>{label}</Text>
          <Text style={[
            styles.attributeValue,
            isRTL && styles.textRTL,
            { color: colors.text }
          ]}>{value}</Text>
        </View>
      </View>
    );
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: isDark ? '#120B29' : '#f9f5ff' }
    ]}>
      {/* Small header with user's name */}
      <View style={[
        styles.smallHeader,
        { backgroundColor: isDark ? '#1A1232' : '#f0e6ff', borderBottomColor: isDark ? '#2A2040' : '#e0d0ff' }
      ]}>
        <Text style={[
          styles.headerUserName,
          { color: colors.text }
        ]}>
          {profile.name}
        </Text>
        <TouchableOpacity
          style={[
            styles.closeIconButton,
            { backgroundColor: isDark ? '#2A2040' : '#fff' }
          ]}
          onPress={onClose}
        >
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>

        <View style={styles.profileImageContainer}>
          <BlurredProfileImage
            source={{ uri: profile.profileImage }}
            style={styles.profileImage}
            showEyeIcon={true}
            blurIntensity={110}
            resizeMode="cover"
          />
          <View style={styles.profileImageFooter}>
            <View style={styles.profileInfoContainer}>
              <Text style={[styles.profileName, isRTL && styles.textRTL]}>
                {profile.name}, {profile.age}
              </Text>

              {profile.nationality && (
                <Text style={[styles.nationalityText, isRTL && styles.textRTL]}>
                  {profile.nationality}
                </Text>
              )}

              <View style={[styles.locationContainer, isRTL && styles.locationContainerRTL]}>
                <Ionicons name="location-outline" size={14} color="#fff" />
                <Text style={[styles.locationText, isRTL && styles.locationTextRTL]}>
                  {profile.location}
                </Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.bioSection}>
          <Text style={[
            styles.sectionTitle,
            isRTL && styles.textRTL,
            { color: colors.text }
          ]}>
            {t('aboutMe')}
          </Text>
          <Text style={[
            styles.bioText,
            isRTL && styles.textRTL,
            { color: isDark ? '#e0e0e0' : '#333' }
          ]}>{profile.bio}</Text>
        </View>

        <View style={styles.attributesRow}>
          {profile.religious && (
            <View style={[styles.badge, styles.islamicBadge]}>
              <Ionicons name="moon" size={16} color="#fff" />
              <Text style={[styles.badgeText, isRTL && styles.badgeTextRTL]}>{profile.religious}</Text>
            </View>
          )}
          {profile.education && (
            <View style={[styles.badge, styles.educationBadge]}>
              <Ionicons name="school" size={16} color="#fff" />
              <Text style={[styles.badgeText, isRTL && styles.badgeTextRTL]}>{profile.education}</Text>
            </View>
          )}
          {profile.height && (
            <View style={[styles.badge, styles.heightBadge]}>
              <Ionicons name="resize" size={16} color="#fff" />
              <Text style={[styles.badgeText, isRTL && styles.badgeTextRTL]}>{profile.height}</Text>
            </View>
          )}
        </View>

        <View style={styles.attributesRow}>
          {profile.prayer && (
            <View style={[styles.badge, styles.prayerBadge]}>
              <Ionicons name="time" size={16} color="#fff" />
              <Text style={[styles.badgeText, isRTL && styles.badgeTextRTL]}>{profile.prayer}</Text>
            </View>
          )}
          {profile.employment && (
            <View style={[styles.badge, styles.employmentBadge]}>
              <Ionicons name="briefcase" size={16} color="#fff" />
              <Text style={[styles.badgeText, isRTL && styles.badgeTextRTL]}>{profile.employment}</Text>
            </View>
          )}
          {profile.smoker === false && (
            <View style={[styles.badge, styles.smokerBadge]}>
              <Ionicons name="close-circle" size={16} color="#fff" />
              <Text style={[styles.badgeText, isRTL && styles.badgeTextRTL]}>
                {t('no')}
              </Text>
            </View>
          )}
        </View>

        <View style={[
          styles.sectionDivider,
          { backgroundColor: isDark ? '#303030' : '#e0e0e0' }
        ]} />

        <View style={styles.partnerSection}>
          <Text style={[
            styles.sectionTitle,
            isRTL && styles.textRTL,
            { color: colors.text }
          ]}>
            {t('partnerDescription')}
          </Text>
          <Text style={[
            styles.partnerText,
            isRTL && styles.textRTL,
            { color: isDark ? '#e0e0e0' : '#333' }
          ]}>{profile.partnerDescription}</Text>
        </View>

        <View style={styles.detailsSection}>
          <Text style={[
            styles.sectionTitle,
            isRTL && styles.textRTL,
            { color: colors.text }
          ]}>
            {t('moreAboutMe')}
          </Text>

          {renderProfileAttribute(t('educationLevel'), profile.education, 'school-outline')}
          {renderProfileAttribute(t('jobLevel'), profile.employment, 'briefcase-outline')}
          {renderProfileAttribute(t('preferredResidence'), profile.preferredResidence, 'home-outline')}
          {renderProfileAttribute(t('skinColor'), profile.skin_color ? t(`skinColor_${profile.skin_color}`) : profile.skinTone, 'hand-right-outline')}
          {renderProfileAttribute(t('marriageType'), profile.marriageType, 'people-outline')}
          {renderProfileAttribute(t('smoking'), profile.smoker ? t('yes') : t('no'), 'close-circle-outline')}
          {renderProfileAttribute(t('prayerFrequency'), profile.prayer, 'time-outline')}
          {renderProfileAttribute(t('fastingFrequency'), profile.fasting, 'calendar-outline')}
          {renderProfileAttribute(t('wifeAfterMarriage'), profile.wifeAfterMarriage, 'woman-outline')}
          {renderProfileAttribute(t('workAfterMarriage'), profile.workAfterMarriage, 'briefcase-outline')}
          {renderProfileAttribute(t('financialContribution'), profile.financialContribution, 'cash-outline')}
          {renderProfileAttribute(t('premaritalItems'), profile.premaritalItems, 'list-outline')}
          {renderProfileAttribute(t('relocationAfterMarriage'), profile.relocationAfterMarriage, 'globe-outline')}
          {renderProfileAttribute(t('healthStatus'), profile.healthStatus, 'medical-outline')}
          {renderProfileAttribute(t('hajjStatus'), profile.hajjStatus, 'navigate-outline')}
          {renderProfileAttribute(t('fastingStatus'), profile.fastingStatus, 'calendar-outline')}
        </View>

        {/* Bottom padding for better scrolling experience */}
        <View style={{ height: 30 }} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  smallHeader: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: StatusBar.currentHeight + 10, // Adjust for status bar
    paddingBottom: 15,
    paddingHorizontal: 20,
    position: 'relative',
    borderBottomWidth: 1,
    height: 60, // Fixed height for header
    width: '100%',
    zIndex: 10,
  },
  headerUserName: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    maxWidth: '80%', // Prevent text from overlapping with close button
  },
  closeIconButton: {
    position: 'absolute',
    end: 15,
    top: StatusBar.currentHeight + 10, // Align with header text
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  profileInfoContainer: {
    width: '100%',
  },
  nationalityText: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 5,
  },
  profileImageContainer: {
    height: screenHeight * 0.55, // Increased height to take more screen space
    position: 'relative',
    width: '100%',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  eyeIconContainer: {
    position: 'absolute',
    top: 15,
    start: 15,
    backgroundColor: 'rgba(0,0,0,0.4)',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  profileImageFooter: {
    position: 'absolute',
    bottom: 0,
    start: 0,
    end: 0,
    backgroundColor: 'rgba(0,0,0,0.7)', // Darker background for better readability
    padding: 20,
  },
  profileName: {
    color: '#fff',
    fontSize: 24, // Larger font size
    fontWeight: 'bold',
    marginBottom: 5,
  },
  locationContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  locationContainerRTL: {
    flexDirection: rtl.isRTL ? 'row' : 'row-reverse',
  },
  locationText: {
    color: '#fff',
    fontSize: 16, // Larger font size
    marginStart: 5,
  },
  locationTextRTL: {
    marginStart: 0,
    marginEnd: 5,
  },
  bioSection: {
    padding: 20,
    marginTop: 15,
    borderRadius: 15,
    marginHorizontal: 10,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: rtl.isRTL ? 'right' : 'left',
  },
  bioText: {
    fontSize: 16,
    lineHeight: 24,
  },
  textRTL: {
    textAlign: rtl.isRTL ? 'left' : 'right',
  },
  attributesRow: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 15,
    marginBottom: 20,
    justifyContent: 'flex-start',
    marginHorizontal: 5,
  },
  badge: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    marginEnd: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  islamicBadge: {
    backgroundColor: '#8A2BE2',
  },
  educationBadge: {
    backgroundColor: '#4682B4',
  },
  heightBadge: {
    backgroundColor: '#708090',
  },
  prayerBadge: {
    backgroundColor: '#6A5ACD',
  },
  employmentBadge: {
    backgroundColor: '#DB7093',
  },
  smokerBadge: {
    backgroundColor: '#696969',
  },
  badgeText: {
    color: '#fff',
    fontSize: 15,
    marginStart: 8,
    fontWeight: '500',
  },
  badgeTextRTL: {
    marginStart: 0,
    marginEnd: 8,
  },
  sectionDivider: {
    height: 1,
    marginVertical: 20,
    marginHorizontal: 20,
  },
  partnerSection: {
    padding: 20,
    marginTop: 10,
    borderRadius: 15,
    marginHorizontal: 10,
  },
  partnerText: {
    fontSize: 16,
    lineHeight: 24,
  },
  detailsSection: {
    padding: 20,
    marginTop: 10,
    borderRadius: 15,
    marginHorizontal: 10,
  },
  attributeContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginBottom: 15,
    padding: 16,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 2,
    marginHorizontal: 5,
  },
  attributeContainerRTL: {
    flexDirection: rtl.isRTL ? 'row' : 'row-reverse',
  },
  attributeIconContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
    alignItems: 'center',
    justifyContent: 'center',
    marginEnd: 15,
  },
  attributeContent: {
    flex: 1,
    alignItems: 'flex-start',
  },
  attributeContentRTL: {
    alignItems: 'flex-end',
    marginEnd: 15,
    marginStart: 0,
  },
  attributeLabel: {
    fontSize: 14,
    marginBottom: 6,
    opacity: 0.8,
  },
  attributeValue: {
    fontSize: 17,
    fontWeight: '600',
  }
});

export default ScrollableProfileCard;