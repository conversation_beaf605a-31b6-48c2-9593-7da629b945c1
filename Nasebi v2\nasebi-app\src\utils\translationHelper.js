/**
 * Helper function to convert dotted translation keys to flat keys
 * This is a temporary utility to help with the transition from dotted keys to flat keys
 * 
 * Example: 
 * - Old: t('common.appName')
 * - New: t('appName')
 * 
 * @param {string} key - The translation key
 * @returns {string} - The flattened key
 */
export const flattenKey = (key) => {
  if (!key) return '';
  
  // If the key contains a dot, take the last part
  if (key.includes('.')) {
    const parts = key.split('.');
    return parts[parts.length - 1];
  }
  
  // Otherwise, return the key as is
  return key;
};

/**
 * Wrapper for the translation function to handle both old and new keys
 * 
 * @param {Function} t - The original translation function
 * @param {string} key - The translation key
 * @param {Object} options - Translation options
 * @returns {string} - The translated string
 */
export const translateWithFallback = (t, key, options = {}) => {
  // Try with the original key first
  let result = t(key, options);
  
  // If the result is the same as the key, it means the translation wasn't found
  // Try with the flattened key
  if (result === key) {
    const flatKey = flattenKey(key);
    result = t(flatKey, options);
  }
  
  return result;
};
