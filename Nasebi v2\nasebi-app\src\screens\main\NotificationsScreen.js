import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNotifications } from '../../context/NotificationContext';
import { useTheme } from '../../context/ThemeContext';
import { useLanguage } from '../../context/LanguageContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { useRTL } from '../../hooks/useRTL';


const NotificationsScreen = ({ navigation }) => {
  const rtl = useRTL();

  const {
    notifications,
    unreadCount,
    loading,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    handleNotificationPress
  } = useNotifications();
  const { colors } = useTheme();
  const { isRTL } = useLanguage();
  const { t } = useTranslation();
  const [filterActive, setFilterActive] = useState(false);
  const [notificationTypes, setNotificationTypes] = useState({
    like: true,
    message: true,
    visit: true,
    other: true
  });

  useEffect(() => {
    // Refresh notifications when the screen comes into focus
    const unsubscribe = navigation.addListener('focus', () => {
      fetchNotifications();
    });

    return unsubscribe;
  }, [navigation, fetchNotifications]);

  const toggleFilter = (type) => {
    setNotificationTypes(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  const filteredNotifications = filterActive
    ? notifications.filter(item => notificationTypes[item.type] ||
      (item.type !== 'like' && item.type !== 'message' && item.type !== 'visit' && notificationTypes.other))
    : notifications;

  const renderNotificationItem = ({ item }) => {
    // Define which icon to use based on the notification type
    let iconName;
    let iconColor;

    switch (item.type) {
      case 'like':
        iconName = 'heart';
        iconColor = '#FF6B6B';
        break;
      case 'message':
        iconName = 'chatbubble';
        iconColor = '#4CAF50';
        break;
      case 'visit':
        iconName = 'eye';
        iconColor = '#2196F3';
        break;
      default:
        iconName = 'notifications';
        iconColor = '#FFC107';
    }

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          { backgroundColor: item.read ? colors.card : colors.primary + '15' },
          { borderStartColor: item.read ? colors.border : colors.primary,
            borderEndColor: item.read ? colors.border : colors.primary,
            borderStartWidth: isRTL ? 0 : 4,
            borderEndWidth: isRTL ? 4 : 0 }
        ]}
        onPress={() => handleNotificationPress(item)}
      >
        <View style={styles.iconContainer}>
          <Ionicons name={iconName} size={24} color={iconColor} />
        </View>
        <View style={styles.notificationContent}>
          <Text style={[styles.notificationText, { color: colors.text }]}>
            {item.message}
          </Text>
          <Text style={[styles.notificationTime, { color: colors.subtext }]}>
            {item.time}
          </Text>
        </View>
        <View style={[styles.notificationActions, rtl.direction]}>
          {!item.read && (
            <View style={[styles.unreadDot, { backgroundColor: colors.primary }]} />
          )}
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => deleteNotification(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color="#FF6B6B" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="notifications-off-outline" size={80} color={colors.subtext} />
      <Text style={[styles.emptyText, { color: colors.subtext }]}>
        {t('notifications.noNotifications')}
      </Text>
    </View>
  );

  const renderFilterSection = () => (
    <View style={[styles.filterContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <View style={styles.filterHeader}>
        <Text style={[styles.filterTitle, { color: colors.text }]}>{t('تصفية الإشعارات')}</Text>
        <Switch
          value={filterActive}
          onValueChange={setFilterActive}
          trackColor={{ false: colors.border, true: colors.primary }}
          thumbColor={filterActive ? colors.background : '#f4f3f4'}
        />
      </View>

      {filterActive && (
        <View style={[styles.filterOptions, rtl.direction]}>
          <TouchableOpacity
            style={styles.filterOption}
            onPress={() => toggleFilter('like')}
          >
            <View style={[styles.filterOptionContent, rtl.direction]}>
              <Ionicons name="heart" size={20} color="#FF6B6B" />
              <Text style={[styles.filterText, { color: colors.text }]}>{t('الإعجابات')}</Text>
            </View>
            <View style={styles.checkboxContainer}>
              <View style={[
                styles.checkbox,
                notificationTypes.like ? { backgroundColor: colors.primary, borderColor: colors.primary } : { borderColor: colors.border }
              ]}>
                {notificationTypes.like && <Ionicons name="checkmark" size={16} color="white" />}
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.filterOption}
            onPress={() => toggleFilter('message')}
          >
            <View style={[styles.filterOptionContent, rtl.direction]}>
              <Ionicons name="chatbubble" size={20} color="#4CAF50" />
              <Text style={[styles.filterText, { color: colors.text }]}>{t('الرسائل')}</Text>
            </View>
            <View style={styles.checkboxContainer}>
              <View style={[
                styles.checkbox,
                notificationTypes.message ? { backgroundColor: colors.primary, borderColor: colors.primary } : { borderColor: colors.border }
              ]}>
                {notificationTypes.message && <Ionicons name="checkmark" size={16} color="white" />}
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.filterOption}
            onPress={() => toggleFilter('visit')}
          >
            <View style={[styles.filterOptionContent, rtl.direction]}>
              <Ionicons name="eye" size={20} color="#2196F3" />
              <Text style={[styles.filterText, { color: colors.text }]}>{t('الزيارات')}</Text>
            </View>
            <View style={styles.checkboxContainer}>
              <View style={[
                styles.checkbox,
                notificationTypes.visit ? { backgroundColor: colors.primary, borderColor: colors.primary } : { borderColor: colors.border }
              ]}>
                {notificationTypes.visit && <Ionicons name="checkmark" size={16} color="white" />}
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.filterOption}
            onPress={() => toggleFilter('other')}
          >
            <View style={[styles.filterOptionContent, rtl.direction]}>
              <Ionicons name="notifications" size={20} color="#FFC107" />
              <Text style={[styles.filterText, { color: colors.text }]}>{t('أخرى')}</Text>
            </View>
            <View style={styles.checkboxContainer}>
              <View style={[
                styles.checkbox,
                notificationTypes.other ? { backgroundColor: colors.primary, borderColor: colors.primary } : { borderColor: colors.border }
              ]}>
                {notificationTypes.other && <Ionicons name="checkmark" size={16} color="white" />}
              </View>
            </View>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.mainContent}>
        {/* Top Filter Section */}
        <View style={styles.topFilterSection}>
          {renderFilterSection()}
        </View>

        {/* Mark All as Read Button */}
        {unreadCount > 0 && (
          <TouchableOpacity
            style={[styles.markAllButton, { borderBottomColor: colors.border }]}
            onPress={markAllAsRead}
          >
            <View style={[styles.markAllButtonContent, rtl.direction]}>
              <Ionicons
                name="checkmark-done-outline"
                size={20}
                color={colors.primary}
                style={{ marginEnd: 8 }}
              />
              <Text style={[styles.markAllText, { color: colors.primary }]}>
                {t('notifications.markAllAsRead')}
              </Text>
            </View>
          </TouchableOpacity>
        )}

        {/* Notifications List */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <FlatList
            data={filteredNotifications}
            renderItem={renderNotificationItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.notificationsList}
            ListEmptyComponent={renderEmptyComponent}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContent: {
    flex: 1,
    flexDirection: 'column',
  },
  topFilterSection: {
    width: '100%',
  },
  markAllButton: {
    padding: 15,
    alignItems: 'center',
    borderBottomWidth: 1,
  },
  markAllButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  markAllText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  notificationActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    padding: 5,
    marginStart: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationsList: {
    padding: 10,
    flexGrow: 1,
  },
  notificationItem: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    borderStartWidth: 4,
  },
  iconContainer: {
    marginEnd: 15,
  },
  notificationContent: {
    flex: 1,
  },
  notificationText: {
    fontSize: 15,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 13,
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginStart: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 15,
    textAlign: 'center',
  },
  filterContainer: {
    margin: 10,
    borderRadius: 8,
    padding: 10,
    borderWidth: 1,
  },
  filterHeader: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  filterOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  filterOption: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    paddingHorizontal: 8,
    marginVertical: 2,
    marginHorizontal: 2,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: '22%',
    maxWidth: '48%',
  },
  filterOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterText: {
    marginStart: 5,
    fontSize: 13,
  },
  checkboxContainer: {
    marginStart: 5,
  },
  checkbox: {
    width: 18,
    height: 18,
    borderWidth: 2,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NotificationsScreen;