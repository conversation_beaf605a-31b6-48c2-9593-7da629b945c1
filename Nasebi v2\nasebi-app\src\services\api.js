import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import config from '../config/config';

// DIRECT SOLUTION: Use the actual IP address of your computer
// This ensures consistent access from both emulators and physical devices
const DIRECT_IP = '*************'; // Your actual IP address from ipconfig
let API_URL = `http://${DIRECT_IP}:3000`;

// Log the configured API URL and platform
console.log(`Platform: ${Platform.OS}`);
console.log(`Using direct IP address: ${DIRECT_IP}`);
console.log(`API URL configured as: ${API_URL}`);

// Use the mock data flag from config
const useMockData = config.useMockData;
console.log(`Mock data mode: ${useMockData ? 'ENABLED' : 'DISABLED'}`);

// Create axios instance with direct IP
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: config.apiTimeout,
});

// Create a secondary instance with the same direct IP for fallback
// This ensures consistent behavior across platforms
const fallbackApi = axios.create({
  baseURL: API_URL, // Use the same direct IP
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 60000, // 60 seconds for fallback attempts
});

// Add a request interceptor to include auth token in all requests
api.interceptors.request.use(
  async (config) => {
    try {
      // Try to get the token from AsyncStorage
      const token = await AsyncStorage.getItem('auth-token');

      // If token exists, add it to the headers
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('Added auth token to request:', config.url);
      } else {
        console.log('No auth token available for request:', config.url);
        // Try to get a new token if we're not already trying to login or register
        if (!config.url.includes('/api/auth/login') && !config.url.includes('/api/auth/register')) {
          console.log('Attempting to refresh authentication...');
          // You could implement token refresh logic here if needed
        }
      }
    } catch (error) {
      console.error('Error adding auth token to request:', error);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// No more mock data - using real API only
console.log('Using real API data only - all mock data removed');

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    console.log(`API Success: ${response.config.method.toUpperCase()} ${response.config.url}`);
    return response;
  },
  async (error) => {
    // Default error message
    let errorMessage = 'An unexpected error occurred';

    // Log detailed error information for debugging
    console.log('API Error Details:');
    console.log(`- Request: ${error.config?.method?.toUpperCase() || 'UNKNOWN'} ${error.config?.url || 'UNKNOWN'}`);
    console.log(`- Error Message: ${error.message}`);

    // Handle connection errors
    if (!error.response) {
      errorMessage = 'Network error - please check your internet connection';
      console.log('Network error occurred');
      console.log(`- API URL: ${API_URL}`);
      console.log(`- Full Error:`, error);

      // DIRECT SOLUTION: Simplified retry logic with direct IP
      if ((error.message === 'Network Error' || error.code === 'ECONNABORTED') && error.config) {
        const originalRequest = error.config;

        // Try with increased timeout
        console.log('Network error detected, retrying with increased timeout');

        try {
          // Create a new config with increased timeout and direct IP
          const retryConfig = {
            ...originalRequest,
            baseURL: `http://${DIRECT_IP}:3000`,
            timeout: 60000 // 60 seconds
          };

          console.log(`Retrying request to: http://${DIRECT_IP}:3000${originalRequest.url}`);
          return await axios(retryConfig);
        } catch (retryError) {
          console.log('Retry failed:', retryError.message);

          // Log detailed error information
          console.log('Detailed error information:');
          console.log('- Original URL:', originalRequest.url);
          console.log('- Method:', originalRequest.method);
          console.log('- Headers:', JSON.stringify(originalRequest.headers));
          console.log('- Error:', retryError.message);

          // If using a physical device, make sure it's on the same network as your computer
          console.log('TROUBLESHOOTING TIPS:');
          console.log('1. Make sure your device is on the same network as your computer');
          console.log('2. Check if your firewall is blocking connections to port 3000');
          console.log('3. Try restarting the backend server');
        }
      }
    } else {
      // Server returned an error response
      console.log(`- Status Code: ${error.response.status}`);
      console.log(`- Response Data:`, error.response.data);

      if (error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else {
        // Map HTTP status codes to readable messages
        switch (error.response.status) {
          case 400:
            errorMessage = 'Bad request - please check your input';
            break;
          case 401:
            errorMessage = 'Authentication failed - please log in again';
            // Could trigger a logout action here
            break;
          case 403:
            errorMessage = 'You do not have permission to access this resource';
            break;
          case 404:
            errorMessage = 'The requested resource was not found';
            break;
          case 500:
            errorMessage = 'Server error - please try again later';
            break;
        }
      }
    }

    // Attach a readable error message
    error.userMessage = errorMessage;
    return Promise.reject(error);
  }
);

// Create a wrapper for the API that adds error handling and logging
const apiWrapper = {
  get: async (url, config = {}) => {
    try {
      console.log(`API GET request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to GET request: ${url}`);
        }
      }

      const response = await api.get(url, config);
      return response;
    } catch (error) {
      console.error(`API Error in GET ${url}:`, error.message);

      // Special handling for 404 errors on message endpoints
      if (error.response && error.response.status === 404 && url.startsWith('/api/messages/')) {
        console.log('Message endpoint 404, returning empty array');
        return { data: [] };
      }

      throw error;
    }
  },
  post: async (url, data, config = {}) => {
    try {
      console.log(`API POST request to: ${url}`);
      console.log('Request data:', data);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to POST request: ${url}`);
        }
      }

      const response = await api.post(url, data, config);
      return response;
    } catch (error) {
      console.error(`API Error in POST ${url}:`, error.message);
      throw error;
    }
  },
  put: async (url, data, config = {}) => {
    try {
      console.log(`API PUT request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to PUT request: ${url}`);
        }
      }

      const response = await api.put(url, data, config);
      return response;
    } catch (error) {
      console.error(`API Error in PUT ${url}:`, error.message);

      // Enhanced error logging
      console.log('API Error Details:');
      console.log(`- Request: PUT ${url}`);
      console.log(`- Error Message: ${error.message}`);

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.log(`- Status: ${error.response.status}`);
        console.log(`- Response Data:`, error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.log('Network error occurred');
        console.log(`- API URL: ${api.defaults.baseURL}`);
      }

      throw error;
    }
  },
  delete: async (url, config = {}) => {
    try {
      console.log(`API DELETE request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to DELETE request: ${url}`);
        }
      }

      const response = await api.delete(url, config);
      return response;
    } catch (error) {
      console.error(`API Error in DELETE ${url}:`, error.message);
      throw error;
    }
  },
  patch: async (url, data, config = {}) => {
    try {
      console.log(`API PATCH request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to PATCH request: ${url}`);
        }
      }

      const response = await api.patch(url, data, config);
      return response;
    } catch (error) {
      console.error(`API Error in PATCH ${url}:`, error.message);
      throw error;
    }
  }
};

// Export the API wrapper instead of the raw API
// This ensures we're using the real API with proper error handling
export default apiWrapper;