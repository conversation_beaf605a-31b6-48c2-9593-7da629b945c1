import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import config from '../config/config';

// IMPORTANT: Replace this with your actual IP address
// This is the IP address of your computer on the network
const DIRECT_IP = '************'; // Replace with your actual IP address

// Use direct IP address for all platforms
// This ensures consistent access from both emulators and physical devices
const API_URL = `http://${DIRECT_IP}:3000`;

console.log(`Using direct IP address: ${DIRECT_IP}`);
console.log(`API URL configured as: ${API_URL}`);

// Create axios instance with direct IP
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: config.apiTimeout,
});

// Add a request interceptor to include auth token in all requests
api.interceptors.request.use(
  async (config) => {
    try {
      // Try to get the token from AsyncStorage
      const token = await AsyncStorage.getItem('auth-token');

      // If token exists, add it to the headers
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('Added auth token to request:', config.url);
      } else {
        console.log('No auth token available for request:', config.url);
      }
    } catch (error) {
      console.error('Error adding auth token to request:', error);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    console.log(`API Success: ${response.config.method.toUpperCase()} ${response.config.url}`);
    return response;
  },
  (error) => {
    // Default error message
    let errorMessage = 'An unexpected error occurred';

    // Log detailed error information for debugging
    console.log('API Error Details:');
    console.log(`- Request: ${error.config?.method?.toUpperCase() || 'UNKNOWN'} ${error.config?.url || 'UNKNOWN'}`);
    console.log(`- Error Message: ${error.message}`);

    // Handle connection errors
    if (!error.response) {
      errorMessage = 'Network error - please check your internet connection';
      console.log('Network error occurred');
      console.log(`- API URL: ${API_URL}`);
      console.log(`- Full Error:`, error);
    } else {
      // Server returned an error response
      console.log(`- Status Code: ${error.response.status}`);
      console.log(`- Response Data:`, error.response.data);

      if (error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else {
        // Map HTTP status codes to readable messages
        switch (error.response.status) {
          case 400:
            errorMessage = 'Bad request - please check your input';
            break;
          case 401:
            errorMessage = 'Authentication failed - please log in again';
            break;
          case 403:
            errorMessage = 'You do not have permission to access this resource';
            break;
          case 404:
            errorMessage = 'The requested resource was not found';
            break;
          case 500:
            errorMessage = 'Server error - please try again later';
            break;
        }
      }
    }

    // Attach a readable error message
    error.userMessage = errorMessage;
    return Promise.reject(error);
  }
);

// Create a wrapper for the API that adds error handling and logging
const apiWrapper = {
  get: async (url, config = {}) => {
    try {
      console.log(`API GET request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to GET request: ${url}`);
        }
      }

      const response = await api.get(url, config);
      return response;
    } catch (error) {
      console.error(`API Error in GET ${url}:`, error.message);
      throw error;
    }
  },
  post: async (url, data, config = {}) => {
    try {
      console.log(`API POST request to: ${url}`);
      console.log('Request data:', JSON.stringify(data, null, 2));

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to POST request: ${url}`);
        }
      }

      const response = await api.post(url, data, config);
      return response;
    } catch (error) {
      console.error(`API Error in POST ${url}:`, error.message);
      throw error;
    }
  },
  put: async (url, data, config = {}) => {
    try {
      console.log(`API PUT request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to PUT request: ${url}`);
        }
      }

      const response = await api.put(url, data, config);
      return response;
    } catch (error) {
      console.error(`API Error in PUT ${url}:`, error.message);
      throw error;
    }
  },
  delete: async (url, config = {}) => {
    try {
      console.log(`API DELETE request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to DELETE request: ${url}`);
        }
      }

      const response = await api.delete(url, config);
      return response;
    } catch (error) {
      console.error(`API Error in DELETE ${url}:`, error.message);
      throw error;
    }
  },
  patch: async (url, data, config = {}) => {
    try {
      console.log(`API PATCH request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to PATCH request: ${url}`);
        }
      }

      const response = await api.patch(url, data, config);
      return response;
    } catch (error) {
      console.error(`API Error in PATCH ${url}:`, error.message);
      throw error;
    }
  }
};

// Export the API wrapper instead of the raw API
export default apiWrapper;
