import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Switch,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform
} from 'react-native';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { useTheme } from '../../context/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRTL } from '../../hooks/useRTL';


const PrivacyScreen = () => {
  const { t } = useTranslationFlat();
  const { colors } = useTheme();
  const rtl = useRTL();

  const [privacySettings, setPrivacySettings] = useState({
    showOnlineStatus: true,
    showLastSeen: true,
    showProfileToEveryone: true,
    allowLocationAccess: true,
    allowCameraAccess: true,
    allowContactsAccess: false,
    incognitoMode: false,
  });

  const toggleSetting = (setting) => {
    if (setting === 'incognitoMode' && !privacySettings.incognitoMode) {
      Alert.alert(
        t('incognitoMode'),
        t(''),
        [
          {
            text: t('cancel'),
            style: 'cancel',
          },
          {
            text: t('enable'),
            onPress: () => {
              setPrivacySettings(prev => ({
                ...prev,
                incognitoMode: true,
              }));
            },
          },
        ]
      );
    } else {
      setPrivacySettings(prev => ({
        ...prev,
        [setting]: !prev[setting]
      }));
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      t('deleteAccountTitle'),
      t('deleteAccountConfirm'),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('deleteAccount'),
          style: 'destructive',
          onPress: () => {
            // Would typically call API to delete account
            Alert.alert(
              t('deleteAccountSuccess'),
              t('accountDeletedDescription')
            );
          },
        },
      ]
    );
  };

  const SettingSwitch = ({ title, description, value, onToggle }) => (
    <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
      <View style={styles.settingTextContainer}>
        <Text style={[styles.settingTitle, { color: colors.text }]}>{title}</Text>
        {description && (
          <Text style={[styles.settingDescription, { color: colors.subtext }]}>
            {description}
          </Text>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: '#767577', true: colors.primary }}
        thumbColor={Platform.OS === 'ios' ? '#FFFFFF' : value ? '#FFFFFF' : '#f4f3f4'}
        ios_backgroundColor="#3e3e3e"
      />
    </View>
  );

  const PrivacyOption = ({ icon, title, onPress }) => (
    <TouchableOpacity
      style={[styles.settingItem, { borderBottomColor: colors.border }]}
      onPress={onPress}
    >
      <View style={styles.optionLeft}>
        <Ionicons name={icon} size={22} color={colors.primary} style={styles.optionIcon} />
        <Text style={[styles.settingTitle, { color: colors.text }]}>{title}</Text>
      </View>
      <Ionicons
        name="chevron-forward"
        size={22}
        color={colors.subtext}
      />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView>
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.subtext }]}>
            {t('visibilitySettings')}
          </Text>

          <View style={[styles.settingsContainer, { backgroundColor: colors.card }]}>
            <SettingSwitch
              title={t('hideOnlineStatus')}
              description={t('')}
              value={privacySettings.showOnlineStatus}
              onToggle={() => toggleSetting('showOnlineStatus')}
            />

            <SettingSwitch
              title={t('hideLastSeen')}
              description={t('')}
              value={privacySettings.showLastSeen}
              onToggle={() => toggleSetting('showLastSeen')}
            />

            <SettingSwitch
              title={t('hideProfile')}
              description={t('')}
              value={privacySettings.showProfileToEveryone}
              onToggle={() => toggleSetting('showProfileToEveryone')}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.subtext }]}>
            {t('appPermissions')}
          </Text>

          <View style={[styles.settingsContainer, { backgroundColor: colors.card }]}>
            <SettingSwitch
              title={t('allowLocationAccess')}
              value={privacySettings.allowLocationAccess}
              onToggle={() => toggleSetting('allowLocationAccess')}
            />

            <SettingSwitch
              title={t('allowCameraAccess')}
              value={privacySettings.allowCameraAccess}
              onToggle={() => toggleSetting('allowCameraAccess')}
            />

            <SettingSwitch
              title={t('allowContactsAccess')}
              value={privacySettings.allowContactsAccess}
              onToggle={() => toggleSetting('allowContactsAccess')}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.subtext }]}>
            {t('advancedPrivacy')}
          </Text>

          <View style={[styles.settingsContainer, { backgroundColor: colors.card }]}>
            <SettingSwitch
              title={t('incognitoMode')}
              description={t('')}
              value={privacySettings.incognitoMode}
              onToggle={() => toggleSetting('incognitoMode')}
            />

            <PrivacyOption
              icon="shield-checkmark-outline"
              title={t('blockList')}
              onPress={() => {
                // Navigate to blocked users screen
              }}
            />

            <PrivacyOption
              icon="document-text-outline"
              title={t('privacyPolicy')}
              onPress={() => {
                // Navigate to privacy policy screen
              }}
            />
          </View>
        </View>

        <TouchableOpacity
          style={[styles.deleteButton, { backgroundColor: colors.card }]}
          onPress={handleDeleteAccount}
        >
          <Text style={styles.deleteText}>
            {t('deleteAccount')}
          </Text>
        </TouchableOpacity>

        <Text style={[styles.disclaimer, { color: colors.subtext }]}>
          {t('privacyDisclaimer')}
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontFamily: 'Roboto',
    marginHorizontal: 20,
    marginBottom: 8,
    marginTop: 20,
  },
  settingsContainer: {
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
  },
  settingTextContainer: {
    flex: 1,
    marginEnd: 10,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Roboto',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Roboto',
  },
  optionLeft: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  optionIcon: {
    marginEnd: 14,
  },
  deleteButton: {
    marginHorizontal: 15,
    borderRadius: 10,
    paddingVertical: 16,
    marginTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteText: {
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
    color: '#FF3B30',
  },
  disclaimer: {
    fontSize: 12,
    fontFamily: 'Roboto',
    textAlign: 'center',
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 40,
    lineHeight: 18,
  },
});

export default PrivacyScreen;