import React, { useEffect, useState, useCallback } from 'react';
import { StatusBar, View, Text, Platform } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { <PERSON>eProvider } from '@stripe/stripe-react-native';
import {
  AuthProvider,
  LanguageProvider,
  ThemeProvider,
  NotificationProvider
} from './src/context';
import AppNavigator from './src/navigation/AppNavigator';
import './src/translations';
import { useTranslation } from 'react-i18next';
import Constants from 'expo-constants';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';
import config from './src/config/config';
import { getFontSource, preventScreenshots } from './src/utils';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync().catch(() => {
  /* reloading the app might trigger some race conditions, ignore them */
});

export default function App() {
  const { i18n } = useTranslation();
  const [appIsReady, setAppIsReady] = useState(false);
  const [fontsLoaded, setFontsLoaded] = useState(false);

  const loadFonts = useCallback(async () => {
    try {
      console.log('Loading fonts...');

      // Create a font map
      const fontMap = {
        'Roboto': require('./assets/fonts/Roboto/static/Roboto-Regular.ttf'),
        'Roboto-Medium': require('./assets/fonts/Roboto/static/Roboto-Medium.ttf'),
        'Roboto-Bold': require('./assets/fonts/Roboto/static/Roboto-Bold.ttf'),
        'Poppins': require('./assets/fonts/Poppins/Poppins-Regular.ttf'),
        'Poppins-Medium': require('./assets/fonts/Poppins/Poppins-Medium.ttf'),
        'Poppins-Bold': require('./assets/fonts/Poppins/Poppins-Bold.ttf'),
      };

      // Attempt to load each font individually for better error handling
      const fontLoadPromises = Object.entries(fontMap).map(async ([name, source]) => {
        try {
          await Font.loadAsync({ [name]: source });
          return true;
        } catch (e) {
          console.warn(`Failed to load font ${name}:`, e.message);
          return false;
        }
      });

      const results = await Promise.all(fontLoadPromises);
      const allFontsLoaded = results.every(result => result === true);

      if (allFontsLoaded) {
        console.log('All fonts loaded successfully');
      } else {
        console.log('Some fonts failed to load, but app will continue');
      }

      setFontsLoaded(allFontsLoaded);
      return allFontsLoaded;
    } catch (error) {
      console.error('Error in font loading process:', error);
      // Continue even if fonts fail to load
      return false;
    }
  }, []);

  // RTL is now handled by the global.js file
  // No need for RTL setup here as it's done at app startup in global.js

  useEffect(() => {
    async function prepare() {
      try {
        // Load fonts but proceed even if they fail
        await loadFonts().catch(err => {
          console.warn('Font loading error (continuing anyway):', err);
        });

        // Artificial delay for a smoother experience
        await new Promise(resolve => setTimeout(resolve, 300));
      } catch (e) {
        console.warn('Error during app preparation:', e);
      } finally {
        // Tell the application to render regardless of font loading status
        setAppIsReady(true);
      }
    }

    prepare();
  }, [loadFonts]);

  useEffect(() => {
    if (appIsReady) {
      // Hide splash screen once everything is loaded
      SplashScreen.hideAsync().catch(error => {
        console.warn("Error hiding splash screen:", error);
        // Continue with app initialization even if splash screen hiding fails
      });

      // Enable screenshot prevention for the entire app
      preventScreenshots();
      console.log('Screenshot prevention enabled for the entire app');
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  // Get Stripe publishable key from configuration
  const stripePublishableKey = config.STRIPE_PUBLISHABLE_KEY;

  // Log the Stripe publishable key for debugging
  console.log('App.js: Using Stripe publishable key:', stripePublishableKey);

  return (
    <SafeAreaProvider>
      <LanguageProvider>
        <AuthProvider>
          <ThemeProvider>
            <NotificationProvider>
              <StripeProvider publishableKey={stripePublishableKey}>
                <NavigationContainer>
                  <AppNavigator />
                </NavigationContainer>
              </StripeProvider>
            </NotificationProvider>
          </ThemeProvider>
        </AuthProvider>
      </LanguageProvider>
    </SafeAreaProvider>
  );
}


