/**
 * Font utilities for Nasebi
 */
import { Platform } from 'react-native';

// System font fallbacks by platform
const SYSTEM_FONTS = Platform.select({
  ios: {
    regular: 'System',
    medium: 'System',
    bold: 'System-Bold',
    light: 'System-Light',
  },
  android: {
    regular: 'sans-serif',
    medium: 'sans-serif-medium',
    bold: 'sans-serif-bold',
    light: 'sans-serif-light',
  },
  default: {
    regular: 'System',
    medium: 'System',
    bold: 'System-Bold',
    light: 'System-Light',
  }
});

// Font configuration with fallbacks
export const FONTS = {
  // Regular fonts
  regular: Platform.select({
    web: 'Roboto, sans-serif',
    default: 'Roboto',
  }),
  medium: Platform.select({
    web: 'Roboto-Medium, Roboto, sans-serif',
    default: 'Roboto-Medium',
  }),
  bold: Platform.select({
    web: 'Roboto-Bold, Roboto, sans-serif',
    default: 'Roboto-Bold',
  }),
  
  // Poppins family
  poppinsRegular: Platform.select({
    web: 'Poppins, sans-serif',
    default: 'Poppins',
  }),
  poppinsMedium: Platform.select({
    web: 'Poppins-Medium, Poppins, sans-serif',
    default: 'Poppins-Medium',
  }),
  poppinsBold: Platform.select({
    web: 'Poppins-Bold, Poppins, sans-serif',
    default: 'Poppins-Bold',
  }),
  
  // System fallbacks
  system: SYSTEM_FONTS,
  
  // RTL/Arabic fonts
  arabic: Platform.select({
    ios: 'ArialHebrew',
    android: 'sans-serif',
    default: 'System',
  })
};

// Font size scale
export const FONT_SIZES = {
  xs: 10,
  sm: 12,
  md: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
};

// Use this function if we need to load fonts dynamically
export const getFontSource = (fontFamily) => {
  try {
    switch (fontFamily) {
      case 'Roboto':
        return require('../../assets/fonts/Roboto/static/Roboto-Regular.ttf');
      case 'Roboto-Medium':
        return require('../../assets/fonts/Roboto/static/Roboto-Medium.ttf');
      case 'Roboto-Bold':
        return require('../../assets/fonts/Roboto/static/Roboto-Bold.ttf');
      case 'Poppins':
        return require('../../assets/fonts/Poppins/Poppins-Regular.ttf');
      case 'Poppins-Medium':
        return require('../../assets/fonts/Poppins/Poppins-Medium.ttf');
      case 'Poppins-Bold':
        return require('../../assets/fonts/Poppins/Poppins-Bold.ttf');
      default:
        return null;
    }
  } catch (error) {
    console.warn(`Font source not found for ${fontFamily}:`, error);
    return null;
  }
};

// Function to get the font family with fallback
export const getFontFamily = (family, isFontLoaded = true) => {
  if (!isFontLoaded) {
    // Return system font if custom fonts haven't loaded
    switch (family) {
      case 'Roboto':
      case 'regular':
        return SYSTEM_FONTS.regular;
      case 'Roboto-Medium':
      case 'medium':
        return SYSTEM_FONTS.medium;
      case 'Roboto-Bold':
      case 'bold':
        return SYSTEM_FONTS.bold;
      case 'Poppins':
        return SYSTEM_FONTS.regular;
      case 'Poppins-Medium':
        return SYSTEM_FONTS.medium;
      case 'Poppins-Bold':
        return SYSTEM_FONTS.bold;
      default:
        return SYSTEM_FONTS.regular;
    }
  }
  
  return family;
};

export default {
  FONTS,
  FONT_SIZES,
  getFontFamily,
  getFontSource,
}; 