import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Switch,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import api from '../../services/api';
import { useLanguage } from '../../context/LanguageContext';
import { getUser, updateUser as updateUserService, updateProfileImage } from '../../services/userService';
import LoadingSpinner from '../../components/LoadingSpinner';
import { useRTL } from '../../hooks/useRTL';
import BlurredProfileImage from '../../components/BlurredProfileImage';

const ProfileScreen = ({ navigation }) => {
  const { userData: authUser, logout, updateUser } = useAuth();
  const { colors, isDark } = useTheme();
  const { t } = useTranslationFlat();
  const { i18n } = useTranslation();
  const rtl = useRTL(); // Use only the useRTL hook for consistency
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [profilePhoto, setProfilePhoto] = useState(null);
  const [isPhotoPublic, setIsPhotoPublic] = useState(true);
  const [settings, setSettings] = useState({
    hidePhotos: false,
    notifications: true,
    premium: false,
  });

  useEffect(() => {
    if (authUser) {
      // Use the userData directly from AuthContext
      setUser(authUser);

      // Check if user has a profile photo
      if (authUser.photos && authUser.photos.length > 0) {
        const photo = authUser.photos[0];
        setProfilePhoto(photo);
        setIsPhotoPublic(photo.isPublic);
      } else {
        setProfilePhoto(null);
      }

      checkPhotoPermissions();
      setLoading(false);
    } else {
      console.log('No authenticated user found in AuthContext');
      setLoading(false);
    }
  }, [authUser]);

  useEffect(() => {
    // Refresh profile when the screen comes into focus
    const unsubscribe = navigation.addListener('focus', () => {
      if (authUser) {
        // Use the userData directly from AuthContext
        setUser(authUser);

        // Check if user has a profile photo
        if (authUser.photos && authUser.photos.length > 0) {
          const photo = authUser.photos[0];
          setProfilePhoto(photo);
          setIsPhotoPublic(photo.isPublic);
        } else {
          setProfilePhoto(null);
        }
      } else {
        console.log('No authenticated user found in AuthContext on screen focus');
        setLoading(false);
      }
    });

    return unsubscribe;
  }, [navigation, authUser]);

  const loadUserData = async () => {
    setLoading(true);
    let retryCount = 0;
    const maxRetries = 3;

    const attemptFetch = async () => {
      try {
        console.log(`Loading user data... (Attempt ${retryCount + 1}/${maxRetries})`);

        if (!authUser || !authUser.id) {
          console.error('No authenticated user found');
          // Set default user data to prevent UI errors
          setUser({
            name: '',
            email: '',
            bio: '',
            phone: '',
            profileImage: null,
            profile_completion_percentage: 0
          });
          return false;
        }

        const userData = await getUser(authUser.id);
        if (userData) {
          setUser(userData);
          return true; // Success
        } else {
          console.error('User data is null or undefined');
          // Set default user data to prevent UI errors
          setUser({
            name: authUser?.name || '',
            email: authUser?.email || '',
            bio: '',
            phone: '',
            profileImage: null,
            profile_completion_percentage: 0
          });
          return false;
        }
      } catch (error) {
        console.error(`Error loading user data (Attempt ${retryCount + 1}/${maxRetries}):`, error);

        if (retryCount < maxRetries - 1) {
          retryCount++;
          console.log(`Retrying in 1 second... (${retryCount}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
          return attemptFetch(); // Recursive retry
        }

        // All retries failed
        console.log('All retry attempts failed');
        // Set default user data to prevent UI errors
        setUser({
          name: authUser?.name || '',
          email: authUser?.email || '',
          bio: '',
          phone: '',
          profileImage: null,
          profile_completion_percentage: 0
        });

        if (!__DEV__) {
          Alert.alert(t('error'), t('loadError'));
        }
        return false; // Failed after all retries
      }
    };

    try {
      await attemptFetch();
    } finally {
      setLoading(false);
    }
  };

  const loadProfile = async () => {
    setLoading(true);
    let retryCount = 0;
    const maxRetries = 3;

    const attemptFetch = async () => {
      try {
        console.log(`Loading profile data... (Attempt ${retryCount + 1}/${maxRetries})`);

        if (!authUser || !authUser.id) {
          console.error('No authenticated user found');
          // Set default profile data
          setUser(null);
          setProfilePhoto(null);
          setIsPhotoPublic(true);
          setSettings({
            hidePhotos: false,
            notifications: true,
            premium: false
          });
          return false;
        }

        const response = await api.get('/api/profile');
        console.log('Profile response:', response.data);

        if (response && response.data) {
          // Extract profile data based on different possible response structures
          const profileData = response.data.profile || response.data;

          // Update user data if available
          if (profileData) {
            setUser(prevUser => ({
              ...prevUser,
              ...profileData
            }));
          }

          // Check if user has a profile photo
          let photoData = null;

          // Handle different photo field structures
          if (profileData.photos && profileData.photos.length > 0) {
            photoData = profileData.photos[0];
          } else if (profileData.photo) {
            photoData = {
              id: 1,
              url: profileData.photo,
              isPublic: profileData.isPhotoPublic !== false
            };
          } else if (profileData.profile_photo) {
            photoData = {
              id: 1,
              url: profileData.profile_photo,
              isPublic: profileData.isPhotoPublic !== false
            };
          } else if (profileData.profileImage) {
            photoData = {
              id: 1,
              url: profileData.profileImage,
              isPublic: profileData.isPhotoPublic !== false
            };
          } else if (profileData.avatar) {
            photoData = {
              id: 1,
              url: profileData.avatar,
              isPublic: profileData.isPhotoPublic !== false
            };
          }

          if (photoData) {
            setProfilePhoto(photoData);
            setIsPhotoPublic(photoData.isPublic !== false);
          } else {
            setProfilePhoto(null);
          }

          // Extract settings data
          const settingsData = profileData.settings || response.data.settings || {
            hidePhotos: false,
            notifications: true,
            premium: false
          };

          setSettings(settingsData);
          return true; // Success
        } else {
          console.error('Invalid profile response data');
          // Set default values
          setProfilePhoto(null);
          setIsPhotoPublic(true);
          setSettings({
            hidePhotos: false,
            notifications: true,
            premium: false
          });
          return false;
        }
      } catch (error) {
        console.error(`Error loading profile (Attempt ${retryCount + 1}/${maxRetries}):`, error);

        if (retryCount < maxRetries - 1) {
          retryCount++;
          console.log(`Retrying in 1 second... (${retryCount}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
          return attemptFetch(); // Recursive retry
        }

        // All retries failed
        console.log('All retry attempts failed');
        // Set default values to prevent UI errors
        setProfilePhoto(null);
        setIsPhotoPublic(true);
        setSettings({
          hidePhotos: false,
          notifications: true,
          premium: false
        });
        return false; // Failed after all retries
      }
    };

    try {
      await attemptFetch();
    } finally {
      setLoading(false);
    }
  };

  const checkPhotoPermissions = async () => {
    try {
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          console.log('Camera roll permission denied');
          Alert.alert(
            t('permissionRequired'),
            t('photoPermission')
          );
          return false;
        }
        return true;
      }
      return true;
    } catch (error) {
      console.error('Error checking photo permissions:', error);
      return false;
    }
  };

  const handlePhotoUpload = async () => {
    try {
      const hasPermission = await checkPhotoPermissions();
      if (!hasPermission) {
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0] && result.assets[0].uri) {
        // Show visibility options dialog
        Alert.alert(
          t('photoVisibility') || 'Photo Visibility',
          t('chooseVisibility') || 'Choose who can see this photo',
          [
            {
              text: t('visibilityPublic') || 'Public (Everyone)',
              onPress: () => uploadPhoto(result.assets[0].uri, true),
            },
            {
              text: t('visibilityPrivate') || 'Private (Matches Only)',
              onPress: () => uploadPhoto(result.assets[0].uri, false),
            },
            {
              text: t('cancel') || 'Cancel',
              style: 'cancel',
            },
          ],
          { cancelable: true }
        );
      }
    } catch (error) {
      console.error('Photo upload error:', error);
      Alert.alert(t('error') || 'Error', t('photoUploadFailed') || 'Failed to upload photo');
    }
  };

  const uploadPhoto = async (uri, isPublic) => {
    try {
      setLoading(true);
      console.log(`Uploading photo with URI: ${uri}, isPublic: ${isPublic}`);

      // Try both methods to ensure the photo gets uploaded

      // Method 1: Use the direct API endpoint
      try {
        const formData = new FormData();
        formData.append('photo', {
          uri: uri,
          type: 'image/jpeg',
          name: 'profile-photo.jpg',
        });
        formData.append('isPublic', isPublic ? '1' : '0');

        const response = await api.post('/api/profile/photo', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (response && response.data && response.data.url) {
          const newPhoto = {
            id: response.data.id || Date.now(),
            url: response.data.url,
            isPublic: isPublic
          };

          // Update local state
          setProfilePhoto(newPhoto);
          setIsPhotoPublic(isPublic);

          // Update user context
          if (updateUser && authUser) {
            updateUser({
              ...authUser,
              profileImage: newPhoto.url, // Update the main profile image
              photos: [newPhoto]
            });
          }

          // Also update the local user state
          setUser(prevUser => ({
            ...prevUser,
            profileImage: newPhoto.url
          }));

          Alert.alert(
            t('photoUploaded') || 'Photo Uploaded',
            isPublic
              ? (t('photoVisibleToAll') || 'Your photo is visible to everyone')
              : (t('photoVisibleToMatches') || 'Your photo is only visible to your matches')
          );

          return; // Success, exit the function
        }
      } catch (directApiError) {
        console.log('Direct API upload failed, trying fallback method:', directApiError);
      }

      // Method 2: Use the updateUser method from AuthContext
      if (updateUser && authUser) {
        // Create a temporary photo object with the local URI
        const tempPhoto = {
          id: Date.now(),
          url: uri,
          isPublic: isPublic
        };

        // Update the user with the new photo
        const result = await updateUser({
          ...authUser,
          profileImage: uri, // Set the profile image to the photo URI
          photos: [tempPhoto]
        });

        if (result.success) {
          // Get the updated photo from the result
          const updatedUser = result.user;
          const updatedPhoto = updatedUser.photos && updatedUser.photos.length > 0
            ? updatedUser.photos[0]
            : tempPhoto;

          // Update local state
          setProfilePhoto(updatedPhoto);
          setIsPhotoPublic(isPublic);

          // Also update the local user state
          setUser(prevUser => ({
            ...prevUser,
            profileImage: updatedPhoto.url || uri
          }));

          Alert.alert(
            t('photoUploaded') || 'Photo Uploaded',
            isPublic
              ? (t('photoVisibleToAll') || 'Your photo is visible to everyone')
              : (t('photoVisibleToMatches') || 'Your photo is only visible to your matches')
          );
        } else {
          throw new Error(result.error || 'Failed to update profile');
        }
      } else {
        throw new Error('User context not available');
      }
    } catch (uploadError) {
      console.error('Photo upload error:', uploadError);
      Alert.alert(t('error') || 'Error', t('photoUploadError') || 'Error uploading photo');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePhoto = async () => {
    try {
      if (!profilePhoto) {
        return;
      }

      Alert.alert(
        t('deletePhoto') || 'Delete Photo',
        t('deletePhotoConfirm') || 'Are you sure you want to delete this photo?',
        [
          {
            text: t('cancel') || 'Cancel',
            style: 'cancel',
          },
          {
            text: t('delete') || 'Delete',
            style: 'destructive',
            onPress: async () => {
              setLoading(true);
              try {
                await api.delete('/api/profile/photo');

                // Update local state
                setProfilePhoto(null);
                setIsPhotoPublic(true);

                // Update the user context
                if (updateUser && authUser) {
                  updateUser({
                    ...authUser,
                    profileImage: null, // Clear the profile image
                    photos: []
                  });
                }

                // Also update the local user state
                setUser(prevUser => ({
                  ...prevUser,
                  profileImage: null
                }));

                Alert.alert(
                  t('success') || 'Success',
                  t('photoDeleted') || 'Photo deleted successfully'
                );
              } catch (error) {
                Alert.alert(
                  t('error') || 'Error',
                  t('deletePhotoError') || 'Failed to delete photo'
                );
                console.error('Photo delete error:', error);
              } finally {
                setLoading(false);
              }
            }
          }
        ],
        { cancelable: true }
      );
    } catch (error) {
      Alert.alert(
        t('error') || 'Error',
        t('deletePhotoError') || 'Failed to delete photo'
      );
      console.error('Photo delete error:', error);
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      t('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
      t('يرجى التأكيد لتسجيل الخروج'),
      [
        { text: t('إلغاء'), style: 'cancel' },
        {
          text: t('تسجيل الخروج'),
          onPress: logout,
          style: 'destructive'
        },
      ]
    );
  };

  const togglePhotoVisibility = async () => {
    try {
      if (!profilePhoto) {
        return;
      }

      setLoading(true);
      const newVisibility = !isPhotoPublic;

      // Call API to update photo visibility
      await api.patch('/api/profile/photo/visibility', {
        isPublic: newVisibility
      });

      // Update local state
      setIsPhotoPublic(newVisibility);

      // Update the user context
      if (updateUser && authUser) {
        const updatedPhoto = { ...profilePhoto, isPublic: newVisibility };

        updateUser({
          ...authUser,
          photos: [updatedPhoto]
        });
      }

      Alert.alert(
        t('visibilityUpdated') || 'Visibility Updated',
        newVisibility
          ? (t('photoNowPublic') || 'Photo is now public')
          : (t('photoNowPrivate') || 'Photo is now private')
      );
    } catch (error) {
      console.error('Error updating photo visibility:', error);
      Alert.alert(
        t('error') || 'Error',
        t('visibilityUpdateError') || 'Failed to update photo visibility'
      );
    } finally {
      setLoading(false);
    }
  };

  const toggleSetting = async (setting) => {
    try {
      const newValue = !settings[setting];
      await api.patch('/api/profile/settings', { [setting]: newValue });
      setSettings(prev => ({ ...prev, [setting]: newValue }));
    } catch (error) {
      console.error('Settings update error:', error);
    }
  };

  const navigateToSubscription = () => {
    navigation.navigate('Subscription');
  };

  const handleUpgradePress = () => {
    if (settings.premium) {
      navigateToSubscription();
    } else {
      navigateToSubscription();
    }
  };

  const handleProfilePhotoUpload = async () => {
    // Use the same function as handlePhotoUpload to maintain consistency
    handlePhotoUpload();
  };

  const renderProfileHeader = () => {
    const profileCompletionPercentage = user?.profile_completion_percentage || 0;
    const isProfileComplete = profileCompletionPercentage === 100;
    const rtlStyles = rtl;

    return (
      <View style={[styles.header, { backgroundColor: colors.card }]}>
        {/* Profile completion and verification banner */}
        <View style={styles.profileCompletionContainer}>
          <Text style={[styles.profileCompletionText, { color: colors.text }, rtlStyles.align]}>
            {isProfileComplete
              ? t('profileComplete')
              : t('profileIncomplete')}
          </Text>
          <View style={styles.progressBarContainer}>
            <View style={[styles.progressBar, { width: `${profileCompletionPercentage}%`, backgroundColor: colors.primary }]} />
            <Text style={[styles.progressText, { color: colors.text }]}>
              {profileCompletionPercentage}/100
            </Text>
          </View>
        </View>

        {user?.email_verified === false && (
          <View style={[styles.verificationBanner, { backgroundColor: colors.notification }]}>
            <Text style={[styles.verificationText, { color: isDark ? '#ffffff' : '#000000' }, rtlStyles.align]}>
              {t('emailNotVerified')}
            </Text>
          </View>
        )}

        {/* Profile picture and info side by side */}
        <View style={[styles.profileHeaderRow, rtlStyles.direction]}>
          {/* Profile picture */}
          <TouchableOpacity
            onPress={handleProfilePhotoUpload}
            style={styles.avatarWrapper}
          >
            {user?.profileImage ? (
              <Image
                source={{ uri: user.profileImage }}
                style={styles.avatar}
                resizeMode="cover"
              />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: colors.border }]}>
                <FontAwesome name="user" size={60} color={colors.text} />
              </View>
            )}
          </TouchableOpacity>

          {/* User information */}
          <View style={[styles.profileInfo, rtlStyles.isRTL ? { marginEnd: 15, marginStart: 0 } : { marginStart: 15 }]}>
            <Text style={[styles.nameText, { color: colors.text }, rtlStyles.align]}>
              {user?.name || t('noName')}, {user?.age || '--'}
            </Text>
            {user?.nationality && (
              <View style={[styles.locationContainer, rtlStyles.direction]}>
                <Text style={[styles.locationText, { color: colors.text }, rtlStyles.align]}>
                  {user.nationality}
                </Text>
              </View>
            )}
            {user?.location && (
              <View style={[styles.locationContainer, rtlStyles.direction]}>
                <Ionicons
                  name="location-sharp"
                  size={16}
                  color={colors.primary}
                  style={[styles.locationIcon, rtlStyles.isRTL ? { marginStart: 5, marginEnd: 0 } : { marginEnd: 5 }]}
                />
                <Text style={[styles.locationText, { color: colors.text }, rtlStyles.align]}>
                  {user.location}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    );
  };

  const renderBasicInfo = () => {
    const rtlStyles = rtl;

    return (
      <View style={[styles.section, { backgroundColor: colors.card }]}>
        <Text style={[styles.title, { color: colors.text }, rtlStyles.align]}>
          {t('basicInfo')}
        </Text>

        <View style={styles.infoGrid}>
          {user?.education_level && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="school" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('educationLevel')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.education_level ? t(`education${user.education_level.charAt(0).toUpperCase() + user.education_level.slice(1)}`) : t('notSpecified')}
              </Text>
            </View>
          )}

          {user?.job_level && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="briefcase" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('jobLevel')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.job_level ? t(`job${user.job_level.charAt(0).toUpperCase() + user.job_level.slice(1)}`) : t('notSpecified')}
              </Text>
            </View>
          )}

          {user?.preferred_residence && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="home" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('residence')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.preferred_residence ? t(`residence${user.preferred_residence.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('')}`) : t('notSpecified')}
              </Text>
            </View>
          )}

          {user?.skin_color && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="hand-front-right" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('skinColor')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.skin_color ?
                  // Try different translation key formats based on the language
                  (i18n.language === 'ar' ?
                    // For Arabic, use skinColorFair format
                    t(`skinColor${user.skin_color.charAt(0).toUpperCase() + user.skin_color.slice(1).replace(/_/g, '')}`)
                    :
                    // For English, use skinColor_fair format
                    t(`skinColor_${user.skin_color}`))
                  : t('notSpecified')}
              </Text>
            </View>
          )}

          {typeof user?.tribal_affiliation === 'boolean' && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="account-group" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('tribalAffiliation')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.tribal_affiliation ? t('common_yes') : t('common_no')}
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderReligiousInfo = () => {
    const rtlStyles = rtl;

    return (
      <View style={[styles.section, { backgroundColor: colors.card }]}>
        <Text style={[styles.title, { color: colors.text }, rtlStyles.align]}>
          {t('religiousInfo')}
        </Text>

        <View style={styles.infoGrid}>
          {user?.prayer_level && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="hands-pray" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('prayerFrequency')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.prayer_level ? t(`prayer${user.prayer_level.charAt(0).toUpperCase() + user.prayer_level.slice(1)}`) : t('notSpecified')}
              </Text>
            </View>
          )}

          {user?.chat_languages && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="message-processing" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('preferredLanguages')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.chat_languages}
              </Text>
            </View>
          )}

          {user?.income_level && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="currency-usd" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('incomeLevel')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.income_level ? t(`income${user.income_level.charAt(0).toUpperCase() + user.income_level.slice(1)}`) : t('notSpecified')}
              </Text>
            </View>
          )}

          {user?.wants_children && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="baby-carriage" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('wantsChildren')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.wants_children ? t(`children${user.wants_children.charAt(0).toUpperCase() + user.wants_children.slice(1)}`) : t('notSpecified')}
              </Text>
            </View>
          )}

          {user?.smoking && (
            <View style={styles.infoItem}>
              <View style={[styles.infoIconContainer, rtlStyles.direction]}>
                <MaterialCommunityIcons name="smoking" size={18} color={colors.primary} />
              </View>
              <Text style={[styles.infoLabel, { color: colors.subtext }, rtlStyles.align]}>{t('smoking')}</Text>
              <Text style={[styles.infoValue, { color: colors.text }, rtlStyles.align]}>
                {user.smoking ? t(`smoking${user.smoking.charAt(0).toUpperCase() + user.smoking.slice(1)}`) : t('notSpecified')}
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderActionsMenu = () => {
    const rtlStyles = rtl;

    return (
      <View style={[styles.section, { backgroundColor: colors.card }]}>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.background }, rtlStyles.direction]}
            onPress={() => navigation.navigate('Settings')}
          >
            <Ionicons name="settings-outline" size={24} color={colors.text} />
            <Text style={[styles.actionButtonText, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
              {t('settings')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.background }, rtlStyles.direction]}
            onPress={() => navigation.navigate('EditProfile')}
          >
            <Ionicons name="create-outline" size={24} color={colors.text} />
            <Text style={[styles.actionButtonText, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
              {t('editProfile')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.background }, rtlStyles.direction]}
            onPress={() => navigation.navigate('Preferences')}
          >
            <Ionicons name="options-outline" size={24} color={colors.text} />
            <Text style={[styles.actionButtonText, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
              {t('preferences')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.background, borderColor: isDark ? '#444' : '#ddd' }, rtlStyles.direction]}
            onPress={handleLogout}
          >
            <Ionicons name="eye-off-outline" size={24} color="#FFC107" />
            <Text style={[styles.actionButtonText, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
              {t('Log Out')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderSubscriptionBanner = () => {
    if (settings.premium) return null;
    const rtlStyles = rtl;

    return (
      <TouchableOpacity
        style={[styles.subscriptionBanner, { backgroundColor: isDark ? '#483b00' : '#fff7d9', borderColor: '#FFC107' }]}
        onPress={handleUpgradePress}
      >
        <View style={[styles.subscriptionContent, rtlStyles.direction]}>
          <View style={styles.crownContainer}>
            <Ionicons name="star" size={24} color="#FFC107" />
          </View>
          <View style={[styles.subscriptionText, rtlStyles.isRTL ? { marginEnd: 10, marginStart: 0 } : { marginStart: 10 }]}>
            <Text style={[styles.subscriptionTitle, { color: isDark ? '#FFC107' : '#8B6000' }, rtlStyles.align]}>
              {t('vipMembership')}
            </Text>
            <Text style={[styles.subscriptionDescription, { color: isDark ? '#e0e0e0' : '#5D4037' }, rtlStyles.align]}>
              {t('vipDescription')}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={[styles.subscribeButton, { backgroundColor: '#FFC107' }]}
          onPress={handleUpgradePress}
        >
          <Text style={[styles.subscribeButtonText, rtlStyles.align]}>
            {t('subscribe')}
          </Text>
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  const renderBio = () => {
    if (!user?.bio && !user?.partner_description) return null;
    const rtlStyles = rtl;

    return (
      <View style={[styles.section, { backgroundColor: colors.card }]}>
        {/* Display sections in opposite order based on language direction */}
        {rtlStyles.isRTL ? (
          // For RTL (Arabic): Partner first, then About Me
          <>
            {user?.partner_description && (
              <View style={styles.bioContainer}>
                <View style={[styles.bioHeader, rtlStyles.direction]}>
                  <MaterialCommunityIcons name="account-heart-outline" size={20} color={colors.text} />
                  <Text style={[styles.bioTitle, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
                    {t('partnerDescription')}
                  </Text>
                </View>
                <Text style={[styles.bioText, { color: colors.text }, rtlStyles.align]}>
                  {user.partner_description}
                </Text>
              </View>
            )}

            {user?.bio && (
              <View style={[styles.bioContainer, user?.partner_description ? { marginTop: 15 } : {}]}>
                <View style={[styles.bioHeader, rtlStyles.direction]}>
                  <MaterialCommunityIcons name="account-outline" size={20} color={colors.text} />
                  <Text style={[styles.bioTitle, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
                    {t('aboutMe')}
                  </Text>
                </View>
                <Text style={[styles.bioText, { color: colors.text }, rtlStyles.align]}>
                  {user.bio}
                </Text>
              </View>
            )}
          </>
        ) : (
          // For LTR (English): About Me first, then Partner
          <>
            {user?.bio && (
              <View style={styles.bioContainer}>
                <View style={[styles.bioHeader, rtlStyles.direction]}>
                  <MaterialCommunityIcons name="account-outline" size={20} color={colors.text} />
                  <Text style={[styles.bioTitle, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
                    {t('aboutMe')}
                  </Text>
                </View>
                <Text style={[styles.bioText, { color: colors.text }, rtlStyles.align]}>
                  {user.bio}
                </Text>
              </View>
            )}

            {user?.partner_description && (
              <View style={[styles.bioContainer, user?.bio ? { marginTop: 15 } : {}]}>
                <View style={[styles.bioHeader, rtlStyles.direction]}>
                  <MaterialCommunityIcons name="account-heart-outline" size={20} color={colors.text} />
                  <Text style={[styles.bioTitle, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
                    {t('partnerDescription')}
                  </Text>
                </View>
                <Text style={[styles.bioText, { color: colors.text }, rtlStyles.align]}>
                  {user.partner_description}
                </Text>
              </View>
            )}
          </>
        )}
      </View>
    );
  };

  // Show loading indicator
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background, justifyContent: 'center', alignItems: 'center' }]}>
        <LoadingSpinner size="large" color={colors.primary} />
        <Text style={{ marginTop: 20, color: colors.text }}>{t('loading')}</Text>
      </SafeAreaView>
    );
  }

  // Show message if not authenticated
  if (!authUser) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background, justifyContent: 'center', alignItems: 'center' }]}>
        <Ionicons name="person-circle-outline" size={80} color={colors.primary} />
        <Text style={{ marginTop: 20, fontSize: 18, color: colors.text, textAlign: 'center', marginHorizontal: 30 }}>
          {t('profile.notAuthenticated')}
        </Text>
        <TouchableOpacity
          style={[styles.loginButton, { backgroundColor: colors.primary, marginTop: 30, paddingVertical: 12, paddingHorizontal: 30, borderRadius: 8 }]}
          onPress={() => navigation.navigate('Login')}
        >
          <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 16 }}>
            {t('auth.login')}
          </Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  const renderPhotos = () => {
    const rtlStyles = rtl;

    if (!profilePhoto) {
      return (
        <View style={[styles.section, { backgroundColor: colors.card }]}>
          <Text style={[styles.title, { color: colors.text }, rtlStyles.align]}>
            {t('photo') || 'Profile Photo'}
          </Text>
          <TouchableOpacity
            style={[styles.addPhotoButton, { borderColor: colors.border }, rtlStyles.direction]}
            onPress={handlePhotoUpload}
          >
            <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
            <Text style={[styles.addPhotoText, { color: colors.text }, rtlStyles.align, rtlStyles.isRTL ? { marginEnd: 8, marginStart: 0 } : { marginStart: 8 }]}>
              {t('addPhoto') || 'Add Profile Photo'}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={[styles.section, { backgroundColor: colors.card }]}>
        <View style={[styles.sectionHeader, rtlStyles.direction, { justifyContent: 'space-between' }]}>
          <Text style={[styles.title, { color: colors.text }, rtlStyles.align]}>
            {t('photo') || 'Profile Photo'}
          </Text>
          <TouchableOpacity onPress={handlePhotoUpload}>
            <Ionicons name="refresh-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>

        <View style={styles.singlePhotoContainer}>
          <Image
            source={{ uri: profilePhoto.url }}
            style={styles.singlePhoto}
            resizeMode="cover"
          />
          <View style={[styles.photoActions, rtlStyles.direction]}>
            <TouchableOpacity
              style={[styles.photoActionButton, { backgroundColor: colors.card }]}
              onPress={togglePhotoVisibility}
            >
              <Ionicons
                name={isPhotoPublic ? "eye-outline" : "eye-off-outline"}
                size={20}
                color={isPhotoPublic ? colors.primary : colors.text}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.photoActionButton, { backgroundColor: colors.card }]}
              onPress={handleDeletePhoto}
            >
              <Ionicons name="trash-outline" size={20} color="#FF6B6B" />
            </TouchableOpacity>
          </View>
          <View style={[
            styles.visibilityBadge,
            { backgroundColor: isPhotoPublic ? colors.primary : colors.border }
          ]}>
            <Text style={[styles.visibilityText, { color: '#fff' }, rtlStyles.align]}>
              {isPhotoPublic ? (t('public') || 'Public') : (t('private') || 'Private')}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  // Create RTL-aware styles
  const rtlStyles = {
    direction: rtl.direction,
    align: rtl.align,
    isRTL: rtl.isRTL,
    margin: rtl.margin,
    padding: rtl.padding,
    position: rtl.position,
    positionAbsolute: rtl.positionAbsolute,
    iconTransform: rtl.iconTransform,
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        bounces={true}
        overScrollMode="always"
      >
        {renderProfileHeader()}
        {renderActionsMenu()}
        {renderSubscriptionBanner()}
        {renderPhotos()}
        {renderBio()}
        {renderBasicInfo()}
        {renderReligiousInfo()}
        {/* Add extra padding at the bottom to ensure all content is visible */}
        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  bottomPadding: {
    height: 40, // Extra padding at the bottom
  },
  header: {
    paddingVertical: 20,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  profileCompletionContainer: {
    marginBottom: 15,
  },
  profileCompletionText: {
    fontSize: 14,
    marginBottom: 5,
  },
  progressBarContainer: {
    height: 20,
    backgroundColor: '#e0e0e0',
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  progressBar: {
    height: '100%',
    borderRadius: 10,
  },
  progressText: {
    position: 'absolute',
    end: 10,
    top: 0,
    bottom: 0,
    fontSize: 12,
    textAlignVertical: 'center',
  },
  verificationBanner: {
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  verificationText: {
    textAlign: 'center',
    fontSize: 14,
  },
  profileHeaderRow: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  avatarWrapper: {
    width: '30%', // Allocate 30% of the width for the avatar
  },
  avatarContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    flex: 1, // Take up remaining space
    marginStart: 15,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  locationContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginVertical: 3,
  },
  locationIcon: {
    marginEnd: 5,
  },
  locationText: {
    fontSize: 14,
  },
  section: {
    marginBottom: 15,
    padding: 15,
    borderRadius: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  infoGrid: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  infoItem: {
    width: '48%',
    marginBottom: 15,
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  infoIconContainer: {
    marginBottom: 5,
  },
  infoLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  actionButton: {
    width: '48%',
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  actionButtonText: {
    fontSize: 14,
  },
  subscriptionBanner: {
    marginBottom: 15,
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
  },
  subscriptionContent: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    marginBottom: 15,
  },
  crownContainer: {
    marginEnd: 10,
  },
  subscriptionText: {
    flex: 1,
  },
  subscriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  subscriptionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  subscribeButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  subscribeButtonText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 16,
  },
  bioContainer: {
    padding: 5,
  },
  bioHeader: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  bioTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginStart: 5,
  },
  bioText: {
    fontSize: 14,
    lineHeight: 22,
  },
  loginButton: {
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    alignItems: 'center',
  },
  loginButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  sectionHeader: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  addPhotoButton: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 10,
    marginTop: 10,
  },
  addPhotoText: {
    marginStart: 10,
    fontSize: 16,
  },
  photosGrid: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  photoContainer: {
    width: '48%',
    aspectRatio: 1,
    marginBottom: 15,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  photo: {
    width: '100%',
    height: '100%',
  },
  photoActions: {
    position: 'absolute',
    top: 5,
    end: 5,
  },
  photoActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginStart: 5,
    opacity: 0.9,
  },
  visibilityBadge: {
    position: 'absolute',
    bottom: 5,
    start: 5,
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 10,
  },
  visibilityText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  singlePhotoContainer: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
    marginBottom: 10,
  },
  singlePhoto: {
    width: '100%',
    height: '100%',
  }
});

export default ProfileScreen;