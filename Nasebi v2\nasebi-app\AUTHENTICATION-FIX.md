# Fixing Authentication API Connection Issues

This document provides a comprehensive solution to fix the login and registration API connection issues in the Nasebi app.

## Problem Description

The mobile app is experiencing connection issues when trying to connect to the backend API server for authentication. The error message is:

```
timeout of 30000ms exceeded
```

The app is trying to connect to `http://********:3000` but the connection is timing out.

## Root Cause Analysis

After thorough analysis, we've identified the following issues:

1. **Special IP address issue**: The Android emulator's special IP address `********` is not working correctly for connecting to the host machine.

2. **Fallback mechanism not working**: The fallback mechanism in the API service is still trying to use the same problematic IP address.

3. **Complex retry logic**: The current retry logic is too complex and doesn't effectively solve the connection issue.

## Solution: Direct IP Connection

The solution is to modify the mobile app to use the direct IP address of your computer instead of the special Android emulator IP address.

### Step 1: Find Your Computer's IP Address

Run the following command in PowerShell to find your computer's IP address:

```powershell
ipconfig
```

Look for the IPv4 Address under your active network adapter (e.g., Wi-Fi or Ethernet). It should look like `************`.

### Step 2: Update the Config File

Replace the content of `nasebi-app/src/config/config.js` with the content from `nasebi-app/src/config/config.fixed.js`.

Make sure to update the `DIRECT_IP` constant with your actual IP address:

```javascript
// IMPORTANT: Replace this with your actual IP address
// This is the IP address of your computer on the network
const DIRECT_IP = '************'; // Replace with your actual IP address
```

### Step 3: Update the API Service

Replace the content of `nasebi-app/src/services/api.js` with the content from `nasebi-app/src/services/api.fixed.js`.

Make sure to update the `DIRECT_IP` constant with your actual IP address:

```javascript
// IMPORTANT: Replace this with your actual IP address
// This is the IP address of your computer on the network
const DIRECT_IP = '************'; // Replace with your actual IP address
```

### Step 4: Open Windows Firewall

Run the following command as administrator to open the firewall:

```
netsh advfirewall firewall add rule name="Nasebi API Server (3000)" dir=in action=allow protocol=TCP localport=3000
```

### Step 5: Restart the Mobile App

Restart the mobile app to apply the changes.

## Verification

After implementing the solution:

1. Check the app logs to verify it's using the direct IP address
2. Test the login functionality with the following credentials:
   - Email: <EMAIL>
   - Password: password123
3. Test the registration functionality with a new email address
4. Verify that other API endpoints work after authentication

## Why This Solution Works

This solution works because:

1. It bypasses the problematic special IP address `********`
2. It uses a direct connection to your computer's IP address
3. It simplifies the connection process by using a consistent approach for all platforms
4. It maintains all the necessary headers and authentication tokens

## Testing the Connection

To test if the connection is working correctly:

1. Start the backend server:
   ```
   cd nasebi-backend
   node server.js
   ```

2. Test the API health endpoint from your computer:
   ```
   curl http://localhost:3000/api/health
   ```

3. Test the API health endpoint from your mobile device's browser:
   ```
   http://<your-ip-address>:3000/api/health
   ```

If the health endpoint is accessible from both your computer and mobile device, the connection is working correctly.

## Troubleshooting

If you're still experiencing issues:

1. **Check your IP address**: Make sure you're using the correct IP address of your computer. You can find it by running:
   ```
   ipconfig
   ```
   Look for the IPv4 Address under your active network adapter.

2. **Check if the server is running**: Make sure the backend server is running and accessible at:
   ```
   http://localhost:3000/api/health
   ```

3. **Check firewall settings**: Make sure Windows Firewall is allowing incoming connections to port 3000.

4. **Try a different network**: If possible, try connecting both your development machine and mobile device/emulator to the same network, such as a mobile hotspot.

5. **Test with a physical device**: If possible, test with a physical device connected to the same network as your development machine.

## Alternative Solutions

If the direct IP solution doesn't work, try these alternatives:

### Option 1: Use ngrok

1. Install ngrok:
   ```
   npm install -g ngrok
   ```

2. Expose your local server:
   ```
   ngrok http 3000
   ```

3. Use the ngrok URL in your app:
   ```javascript
   const API_URL = 'https://your-ngrok-url.ngrok.io';
   ```

### Option 2: Use a Local Web Server

1. Install a local web server like XAMPP or WAMP
2. Configure it to proxy requests to your Node.js server
3. Use the web server's IP address in your app

## Conclusion

The authentication API connection issue is caused by connectivity problems between the Android emulator and the host machine. By using the direct IP address of your computer, we bypass this issue and ensure reliable connections for all API requests.

This solution is simple, effective, and doesn't require any changes to the backend server.
