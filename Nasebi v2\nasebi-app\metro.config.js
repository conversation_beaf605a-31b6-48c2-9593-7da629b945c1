const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Configure server settings
config.server = {
  port: 19000
};

// Add support for SVG files and expand supported extensions
config.resolver.assetExts = config.resolver.assetExts.filter(ext => ext !== 'svg');
config.resolver.sourceExts = [
  'js', 'jsx', 'ts', 'tsx', 'json', 'svg',
  'android.js', 'android.jsx', 'android.ts', 'android.tsx',
  'ios.js', 'ios.jsx', 'ios.ts', 'ios.tsx',
  ...config.resolver.sourceExts
];

// Enhanced module resolution for problematic packages
config.resolver.extraNodeModules = {
  // Resolve specific problematic packages
  '@react-native-community/datetimepicker': path.resolve(__dirname, 'node_modules/@react-native-community/datetimepicker'),
  'use-latest-callback': path.resolve(__dirname, 'node_modules/use-latest-callback'),
};

// Fix for packages with conditional exports
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Handle problematic packages with export conditions
  if (moduleName === 'i18next' && platform === 'android') {
    return {
      filePath: path.resolve(__dirname, 'node_modules/i18next/dist/cjs/i18next.js'),
      type: 'sourceFile',
    };
  }
  if (moduleName === 'use-latest-callback') {
    return {
      filePath: path.resolve(__dirname, 'node_modules/use-latest-callback/lib/src/index.js'),
      type: 'sourceFile',
    };
  }

  // Use default resolution for all other cases
  return context.resolveRequest(context, moduleName, platform);
};

// Add resolver options
config.resolver.resolverMainFields = ['react-native', 'browser', 'main', 'module'];
config.resolver.disableHierarchicalLookup = false;

// Configure transformer
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('metro-react-native-babel-transformer')
};

// Configure transform options
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

// Add platform-specific extensions
config.resolver.platforms = ['ios', 'android', 'web'];

// Export the config with NativeWind support
module.exports = withNativeWind(config, { input: './tailwind.config.js' });