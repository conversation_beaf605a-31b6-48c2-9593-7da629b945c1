import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Default API URL based on platform
let API_URL;

// Use the direct IP address of the machine for all platforms
// This ensures consistent access from both emulators and physical devices
// For Android devices, we'll use the actual IP address of the computer
// instead of ******** which might be causing connection issues

// Use the actual IP address of your computer on the network
// Replace this with your actual IP address from the test-connection.js output
const LOCAL_IP = '************'; // Replace with your actual IP from test-connection.js output

// Configure API URL based on platform
if (Platform.OS === 'android') {
  // For Android, use the direct IP address
  API_URL = `http://${LOCAL_IP}:3000`;
  console.log('Android detected, using direct IP:', API_URL);
} else if (Platform.OS === 'ios') {
  // For iOS, use localhost or direct IP depending on if it's a simulator or device
  API_URL = 'http://localhost:3000';
  console.log('iOS detected, using:', API_URL);
} else {
  // For web or other platforms
  API_URL = 'http://localhost:3000';
  console.log('Web/other platform detected, using:', API_URL);
}

// Log platform information for debugging
console.log('Platform:', Platform.OS);
console.log('Using API URL:', API_URL);

// Override with environment variables if available
if (Constants.expoConfig?.extra?.apiUrl) {
  API_URL = Constants.expoConfig.extra.apiUrl;
  console.log('Overriding API URL from environment:', API_URL);
}

// Environment settings
const isDevelopment = process.env.NODE_ENV === 'development' || __DEV__;

// Stripe configuration
const STRIPE_PUBLISHABLE_KEY =
  Constants.expoConfig?.extra?.stripePublishableKey ||
  'pk_test_51OpGOZFzxLuwKT1JYzL55vTzxr8iUP8FqQeN1ofUWeSZAuvsfgsLCmQDToy4wJXnEurXlR6VVRKdEYZ6cz1VZtjw00IWMZrpKb';

// App configuration
const config = {
  API_URL,
  STRIPE_PUBLISHABLE_KEY,
  isDevelopment,
  // Set to false to use real data from the backend
  useMockData: false, // Disable mock data to use real database
  // App settings
  defaultLanguage: 'ar',
  supportedLanguages: ['ar', 'en'],
  apiTimeout: 30000, // 30 seconds
  profileCompletionMinimum: 70, // Percentage required to be considered "complete"
  maxPhotos: 6, // Maximum number of photos allowed per user
  requestTimeoutMs: 30000, // 30 seconds (matching apiTimeout)
};

export default config;
