/**
 * Global utilities and configurations
 * This file is imported first in the application to ensure global utilities are available
 */

import { I18nManager, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// RTL utilities for the application
export const rtlUtils = {
  // Check if the current layout is RTL
  get isRTL() {
    return I18nManager.isRTL;
  },

  // Set RTL direction
  setRTL: (enable) => {
    // Only update if the current setting is different
    if (I18nManager.isRTL !== enable) {
      console.log(`Setting RTL to: ${enable}`);
      I18nManager.allowRTL(enable);
      I18nManager.forceRTL(enable);

      // Log the result
      console.log(`RTL settings updated, I18nManager.isRTL is now: ${I18nManager.isRTL}`);
    }
    return I18nManager.isRTL;
  },

  // Get RTL direction based on language
  getRTLDirection: (language) => {
    return language === 'ar';
  },

  // Get text alignment based on RTL
  getTextAlign: () => {
    return I18nManager.isRTL ? 'right' : 'left';
  },

  // Get flex direction based on RTL
  getFlexDirection: () => {
    return I18nManager.isRTL ? 'row-reverse' : 'row';
  },

  // Get start padding/margin based on RTL
  getPaddingStart: (value) => {
    return I18nManager.isRTL ? { paddingRight: value } : { paddingLeft: value };
  },

  // Get end padding/margin based on RTL
  getPaddingEnd: (value) => {
    return I18nManager.isRTL ? { paddingLeft: value } : { paddingRight: value };
  },

  // Get margin start based on RTL
  getMarginStart: (value) => {
    return I18nManager.isRTL ? { marginRight: value } : { marginLeft: value };
  },

  // Get margin end based on RTL
  getMarginEnd: (value) => {
    return I18nManager.isRTL ? { marginLeft: value } : { marginRight: value };
  },

  // Get border radius for RTL
  getBorderRadius: (topLeft, topRight, bottomRight, bottomLeft) => {
    return I18nManager.isRTL
      ? {
          borderTopLeftRadius: topRight,
          borderTopRightRadius: topLeft,
          borderBottomRightRadius: bottomLeft,
          borderBottomLeftRadius: bottomRight,
        }
      : {
          borderTopLeftRadius: topLeft,
          borderTopRightRadius: topRight,
          borderBottomRightRadius: bottomRight,
          borderBottomLeftRadius: bottomLeft,
        };
  }
};

// Make rtlUtils available globally
global.rtlUtils = rtlUtils;

// Platform detection utilities
export const platformUtils = {
  isAndroid: Platform.OS === 'android',
  isIOS: Platform.OS === 'ios',
  isWeb: Platform.OS === 'web',

  // Get platform-specific value
  select: (config) => Platform.select(config)
};

// Make platformUtils available globally
global.platformUtils = platformUtils;

// Initialize global settings
export const initGlobalSettings = async () => {
  try {
    // Get the saved language
    const savedLanguage = await AsyncStorage.getItem('user-language');
    const isRTL = savedLanguage ? savedLanguage === 'ar' : true; // Default to RTL if no saved language

    console.log(`Initializing global settings with language: ${savedLanguage || 'ar'}, isRTL: ${isRTL}`);

    // Set RTL based on language
    rtlUtils.setRTL(isRTL);

    // Verify RTL settings
    console.log(`After initialization, I18nManager.isRTL is: ${I18nManager.isRTL}`);

    return { language: savedLanguage || 'ar', isRTL: I18nManager.isRTL };
  } catch (error) {
    console.error('Error initializing global settings:', error);
    // Default to Arabic/RTL in case of error
    rtlUtils.setRTL(true);
    return { language: 'ar', isRTL: I18nManager.isRTL };
  }
};

// Initialize global settings on import
initGlobalSettings().catch(err => {
  console.warn('Failed to initialize global settings:', err);
});

// Export a simple rtl object for backward compatibility
export const rtl = {
  isRTL: I18nManager.isRTL,
  direction: I18nManager.isRTL ? 'rtl' : 'ltr'
};

// Make rtl available globally
global.rtl = rtl;

export default {
  rtlUtils,
  platformUtils,
  rtl
};
