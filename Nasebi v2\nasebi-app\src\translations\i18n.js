import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Localization from 'expo-localization';
import { I18nManager } from 'react-native';

// Translations
import en_flat from './en_flat.json';
import ar_flat from './ar_flat.json';

const LANGUAGES = {
  en: { translation: en_flat },
  ar: { translation: ar_flat },
};

const LANG_CODES = Object.keys(LANGUAGES);

const LANGUAGE_DETECTOR = {
  type: 'languageDetector',
  async: true,
  detect: async callback => {
    try {
      console.log('i18n: Detecting language...');

      // First try to get the saved language preference
      const savedLanguage = await AsyncStorage.getItem('user-language');
      console.log('i18n: Saved language from AsyncStorage:', savedLanguage);

      if (savedLanguage && LANG_CODES.includes(savedLanguage)) {
        console.log('i18n: Using saved language:', savedLanguage);
        return callback(savedLanguage);
      }

      // If no saved preference, try to detect device language
      const deviceLocale = Localization.locale.split('-')[0];
      console.log('i18n: Device locale:', deviceLocale);

      // Use the device language if it's supported, otherwise default to Arabic
      if (LANG_CODES.includes(deviceLocale)) {
        console.log(`i18n: Using device language: ${deviceLocale}`);
        return callback(deviceLocale);
      }

      // Default to Arabic if no saved language and device language not supported
      console.log('i18n: No saved language found, defaulting to Arabic');
      callback('ar');
    } catch (error) {
      console.error('i18n: Error detecting language:', error);
      console.log('i18n: Error occurred, defaulting to Arabic');
      callback('ar');
    }
  },
  init: () => {
    console.log('i18n: Language detector initialized');
  },
  cacheUserLanguage: async language => {
    try {
      console.log('i18n: Caching language:', language);
      await AsyncStorage.setItem('user-language', language);

      // Update RTL setting based on language
      const isRTL = language === 'ar';
      if (I18nManager.isRTL !== isRTL) {
        I18nManager.allowRTL(isRTL);
        I18nManager.forceRTL(isRTL);
      }

      console.log('i18n: Language cached successfully');
    } catch (error) {
      console.error('i18n: Error caching language:', error);
    }
  },
};

console.log('i18n: Initializing i18next...');

i18n
  .use(LANGUAGE_DETECTOR)
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v3',
    resources: LANGUAGES,
    lng: 'ar', // Default to Arabic
    fallbackLng: 'ar',
    react: {
      useSuspense: false,
    },
    interpolation: {
      escapeValue: false,
    }
  }, (err) => {
    if (err) {
      console.error('i18n: Error initializing i18next:', err);
    } else {
      console.log('i18n: i18next initialized successfully');
      console.log('i18n: Current language:', i18n.language);
      console.log('i18n: Available languages:', Object.keys(LANGUAGES));
      console.log('i18n: Arabic translation sample:', LANGUAGES.ar.translation.appName);
    }
  });

// Export i18n instance
export { i18n };
export default i18n;