import Constants from 'expo-constants';
import { Platform } from 'react-native';

// DIRECT SOLUTION: Use the actual IP address of your computer
// This ensures consistent access from both emulators and physical devices
const DIRECT_IP = '*************'; // Your actual IP address from ipconfig
let API_URL = `http://${DIRECT_IP}:3000`;

// Log the configured API URL and platform
console.log(`Platform: ${Platform.OS}`);
console.log(`Using direct IP address: ${DIRECT_IP}`);
console.log(`API URL configured as: ${API_URL}`);

// Override with environment variables if available
if (Constants.expoConfig?.extra?.apiUrl) {
  API_URL = Constants.expoConfig.extra.apiUrl;
  console.log('Overriding API URL from environment:', API_URL);
}

// Environment settings
const isDevelopment = process.env.NODE_ENV === 'development' || __DEV__;

// Stripe configuration
const STRIPE_PUBLISHABLE_KEY =
  Constants.expoConfig?.extra?.stripePublishableKey ||
  'pk_test_51OpGOZFzxLuwKT1JYzL55vTzxr8iUP8FqQeN1ofUWeSZAuvsfgsLCmQDToy4wJXnEurXlR6VVRKdEYZ6cz1VZtjw00IWMZrpKb';

// Log the Stripe publishable key for debugging
console.log('Using Stripe publishable key:', STRIPE_PUBLISHABLE_KEY);

// App configuration
const config = {
  API_URL,
  STRIPE_PUBLISHABLE_KEY,
  isDevelopment,
  // Set to false to use real data from the backend
  useMockData: false, // Disable mock data to use real database
  // App settings
  defaultLanguage: 'ar',
  supportedLanguages: ['ar', 'en'],
  apiTimeout: 30000, // 30 seconds
  profileCompletionMinimum: 70, // Percentage required to be considered "complete"
  maxPhotos: 6, // Maximum number of photos allowed per user
  requestTimeoutMs: 30000, // 30 seconds (matching apiTimeout)
};

export default config;