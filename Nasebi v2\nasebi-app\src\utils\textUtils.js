/**
 * Text formatting utilities for Nasebi
 */

/**
 * Removes periods and any words before periods from text
 * @param {string} text - The text to process
 * @returns {string} - The processed text without periods and words before them
 */
export const removePeriodAndWordsBefore = (text) => {
  if (!text) return '';

  // Remove any word followed by a period and the period itself
  return text.replace(/\S+\.\s*/g, '');
};

/**
 * Removes the "subscription." prefix from translation keys
 * @param {string} key - The translation key
 * @param {Function} t - The translation function
 * @returns {string} - The translated text without the subscription prefix
 */
export const removeSubscriptionPrefix = (key, t) => {
  if (!key || !t) return '';

  // Get the translated text
  const translatedText = t(key);

  // Return the translated text
  return translatedText;
};

/**
 * Formats text based on language
 * @param {string} text - The text to format
 * @param {string} language - The language code ('en' or 'ar')
 * @param {boolean} isRTL - Whether the text direction is RTL
 * @returns {string} - The formatted text
 */
export const formatTextByLanguage = (text, language, isRTL) => {
  if (!text) return '';

  // Make sure we have a string
  const textStr = String(text);

  if (language === 'en') {
    // For English, remove periods and words before them
    return removePeriodAndWordsBefore(textStr);
  } else if (language === 'ar') {
    // For Arabic, ensure proper RTL formatting
    // We don't modify the Arabic text content, just ensure it's displayed correctly
    // with RTL direction in the component
    return textStr;
  }

  return textStr;
};

export default {
  removePeriodAndWordsBefore,
  removeSubscriptionPrefix,
  formatTextByLanguage
};
