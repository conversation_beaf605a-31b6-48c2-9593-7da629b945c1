import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Slider from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';
import api from '../../services/api';
import { useTheme } from '../../context/ThemeContext';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { useLanguage } from '../../context/LanguageContext';
import { useRTL } from '../../hooks/useRTL';
import { countries } from '../../data/countries';
import { skinColors } from '../../data/skinColors';
import CustomDropdown from '../../components/CustomDropdown';
import CountryCityDropdown from '../../components/CountryCityDropdown';

// Import dropdown options from the flat file
import * as DropdownOptions from '../../data/dropdownOptions_flat';

const PreferencesScreen = ({ navigation }) => {
  const { colors, isDark } = useTheme();
  const { t } = useTranslationFlat();
  const { isRTL } = useLanguage();
  const rtl = useRTL();

  const [preferences, setPreferences] = useState({
    minAge: 18,
    maxAge: 50,
    preferredHeightMin: 150,
    preferredHeightMax: 200,
    preferredLocations: '',
    maxDistance: 50,
    preferredNationalities: '',
    preferredEthnicities: '',
    preferredEducationLevels: '',
    preferredOccupations: '',
    preferredMaritalStatus: '',
    acceptChildren: false,
    preferredReligiousLevel: '',
    preferredPrayerLevel: '',
    preferredReligiousPractices: '',
    preferredHijabStatus: '',
    dealBreakers: '',
    preferredSkinColors: '',
    preferredLanguages: '',
    acceptSmoker: false,
    preferredCountry: '',
    preferredCity: '',
  });
  const [loading, setLoading] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState('');

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const response = await api.get('/api/matches/preferences');
      const data = response.data || preferences;
      setPreferences(data);

      // Set the selected country for city picker
      if (data.preferredCountry) {
        setSelectedCountry(data.preferredCountry);
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      await api.post('/api/matches/preferences', preferences);
      navigation.goBack();
    } catch (error) {
      console.error('Error saving preferences:', error);
      Alert.alert(t('error'), t('failedToSavePreferences'));
    } finally {
      setLoading(false);
    }
  };

  // We've replaced these helper functions with our custom dropdown components

  const renderInput = (label, field, placeholder, keyboardType = 'default', multiline = false) => (
    <View style={styles.inputContainer}>
      <Text style={[styles.label, { color: colors.text }]}>{label}</Text>
      <TextInput
        style={[
          styles.input,
          multiline && styles.textArea,
          {
            backgroundColor: colors.card,
            color: colors.text,
            borderColor: colors.border
          }
        ]}
        value={preferences[field]?.toString()}
        onChangeText={(text) => setPreferences(prev => ({ ...prev, [field]: text }))}
        placeholder={placeholder}
        placeholderTextColor={colors.subtext}
        keyboardType={keyboardType}
        multiline={multiline}
      />
    </View>
  );

  const renderSwitch = (label, field) => (
    <View style={styles.switchContainer}>
      <Text style={[styles.label, { color: colors.text }]}>{label}</Text>
      <Switch
        value={preferences[field]}
        onValueChange={(value) => setPreferences(prev => ({ ...prev, [field]: value }))}
        trackColor={{ false: colors.border, true: colors.primary }}
        thumbColor={preferences[field] ? (isDark ? '#fff' : '#fff') : '#f4f3f4'}
      />
    </View>
  );

  const renderRangeSlider = (label, minField, maxField, min, max, step = 1) => (
    <View style={styles.sliderContainer}>
      <Text style={[styles.label, { color: colors.text }]}>{label}</Text>
      <Text style={[styles.rangeText, { color: colors.subtext }]}>
        {preferences[minField]} - {preferences[maxField]}
      </Text>
      <View style={styles.slidersWrapper}>
        <Slider
          style={styles.slider}
          minimumValue={min}
          maximumValue={max}
          step={step}
          value={preferences[minField]}
          onValueChange={(value) => setPreferences(prev => ({ ...prev, [minField]: value }))}
          minimumTrackTintColor={colors.primary}
          maximumTrackTintColor={colors.border}
          thumbTintColor={colors.primary}
        />
        <Slider
          style={styles.slider}
          minimumValue={min}
          maximumValue={max}
          step={step}
          value={preferences[maxField]}
          onValueChange={(value) => setPreferences(prev => ({ ...prev, [maxField]: value }))}
          minimumTrackTintColor={colors.primary}
          maximumTrackTintColor={colors.border}
          thumbTintColor={colors.primary}
        />
      </View>
    </View>
  );

  const renderSingleSlider = (label, field, min, max, step = 1) => (
    <View style={styles.sliderContainer}>
      <Text style={[styles.label, { color: colors.text }]}>{label}</Text>
      <Text style={[styles.rangeText, { color: colors.subtext }]}>
        {preferences[field]} {field === 'maxDistance' ? t('km') : ''}
      </Text>
      <View style={styles.slidersWrapper}>
        <Slider
          style={styles.slider}
          minimumValue={min}
          maximumValue={max}
          step={step}
          value={preferences[field]}
          onValueChange={(value) => setPreferences(prev => ({ ...prev, [field]: value }))}
          minimumTrackTintColor={colors.primary}
          maximumTrackTintColor={colors.border}
          thumbTintColor={colors.primary}
        />
      </View>
    </View>
  );

  // Render custom dropdown component
  const renderDropdown = (label, field, options, placeholder = '') => (
    <CustomDropdown
      label={label}
      options={options}
      selectedValue={preferences[field]}
      onValueChange={(value) => setPreferences(prev => ({ ...prev, [field]: value }))}
      placeholder={placeholder}
    />
  );

  // Render country and city dropdowns
  const renderCountryCityDropdowns = () => (
    <CountryCityDropdown
      selectedCountry={selectedCountry}
      selectedCity={preferences.preferredCity}
      onCountryChange={(value) => {
        setSelectedCountry(value);
        setPreferences(prev => ({ ...prev, preferredCountry: value, preferredCity: '' }));
      }}
      onCityChange={(value) => setPreferences(prev => ({ ...prev, preferredCity: value }))}
    />
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView>
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <Text style={[styles.title, { color: colors.text }]}>{t('preferences')}</Text>
        </View>

        <View style={styles.form}>
          {renderRangeSlider(t('ageRange'), 'minAge', 'maxAge', 18, 80)}
          {renderRangeSlider(t('heightRange') + ' (cm)', 'preferredHeightMin', 'preferredHeightMax', 140, 220)}
          {renderSingleSlider(t('maxDistance') + ' (km)', 'maxDistance', 10, 500, 10)}

          <View style={styles.section}>
            <Text style={[styles.title, { color: colors.text, borderBottomColor: colors.border }]}>
              {t('locationPreferences')}
            </Text>
            {renderCountryCityDropdowns()}
            {renderDropdown(t('preferredNationalities'), 'preferredNationalities', countries, t('selectCountry'))}
            {renderDropdown(t('preferredSkinColors'), 'preferredSkinColors', skinColors, t('selectSkinColor'))}
          </View>

          <View style={styles.section}>
            <Text style={[styles.title, { color: colors.text, borderBottomColor: colors.border }]}>
              {t('professionalPreferences')}
            </Text>
            {renderDropdown(t('educationLevels'), 'preferredEducationLevels', DropdownOptions.EDUCATION_LEVELS, t('enterPreferredEducationLevels'))}
            {renderInput(t('occupations'), 'preferredOccupations', t('enterPreferredOccupations'), 'default', true)}
          </View>

          <View style={styles.section}>
            <Text style={[styles.title, { color: colors.text, borderBottomColor: colors.border }]}>
              {t('personalStatus')}
            </Text>
            {renderDropdown(t('maritalStatus'), 'preferredMaritalStatus', DropdownOptions.MARITAL_STATUS, t('enterPreferredMaritalStatus'))}
            {renderSwitch(t('acceptPartnersWithChildren'), 'acceptChildren')}
            {renderSwitch(t('acceptSmokers'), 'acceptSmoker')}
          </View>

          <View style={styles.section}>
            <Text style={[styles.title, { color: colors.text, borderBottomColor: colors.border }]}>
              {t('religiousPreferences')}
            </Text>
            {renderDropdown(t('religiousLevel'), 'preferredReligiousLevel', DropdownOptions.RELIGIOUS_LEVELS, t('enterPreferredReligiousLevel'))}
            {renderDropdown(t('prayerLevel'), 'preferredPrayerLevel', DropdownOptions.PRAYER_LEVELS, t('enterPreferredPrayerLevel'))}
            {renderDropdown(t('fastingFrequency'), 'preferredFastingLevel', DropdownOptions.FASTING_LEVELS, t('enterPreferredFastingLevel'))}
            {renderDropdown(t('hajjStatus'), 'preferredHajjStatus', DropdownOptions.HAJJ_STATUS, t('enterPreferredHajjStatus'))}
            {renderDropdown(t('preferredLanguages'), 'preferredLanguages', DropdownOptions.LANGUAGES, t('enterPreferredLanguages'))}
          </View>

          <View style={styles.section}>
            <Text style={[styles.title, { color: colors.text, borderBottomColor: colors.border }]}>
              {t('dealBreakers')}
            </Text>
            {renderInput(t('dealBreakers'), 'dealBreakers', t('enterDealBreakers'), 'default', true)}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton, { borderColor: colors.border }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={[styles.cancelButtonText, { color: colors.text }]}>{t('cancel')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.button,
              styles.saveButton,
              { backgroundColor: colors.primary },
              loading && styles.disabledButton
            ]}
            onPress={handleSave}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <Text style={styles.saveButtonText}>
                {t('save')}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  form: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  title: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 15,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    height: 45,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    height: 45,
  },
  sliderContainer: {
    marginBottom: 24,
  },
  rangeText: {
    fontSize: 14,
    marginBottom: 8,
  },
  slidersWrapper: {
    marginVertical: 8,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  buttonContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    padding: 16,
    paddingBottom: 32,
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    height: 48,
  },
  saveButton: {
    // backgroundColor is set dynamically using colors.primary
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default PreferencesScreen;