/**
 * <PERSON><PERSON><PERSON> to test the translation system
 * 
 * This script helps test the translation system by checking if all keys in the translation files
 * are properly defined and accessible.
 */

const fs = require('fs');
const path = require('path');

// Load translation files
const enFlatPath = path.join(__dirname, '..', 'src', 'translations', 'en_flat.json');
const arFlatPath = path.join(__dirname, '..', 'src', 'translations', 'ar_flat.json');

try {
  const enFlat = JSON.parse(fs.readFileSync(enFlatPath, 'utf8'));
  const arFlat = JSON.parse(fs.readFileSync(arFlatPath, 'utf8'));

  console.log(`=== Translation System Test ===\n`);
  
  // Count keys
  const enKeys = Object.keys(enFlat);
  const arKeys = Object.keys(arFlat);
  
  console.log(`English keys: ${enKeys.length}`);
  console.log(`Arabic keys: ${arKeys.length}`);
  
  // Find missing keys
  const missingInAr = enKeys.filter(key => !arFlat[key]);
  const missingInEn = arKeys.filter(key => !enFlat[key]);
  
  if (missingInAr.length > 0) {
    console.log(`\nKeys missing in Arabic translation (${missingInAr.length}):`);
    missingInAr.forEach(key => console.log(`- ${key}`));
  } else {
    console.log(`\nAll English keys are present in Arabic translation.`);
  }
  
  if (missingInEn.length > 0) {
    console.log(`\nKeys missing in English translation (${missingInEn.length}):`);
    missingInEn.forEach(key => console.log(`- ${key}`));
  } else {
    console.log(`\nAll Arabic keys are present in English translation.`);
  }
  
  // Check for empty values
  const emptyInEn = enKeys.filter(key => enFlat[key] === '');
  const emptyInAr = arKeys.filter(key => arFlat[key] === '');
  
  if (emptyInEn.length > 0) {
    console.log(`\nEmpty values in English translation (${emptyInEn.length}):`);
    emptyInEn.forEach(key => console.log(`- ${key}`));
  } else {
    console.log(`\nNo empty values in English translation.`);
  }
  
  if (emptyInAr.length > 0) {
    console.log(`\nEmpty values in Arabic translation (${emptyInAr.length}):`);
    emptyInAr.forEach(key => console.log(`- ${key}`));
  } else {
    console.log(`\nNo empty values in Arabic translation.`);
  }
  
  console.log(`\n=== Test Complete ===`);
} catch (error) {
  console.error('Error testing translations:', error);
}
