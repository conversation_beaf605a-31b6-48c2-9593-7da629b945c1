<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\main\res"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\build\generated\res\rs\release"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\main\res"><file name="splashscreen" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\main\res\drawable\splashscreen.xml" qualifiers="" type="drawable"/><file name="splashscreen_image" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\main\res\drawable\splashscreen_image.png" qualifiers="" type="drawable"/><file path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\main\res\values\colors_splashscreen.xml" qualifiers=""><color name="splashscreen_background">#FFFFFF</color></file><file path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\main\res\values\strings.xml" qualifiers=""><string name="expo_splash_screen_resize_mode" translatable="false">contain</string><string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string></file><file path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\main\res\values\styles_splashscreen.xml" qualifiers=""><style name="Theme.App.SplashScreen" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:windowBackground">@drawable/splashscreen</item>
    <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    <item name="android:statusBarColor">@android:color/transparent</item>
  </style></file></source><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\build\generated\res\rs\release"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\expo-splash-screen\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>