#Mon Jun 09 11:59:59 GST 2025
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_no_animation_250.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_no_animation_250.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_out_to_right.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_slide_out_to_right.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_fade_from_bottom.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_fade_from_bottom.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_out_to_bottom.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_slide_out_to_bottom.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_default_enter_out.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_default_enter_out.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_no_animation_350.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_no_animation_350.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_fade_out.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_fade_out.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_fade_in.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_fade_in.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_in_from_left.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_slide_in_from_left.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_default_exit_in.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_default_exit_in.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_in_from_right.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_slide_in_from_right.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_no_animation_20.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_no_animation_20.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_in_from_bottom.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_slide_in_from_bottom.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_default_exit_out.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_default_exit_out.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_fade_to_bottom.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_fade_to_bottom.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_slide_out_to_left.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_slide_out_to_left.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_default_enter_in.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_default_enter_in.xml
com.swmansion.rnscreens.react-native-screens-main-6\:/anim/rns_no_animation_medium.xml=C\:\\Users\\elabd\\Desktop\\Nasebi v2\\nasebi-app\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\release\\anim\\rns_no_animation_medium.xml
