import { toArabicNumbers } from './arabicUtils';

/**
 * Format currency value with AED symbol
 * @param {number} value - The value to format
 * @param {string} language - The language code ('en' or 'ar')
 * @param {boolean} showSymbol - Whether to show the currency symbol
 * @returns {string} - The formatted currency string
 */
export const formatCurrency = (value, language = 'en', showSymbol = true) => {
  if (value === undefined || value === null) {
    return showSymbol ? 'AED 0.00' : '0.00';
  }

  // Format the number with 2 decimal places
  const formattedValue = parseFloat(value).toFixed(2);
  
  // Convert to Arabic numbers if needed
  const formattedNumber = language === 'ar' ? toArabicNumbers(formattedValue) : formattedValue;
  
  // Return with or without symbol
  if (showSymbol) {
    return language === 'ar' ? `${formattedNumber} د.إ` : `AED ${formattedNumber}`;
  } else {
    return formattedNumber;
  }
};

/**
 * Format a number with commas for thousands
 * @param {number} value - The value to format
 * @param {string} language - The language code ('en' or 'ar')
 * @returns {string} - The formatted number string
 */
export const formatNumber = (value, language = 'en') => {
  if (value === undefined || value === null) {
    return '0';
  }

  // Format with commas
  const parts = value.toString().split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  const formattedValue = parts.join('.');
  
  // Convert to Arabic numbers if needed
  return language === 'ar' ? toArabicNumbers(formattedValue) : formattedValue;
};
