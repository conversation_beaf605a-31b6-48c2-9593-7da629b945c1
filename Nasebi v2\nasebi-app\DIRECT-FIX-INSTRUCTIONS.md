# Direct Fix for Authentication API Connection Issues

This document provides a direct, step-by-step solution to fix the login and registration API connection issues in the Nasebi app.

## Problem Description

The mobile app is experiencing connection issues when trying to connect to the backend API server for authentication. The error message is:

```
timeout of 30000ms exceeded
```

The app is trying to connect to `http://********:3000` but the connection is timing out.

## Solution: Direct IP Connection

The solution is to modify the mobile app to use the direct IP address of your computer instead of the special Android emulator IP address.

### Step 1: Find Your Computer's IP Address

Run the following command in PowerShell to find your computer's IP address:

```powershell
ipconfig
```

Look for the IPv4 Address under your active network adapter (e.g., Wi-Fi or Ethernet). It should look like `************`.

### Step 2: Update the Config File

Open the file `nasebi-app/src/config/config.js` and replace the beginning of the file with:

```javascript
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// IMPORTANT: Use direct IP address for all API requests
// Replace with your actual IP address
const API_URL = 'http://YOUR_IP_ADDRESS:3000';

console.log('Using direct IP address for API:', API_URL);
console.log('Platform:', Platform.OS);
```

Replace `YOUR_IP_ADDRESS` with your actual IP address (e.g., `************`).

### Step 3: Update the API Service

Open the file `nasebi-app/src/services/api.js` and replace the beginning of the file with:

```javascript
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import config from '../config/config';

// IMPORTANT: Use direct IP address for all API requests
// Replace with your actual IP address
const API_URL = 'http://YOUR_IP_ADDRESS:3000';

console.log(`Using direct IP address for API: ${API_URL}`);
console.log('Platform:', Platform.OS);

// Use the mock data flag from config
const useMockData = config.useMockData;
console.log(`Mock data mode: ${useMockData ? 'ENABLED' : 'DISABLED'}`);

// Create axios instance with direct IP
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: config.apiTimeout,
});
```

Replace `YOUR_IP_ADDRESS` with your actual IP address (e.g., `************`).

### Step 4: Update the Fallback Mechanism

In the same file (`nasebi-app/src/services/api.js`), find the fallback API creation code and replace it with:

```javascript
// Create a secondary instance with the same direct IP for fallback
// This ensures consistent behavior across platforms
const fallbackApi = axios.create({
  baseURL: API_URL, // Use the same direct IP
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 60000, // 60 seconds for fallback attempts
});
```

### Step 5: Simplify the Retry Logic

In the same file (`nasebi-app/src/services/api.js`), find the retry logic in the response interceptor and replace it with:

```javascript
// Simplified retry logic with direct IP
if ((error.message === 'Network Error' || error.code === 'ECONNABORTED') && error.config) {
  const originalRequest = error.config;
  
  console.log('Network error detected, retrying with increased timeout');
  
  try {
    // Create a new config with increased timeout
    const retryConfig = { 
      ...originalRequest,
      timeout: 60000 // 60 seconds
    };
    
    console.log(`Retrying request to: ${API_URL}${originalRequest.url}`);
    return await axios(retryConfig);
  } catch (retryError) {
    console.log('Retry failed:', retryError.message);
  }
}
```

### Step 6: Open Windows Firewall

Run the following command as administrator to open the firewall:

```
netsh advfirewall firewall add rule name="Nasebi API Server (3000)" dir=in action=allow protocol=TCP localport=3000
```

### Step 7: Restart the Mobile App

Restart the mobile app to apply the changes.

## Verification

After implementing the solution:

1. Check the app logs to verify it's using the direct IP address
2. Test the login functionality with the following credentials:
   - Email: <EMAIL>
   - Password: password123
3. Test the registration functionality with a new email address
4. Verify that other API endpoints work after authentication

## Automated Fix

For convenience, you can also use the provided script to automatically apply the fix:

1. Open the file `nasebi-app/fix-auth-api.js`
2. Update the `YOUR_IP_ADDRESS` constant with your actual IP address
3. Run the script with Node.js:
   ```
   node fix-auth-api.js
   ```

## Troubleshooting

If you're still experiencing issues:

1. **Check your IP address**: Make sure you're using the correct IP address of your computer.

2. **Check if the server is running**: Make sure the backend server is running and accessible at:
   ```
   http://localhost:3000/api/health
   ```

3. **Check firewall settings**: Make sure Windows Firewall is allowing incoming connections to port 3000.

4. **Try a different network**: If possible, try connecting both your development machine and mobile device/emulator to the same network, such as a mobile hotspot.

5. **Test with a physical device**: If possible, test with a physical device connected to the same network as your development machine.

## Alternative Solutions

If the direct IP solution doesn't work, try these alternatives:

### Option 1: Use ngrok

1. Install ngrok:
   ```
   npm install -g ngrok
   ```

2. Expose your local server:
   ```
   ngrok http 3000
   ```

3. Use the ngrok URL in your app:
   ```javascript
   const API_URL = 'https://your-ngrok-url.ngrok.io';
   ```

### Option 2: Use a Local Web Server

1. Install a local web server like XAMPP or WAMP
2. Configure it to proxy requests to your Node.js server
3. Use the web server's IP address in your app

## Conclusion

The authentication API connection issue is caused by connectivity problems between the Android emulator and the host machine. By using the direct IP address of your computer, we bypass this issue and ensure reliable connections for all API requests.

This solution is simple, effective, and doesn't require any changes to the backend server.
