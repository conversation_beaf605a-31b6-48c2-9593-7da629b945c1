// API URLs for different environments
export const DEV_API_URL = 'http://localhost:3000';  // Local development
export const STAGING_API_URL = 'https://api-staging.nasebi.com';
export const PROD_API_URL = 'https://api.nasebi.com';

// Set the current environment
const ENV = process.env.NODE_ENV || 'development';

// Set the appropriate API URL based on environment
export const API_URL =
  ENV === 'production' ? PROD_API_URL :
  ENV === 'staging' ? STAGING_API_URL :
  DEV_API_URL;

// App-wide constants
export const APP_VERSION = '1.0.0';
export const APP_BUNDLE_ID = 'com.nasebi.app';
export const APP_STORE_URL = 'https://apps.apple.com/app/nasebi/id123456789';
export const PLAY_STORE_URL = 'https://play.google.com/store/apps/details?id=com.nasebi.app';
export const SUPPORT_EMAIL = '<EMAIL>';
export const PRIVACY_POLICY_URL = 'https://nasebi.com/privacy';
export const TERMS_URL = 'https://nasebi.com/terms';

// Default settings
export const DEFAULT_LANGUAGE = 'ar';
export const SUPPORTED_LANGUAGES = ['ar', 'en'];
export const CACHE_TTL = 86400000; // 24 hours in milliseconds
export const MAX_PHOTOS = 6;
export const MIN_PASSWORD_LENGTH = 8;
export const MAX_BIO_LENGTH = 500;

// Distances (in km)
export const DEFAULT_SEARCH_RADIUS = 50;
export const MAX_SEARCH_RADIUS = 200;

// User Ages
export const MIN_USER_AGE = 18;
export const MAX_USER_AGE = 65;
export const DEFAULT_AGE_RANGE = { min: 18, max: 45 };

// Subscription Plans
export const SUBSCRIPTION_PLANS = {
  MONTHLY: {
    id: 'monthly',
    price: 9.99,
    duration: 1,
    durationUnit: 'month'
  },
  QUARTERLY: {
    id: 'quarterly',
    price: 24.99,
    duration: 3,
    durationUnit: 'months'
  },
  YEARLY: {
    id: 'yearly',
    price: 79.99,
    duration: 12,
    durationUnit: 'months'
  }
};

// Image upload constraints
export const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
export const THUMBNAIL_SIZE = { width: 200, height: 200 };
export const PROFILE_IMAGE_SIZE = { width: 500, height: 500 };

// Messaging constants
export const MESSAGES_LOAD_COUNT = 30;
export const TYPING_TIMEOUT = 3000; // 3 seconds

// Animation durations (in ms)
export const FADE_ANIMATION_DURATION = 300;
export const SLIDE_ANIMATION_DURATION = 400;