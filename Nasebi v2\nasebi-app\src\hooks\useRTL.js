import { useContext } from 'react';
import { I18nManager } from 'react-native';
import { useLanguage } from '../context/LanguageContext';

/**
 * Custom hook to handle RTL layout
 *
 * @returns {Object} - RTL utilities
 */
export const useRTL = () => {
  const { isRTL: languageIsRTL } = useLanguage();

  // Use language context's RTL value or fallback to I18nManager
  const isRTL = languageIsRTL !== undefined ? languageIsRTL : I18nManager.isRTL;

  // RTL-aware styles and utilities
  return {
    isRTL,
    direction: isRTL ? 'rtl' : 'ltr',
    textAlign: isRTL ? 'right' : 'left',
    flexDirection: isRTL ? 'row-reverse' : 'row',
    align: {
      textAlign: isRTL ? 'right' : 'left',
    },
    margin: (start, top, end, bottom) => ({
      marginTop: top,
      marginBottom: bottom,
      marginStart: start,
      marginEnd: end,
    }),
    padding: (start, top, end, bottom) => ({
      paddingTop: top,
      paddingBottom: bottom,
      paddingStart: start,
      paddingEnd: end,
    }),
    position: (start) => ({
      [isRTL ? 'right' : 'left']: start,
    }),
    positionAbsolute: (top, end, bottom, start) => ({
      position: 'absolute',
      top,
      bottom,
      [isRTL ? 'right' : 'left']: start,
      [isRTL ? 'left' : 'right']: end,
    }),
    iconTransform: {
      transform: [{ scaleX: isRTL ? -1 : 1 }],
    },
    // Border radius for RTL
    borderRadius: (topLeft, topRight, bottomRight, bottomLeft) => ({
      borderTopLeftRadius: isRTL ? topRight : topLeft,
      borderTopRightRadius: isRTL ? topLeft : topRight,
      borderBottomRightRadius: isRTL ? bottomLeft : bottomRight,
      borderBottomLeftRadius: isRTL ? bottomRight : bottomLeft,
    }),
    // Padding for RTL
    paddingHorizontal: (start, end) => ({
      paddingLeft: isRTL ? end : start,
      paddingRight: isRTL ? start : end,
    }),
    // Margin for RTL
    marginHorizontal: (start, end) => ({
      marginLeft: isRTL ? end : start,
      marginRight: isRTL ? start : end,
    }),
  };
};

export default useRTL;