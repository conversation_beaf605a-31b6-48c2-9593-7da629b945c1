import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Dimensions,
  RefreshControl,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../context/AuthContext';
import api from '../../services/api';
import { useTheme } from '../../context/ThemeContext';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import BlurredProfileImage from '../../components/BlurredProfileImage';
import ScrollableProfileCard from '../../components/ScrollableProfileCard';
import { useRTL } from '../../hooks/useRTL';


const { width } = Dimensions.get('window');
const MATCH_ITEM_WIDTH = width / 3 - 16;

const MatchesScreen = ({ navigation }) => {
  const rtl = useRTL();

  const { t } = useTranslationFlat();
  const { colors, isDark } = useTheme();
  const { user } = useAuth();
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(true);
  // Set 'liked' as the default tab as requested
  const [activeTab, setActiveTab] = useState('liked');
  const [refreshing, setRefreshing] = useState(false);
  const [likedYou, setLikedYou] = useState([]);
  const [visitedProfiles, setVisitedProfiles] = useState([]);
  const [likedProfiles, setLikedProfiles] = useState([]);
  const [selectedMatch, setSelectedMatch] = useState(null);
  const [showProfileModal, setShowProfileModal] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    let retryCount = 0;
    const maxRetries = 3;

    const attemptFetch = async () => {
      try {
        console.log(`Fetching matches data... (Attempt ${retryCount + 1}/${maxRetries})`);

        // Fetch matches
        try {
          const matchesResponse = await api.get('/api/matches');
          console.log('Matches response:', matchesResponse.data);

          // Handle different response structures
          let matchesData = [];
          if (Array.isArray(matchesResponse.data)) {
            matchesData = matchesResponse.data;
          } else if (matchesResponse.data && Array.isArray(matchesResponse.data.matches)) {
            matchesData = matchesResponse.data.matches;
          } else if (matchesResponse.data && Array.isArray(matchesResponse.data.data)) {
            matchesData = matchesResponse.data.data;
          }

          // Process matches data to ensure consistent structure
          const processedMatches = matchesData.map(match => ({
            id: match.id || match.match_id || match.matchId,
            userId: match.user_id || match.userId || match.id,
            name: match.name || t('common.unknown'),
            age: match.age || '30',
            image: match.image || match.photo || match.profile_photo || match.avatar || 'https://randomuser.me/api/portraits/men/1.jpg',
            lastMessage: match.lastMessage || null,
            premium: match.premium || false
          }));

          setMatches(processedMatches);
        } catch (matchesError) {
          console.log('Error fetching matches:', matchesError);
          // Use empty array if endpoint fails
          setMatches([]);
        }

        // Fetch likes received
        try {
          const likesResponse = await api.get('/api/likes/received');
          console.log('Likes received response:', likesResponse.data);

          // Handle different response structures
          let likesData = [];
          if (Array.isArray(likesResponse.data)) {
            likesData = likesResponse.data;
          } else if (likesResponse.data && Array.isArray(likesResponse.data.likes)) {
            likesData = likesResponse.data.likes;
          } else if (likesResponse.data && Array.isArray(likesResponse.data.data)) {
            likesData = likesResponse.data.data;
          }

          // Process likes data to ensure consistent structure
          const processedLikes = likesData.map(like => ({
            id: like.id || like.like_id || like.likeId,
            userId: like.user_id || like.userId || like.id,
            name: like.name || t('common.unknown'),
            age: like.age || '30',
            image: like.image || like.photo || like.profile_photo || like.avatar || 'https://randomuser.me/api/portraits/men/1.jpg',
            premium: like.premium || false,
            created_at: like.created_at || like.timestamp || new Date().toISOString()
          }));

          setLikedYou(processedLikes);
        } catch (likesError) {
          console.log('Error fetching received likes:', likesError);
          // Use empty array if endpoint fails
          setLikedYou([]);
        }

        try {
          // Fetch profiles the user has visited
          const visitedResponse = await api.get('/api/profile/visited');
          console.log('Visited profiles response:', visitedResponse.data);

          // Handle different response structures
          let visitedData = [];
          if (Array.isArray(visitedResponse.data)) {
            visitedData = visitedResponse.data;
          } else if (visitedResponse.data && Array.isArray(visitedResponse.data.profiles)) {
            visitedData = visitedResponse.data.profiles;
          } else if (visitedResponse.data && Array.isArray(visitedResponse.data.data)) {
            visitedData = visitedResponse.data.data;
          }

          // Process visited profiles data to ensure consistent structure
          const processedVisited = visitedData.map(profile => ({
            id: profile.id || profile.profile_id || profile.profileId,
            userId: profile.user_id || profile.userId || profile.id,
            name: profile.name || t('common.unknown'),
            age: profile.age || '30',
            image: profile.image || profile.photo || profile.profile_photo || profile.avatar || 'https://randomuser.me/api/portraits/men/1.jpg',
            premium: profile.premium || false,
            visited_at: profile.visited_at || profile.timestamp || new Date().toISOString()
          }));

          setVisitedProfiles(processedVisited);
        } catch (visitedError) {
          console.log('Error fetching visited profiles:', visitedError);
          // Use empty array if endpoint doesn't exist yet
          setVisitedProfiles([]);
        }

        try {
          // Fetch profiles the user has liked
          const likedResponse = await api.get('/api/likes/sent');
          console.log('Liked profiles response:', likedResponse.data);

          // Handle different response structures
          let likedData = [];
          if (Array.isArray(likedResponse.data)) {
            likedData = likedResponse.data;
          } else if (likedResponse.data && Array.isArray(likedResponse.data.likes)) {
            likedData = likedResponse.data.likes;
          } else if (likedResponse.data && Array.isArray(likedResponse.data.data)) {
            likedData = likedResponse.data.data;
          }

          // Process liked profiles data to ensure consistent structure
          const processedLiked = likedData.map(profile => ({
            id: profile.id || profile.like_id || profile.likeId,
            userId: profile.user_id || profile.userId || profile.id,
            name: profile.name || t('common.unknown'),
            age: profile.age || '30',
            image: profile.image || profile.photo || profile.profile_photo || profile.avatar || 'https://randomuser.me/api/portraits/men/1.jpg',
            premium: profile.premium || false,
            created_at: profile.created_at || profile.timestamp || new Date().toISOString()
          }));

          setLikedProfiles(processedLiked);
        } catch (likedError) {
          console.log('Error fetching liked profiles:', likedError);
          // Use empty array if endpoint doesn't exist yet
          setLikedProfiles([]);
        }

        return true; // Success
      } catch (error) {
        console.log(`Error in overall fetch (Attempt ${retryCount + 1}/${maxRetries}):`, error);

        if (retryCount < maxRetries - 1) {
          retryCount++;
          console.log(`Retrying in 1 second... (${retryCount}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
          return attemptFetch(); // Recursive retry
        }

        // All retries failed
        console.log('All retry attempts failed');
        Alert.alert(
          t('common.error'),
          t('matches.errorFetchingMatches'),
          [{ text: t('common.ok') }]
        );
        return false; // Failed after all retries
      }
    };

    try {
      await attemptFetch();
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  // Function to calculate age from birth date
  const calculateAge = (birthDate) => {
    if (!birthDate) return '';

    // Handle different date formats
    let dateObj;
    if (typeof birthDate === 'string') {
      // Try to parse the date string
      dateObj = new Date(birthDate);
    } else if (birthDate instanceof Date) {
      dateObj = birthDate;
    } else {
      return '';
    }

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    // Calculate age
    const today = new Date();
    let age = today.getFullYear() - dateObj.getFullYear();
    const monthDiff = today.getMonth() - dateObj.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateObj.getDate())) {
      age--;
    }

    return age.toString();
  };

  const handleViewProfile = async (userId) => {
    // Find the selected profile data from any of the arrays
    let profileData = [...matches, ...likedYou, ...visitedProfiles, ...likedProfiles].find(
      profile => profile.userId === userId
    );

    if (profileData) {
      try {
        // Try to fetch detailed profile data from the API
        const response = await api.get(`/api/profile/${userId}`);
        if (response.data) {
          console.log('Fetched detailed profile data:', response.data);
          // Merge the detailed data with the existing profile data
          profileData = { ...profileData, ...response.data };
        }
      } catch (error) {
        console.log('Error fetching detailed profile data:', error);
        // Continue with the existing profile data
      }

      // Convert profile data to the format expected by ScrollableProfileCard
      const formattedProfile = {
        id: profileData.userId || profileData.user_id || profileData.id,
        name: profileData.name || profileData.first_name || '',
        age: profileData.age || calculateAge(profileData.birth_date),
        nationality: profileData.nationality,
        location: profileData.location || profileData.city || '',
        profileImage: profileData.image || profileData.photo || profileData.profile_photo,
        bio: profileData.bio || profileData.about_me || '',
        religious: profileData.religious || profileData.religion || '',
        education: profileData.education || profileData.education_level || '',
        height: profileData.height || '',
        weight: profileData.weight || '',
        prayer: profileData.prayer || profileData.prayer_frequency || '',
        employment: profileData.employment || profileData.job_level || '',
        smoker: profileData.smoker || false,
        fasting: profileData.fasting || profileData.fasting_frequency || '',
        skin_color: profileData.skin_color || '',
        marriageType: profileData.marriageType || profileData.marriage_type || '',
        wifeAfterMarriage: profileData.wifeAfterMarriage || profileData.wife_after_marriage || '',
        workAfterMarriage: profileData.workAfterMarriage || profileData.work_after_marriage || '',
        financialContribution: profileData.financialContribution || profileData.financial_contribution || '',
        premaritalItems: profileData.premaritalItems || profileData.premarital_items || '',
        relocationAfterMarriage: profileData.relocationAfterMarriage || profileData.relocation_after_marriage || '',
        healthStatus: profileData.healthStatus || profileData.health_status || '',
        hajjStatus: profileData.hajjStatus || profileData.hajj_status || '',
        fastingStatus: profileData.fastingStatus || profileData.fasting_status || '',
        partnerDescription: profileData.partnerDescription || profileData.partner_description || '',
      };

      setSelectedMatch(formattedProfile);
      setShowProfileModal(true);

      // Record this profile view in the backend
      try {
        api.post('/api/profile/view', { viewed_user_id: userId })
          .catch(error => console.log('Error recording profile view:', error));
      } catch (error) {
        console.log('Error recording profile view:', error);
      }
    }
  };

  const handleCloseProfile = () => {
    setShowProfileModal(false);
    setSelectedMatch(null);
  };

  const handleOpenChat = (match) => {
    navigation.navigate('Chat', {
      matchId: match.id,
      name: match.name,
      userId: match.userId,
      image: match.image,
    });
  };

  const renderMatchItem = ({ item }) => (
    <TouchableOpacity
      style={styles.matchItem}
      onPress={() => handleViewProfile(item.userId)}
    >
      <View style={styles.matchImageContainer}>
        <BlurredProfileImage
          source={{ uri: item.image }}
          imageType="match"
          style={styles.matchImage}
          showEyeIcon={true}
          blurIntensity={110}
        />
        <View style={styles.matchNameContainer}>
          <Text style={styles.matchName} numberOfLines={1}>
            {item.name}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderLikeItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.likeItem, { backgroundColor: colors.card }]}
      onPress={() => handleViewProfile(item.userId)}
    >
      <View style={styles.likeImageContainer}>
        <BlurredProfileImage
          source={{ uri: item.image }}
          style={styles.likeImage}
          showEyeIcon={true}
          blurIntensity={110}
        />
        {item.premium && (
          <View style={[styles.premiumBadge, { backgroundColor: colors.primary }]}>
            <Ionicons name="star" size={16} color="#FFF" />
          </View>
        )}
      </View>
      <View style={styles.likeInfoContainer}>
        <Text style={[styles.likeName, { color: colors.text }]}>
          {item.name}, {item.age}
        </Text>
        <Text style={[styles.likeHint, { color: colors.subtext }]}>
          {t('matches.likedYou')}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderVisitedItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.likeItem, { backgroundColor: colors.card }]}
      onPress={() => handleViewProfile(item.userId)}
    >
      <View style={styles.likeImageContainer}>
        <BlurredProfileImage
          source={{ uri: item.image }}
          style={styles.likeImage}
          showEyeIcon={true}
          blurIntensity={110}
        />
        {item.premium && (
          <View style={[styles.premiumBadge, { backgroundColor: colors.primary }]}>
            <Ionicons name="star" size={16} color="#FFF" />
          </View>
        )}
      </View>
      <View style={styles.likeInfoContainer}>
        <Text style={[styles.likeName, { color: colors.text }]}>
          {item.name}, {item.age}
        </Text>
        <Text style={[styles.likeHint, { color: colors.subtext }]}>
          {t('matches.visited')} {getTimeAgo(new Date(item.visited_at || Date.now()))}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderLikedByMeItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.likeItem, { backgroundColor: colors.card }]}
      onPress={() => handleViewProfile(item.userId)}
    >
      <View style={styles.likeImageContainer}>
        <BlurredProfileImage
          source={{ uri: item.image }}
          style={styles.likeImage}
          showEyeIcon={true}
          blurIntensity={110}
        />
        {item.premium && (
          <View style={[styles.premiumBadge, { backgroundColor: colors.primary }]}>
            <Ionicons name="star" size={16} color="#FFF" />
          </View>
        )}
      </View>
      <View style={styles.likeInfoContainer}>
        <Text style={[styles.likeName, { color: colors.text }]}>
          {item.name}, {item.age}
        </Text>
        <Text style={[styles.likeHint, { color: colors.subtext }]}>
          {t('matches.youLiked')} {getTimeAgo(new Date(item.created_at || Date.now()))}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderMatchConversation = ({ item }) => {
    const lastMessageDate = item.lastMessage ? new Date(item.lastMessage.timestamp) : new Date();
    const timeAgo = getTimeAgo(lastMessageDate);

    return (
      <TouchableOpacity
        style={[styles.conversationItem, { borderBottomColor: colors.border }]}
        onPress={() => handleOpenChat(item)}
      >
        <BlurredProfileImage
          source={{ uri: item.image }}
          style={styles.conversationImage}
          showEyeIcon={true}
          blurIntensity={110}
        />
        <View style={styles.conversationContent}>
          <View style={styles.conversationHeader}>
            <Text style={[styles.conversationName, { color: colors.text }]}>
              {item.name}
            </Text>
            <Text style={[styles.conversationTime, { color: colors.subtext }]}>
              {timeAgo}
            </Text>
          </View>
          <View style={styles.conversationMessageContainer}>
            <Text
              style={[
                styles.conversationMessage,
                { color: item.lastMessage?.read ? colors.subtext : colors.text }
              ]}
              numberOfLines={1}
            >
              {item.lastMessage?.text || ''}
            </Text>

            {item.lastMessage && !item.lastMessage.read && (
              <View style={[styles.unreadBadge, { backgroundColor: colors.primary }]} />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const getTimeAgo = (date) => {
    const now = new Date();
    const diffInMs = now - date;
    const diffInMinutes = diffInMs / (1000 * 60);
    const diffInHours = diffInMs / (1000 * 60 * 60);
    const diffInDays = diffInHours / 24;

    if (diffInMinutes < 1) {
      return t('common.justNow');
    } else if (diffInMinutes < 60) {
      return t('common.minutesAgo', { count: Math.floor(diffInMinutes) });
    } else if (diffInHours < 24) {
      return t('common.hoursAgo', { count: Math.floor(diffInHours) });
    } else if (diffInDays < 2) {
      return t('common.yesterday');
    } else if (diffInDays < 7) {
      return t('common.daysAgo', { count: Math.floor(diffInDays) });
    } else if (diffInDays < 30) {
      return t('common.weeksAgo', { count: Math.floor(diffInDays / 7) });
    } else if (diffInDays < 365) {
      return t('common.monthsAgo', { count: Math.floor(diffInDays / 30) });
    } else {
      return t('common.yearsAgo', { count: Math.floor(diffInDays / 365) });
    }
  };

  const EmptyState = ({ type }) => {
    let icon = 'people-outline';
    let title = '';
    let description = '';

    switch (type) {
      case 'matches':
        icon = 'heart-dislike-outline';
        title = t('matches.emptyMatchesTitle');
        description = t('matches.emptyMatchesDesc');
        break;
      case 'likes':
        icon = 'people-outline';
        title = t('matches.emptyLikesTitle');
        description = t('matches.emptyLikesDesc');
        break;
      case 'visited':
        icon = 'eye-outline';
        title = t('matches.emptyVisitedTitle');
        description = t('matches.emptyVisitedDesc');
        break;
      case 'liked':
        icon = 'heart-outline';
        title = t('matches.emptyLikedTitle');
        description = t('matches.emptyLikedDesc');
        break;
      default:
        icon = 'people-outline';
        title = t('matches.emptyProfilesTitle');
        description = t('matches.emptyProfilesDesc');
    }

    return (
      <View style={styles.emptyContainer}>
        <Ionicons
          name={icon}
          size={64}
          color={colors.subtext}
        />
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          {title}
        </Text>
        <Text style={[styles.emptyDescription, { color: colors.subtext }]}>
          {description}
        </Text>

        <TouchableOpacity
          style={[styles.startSwipingButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate('Home')}
        >
          <Text style={styles.startSwipingText}>
            {t('matches.startSwiping')}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView
      style={[
        styles.container,
        {
          backgroundColor: colors.background,
          paddingBottom: 10 // Add padding to the bottom of the SafeAreaView
        }
      ]}
      edges={['top', 'left', 'right']} // Don't include bottom edge to avoid navigation bar overlap
    >
      {/* <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('matches.title')}
        </Text>
      </View> */}

      <View style={[styles.tabContainer, { borderBottomColor: colors.border }]}>
        {/* <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'matches' && [
              styles.activeTab,
              { borderBottomColor: colors.primary },
            ],
          ]}
          onPress={() => setActiveTab('matches')}
        >
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'matches' ? colors.primary : colors.subtext },
            ]}
          >
            {t('matches.matchesTab')}
          </Text>
        </TouchableOpacity> */}
{/*
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'likes' && [
              styles.activeTab,
              { borderBottomColor: colors.primary },
            ],
          ]}
          onPress={() => setActiveTab('likes')}
        >
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'likes' ? colors.primary : colors.subtext },
            ]}
          >
            {t('matches.likesTab')}
            {likedYou.length > 0 && (
              <Text style={styles.likeCount}>
                {` (${likedYou.length})`}
              </Text>
            )}
          </Text>
        </TouchableOpacity> */}

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'visited' && [
              styles.activeTab,
              { borderBottomColor: colors.primary },
            ],
          ]}
          onPress={() => setActiveTab('visited')}
        >
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'visited' ? colors.primary : colors.subtext },
            ]}
          >
            {t('visited')}
            {visitedProfiles.length > 0 && (
              <Text style={styles.likeCount}>
                {` (${visitedProfiles.length})`}
              </Text>
            )}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'liked' && [
              styles.activeTab,
              { borderBottomColor: colors.primary },
            ],
          ]}
          onPress={() => setActiveTab('liked')}
        >
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'liked' ? colors.primary : colors.subtext },
            ]}
          >
            {t('liked')}
            {likedProfiles.length > 0 && (
              <Text style={styles.likeCount}>
                {` (${likedProfiles.length})`}
              </Text>
            )}
          </Text>
        </TouchableOpacity>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : activeTab === 'matches' ? (
        <>
          {matches.length > 0 && (
            <View style={styles.newMatchesContainer}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {t('matches.newMatches')}
                </Text>
                <TouchableOpacity>
                  <Text style={[styles.seeAllText, { color: colors.primary }]}>
                    {t('matches.seeAll')}
                  </Text>
                </TouchableOpacity>
              </View>

              <FlatList
                data={matches}
                horizontal
                showsHorizontalScrollIndicator={false}
                renderItem={renderMatchItem}
                keyExtractor={(item) => item.id.toString()}
                contentContainerStyle={styles.newMatchesList}
              />
            </View>
          )}

          <View style={styles.conversationsContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {t('matches.messages')}
            </Text>

            {matches.length > 0 ? (
              <FlatList
                data={matches}
                renderItem={renderMatchConversation}
                keyExtractor={(item) => item.id.toString()}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[colors.primary]}
                    tintColor={colors.primary}
                  />
                }
                showsVerticalScrollIndicator={true}
                initialNumToRender={10}
                windowSize={10}
                contentContainerStyle={{ paddingBottom: 100 }}
              />
            ) : (
              <EmptyState type="matches" />
            )}
          </View>
        </>
      ) : activeTab === 'likes' ? (
        <View style={styles.likesContainer}>
          {likedYou.length > 0 ? (
            <FlatList
              data={likedYou}
              numColumns={2}
              renderItem={renderLikeItem}
              keyExtractor={(item) => item.id.toString()}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={[colors.primary]}
                  tintColor={colors.primary}
                />
              }
              contentContainerStyle={styles.likesList}
              showsVerticalScrollIndicator={true}
              initialNumToRender={10}
              windowSize={10}
              removeClippedSubviews={false}
            />
          ) : (
            <EmptyState type="likes" />
          )}
        </View>
      ) : activeTab === 'visited' ? (
        <View style={styles.likesContainer}>
          {visitedProfiles.length > 0 ? (
            <FlatList
              data={visitedProfiles}
              numColumns={2}
              renderItem={renderVisitedItem}
              keyExtractor={(item) => item.id.toString()}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={[colors.primary]}
                  tintColor={colors.primary}
                />
              }
              contentContainerStyle={styles.likesList}
              showsVerticalScrollIndicator={true}
              initialNumToRender={10}
              windowSize={10}
              removeClippedSubviews={false}
            />
          ) : (
            <EmptyState type="visited" />
          )}
        </View>
      ) : (
        <View style={styles.likesContainer}>
          {likedProfiles.length > 0 ? (
            <FlatList
              data={likedProfiles}
              numColumns={2}
              renderItem={renderLikedByMeItem}
              keyExtractor={(item) => item.id.toString()}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={[colors.primary]}
                  tintColor={colors.primary}
                />
              }
              contentContainerStyle={styles.likesList}
              showsVerticalScrollIndicator={true}
              initialNumToRender={10}
              windowSize={10}
              removeClippedSubviews={false}
            />
          ) : (
            <EmptyState type="liked" />
          )}
        </View>
      )}

      {/* Profile Modal */}
      <Modal
        visible={showProfileModal}
        animationType="slide"
        onRequestClose={handleCloseProfile}
        statusBarTranslucent
      >
        {selectedMatch && (
          <ScrollableProfileCard
            profile={selectedMatch}
            onClose={handleCloseProfile}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
  },
  tabContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    borderBottomWidth: 1,
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  activeTab: {
    borderBottomWidth: 3,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
  },
  likeCount: {
    fontFamily: 'Roboto',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  newMatchesContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-Bold',
  },
  seeAllText: {
    fontSize: 14,
    fontFamily: 'Roboto-Medium',
  },
  newMatchesList: {
    paddingEnd: 10,
  },
  matchItem: {
    marginEnd: 10,
    width: MATCH_ITEM_WIDTH,
  },
  matchImageContainer: {
    position: 'relative',
  },
  matchImage: {
    width: MATCH_ITEM_WIDTH,
    height: MATCH_ITEM_WIDTH + 20,
    borderRadius: 12,
  },
  matchNameContainer: {
    position: 'absolute',
    bottom: 0,
    start: 0,
    end: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 8,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  matchName: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Roboto-Medium',
    textAlign: 'center',
  },
  conversationsContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  conversationItem: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    paddingVertical: 12,
    borderBottomWidth: 0.5,
  },
  conversationImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginEnd: 15,
  },
  conversationContent: {
    flex: 1,
    justifyContent: 'center',
  },
  conversationHeader: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  conversationName: {
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
  },
  conversationTime: {
    fontSize: 14,
    fontFamily: 'Roboto',
  },
  conversationMessageContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  conversationMessage: {
    fontSize: 14,
    fontFamily: 'Roboto',
    flex: 1,
  },
  unreadBadge: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginStart: 8,
  },
  likesContainer: {
    flex: 1,
  },
  likesList: {
    padding: 10,
    paddingBottom: 100, // Increased padding to account for navigation bar
  },
  likeItem: {
    flex: 1,
    margin: 8,
    borderRadius: 12,
    overflow: 'hidden',
    maxWidth: '50%',
  },
  likeImageContainer: {
    position: 'relative',
  },
  likeImage: {
    width: '100%',
    aspectRatio: 0.8,
    borderRadius: 12,
  },
  premiumBadge: {
    position: 'absolute',
    top: 10,
    end: 10,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  likeInfoContainer: {
    padding: 10,
  },
  likeName: {
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
    marginBottom: 4,
  },
  likeHint: {
    fontSize: 12,
    fontFamily: 'Roboto',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    fontFamily: 'Roboto',
    textAlign: 'center',
    marginBottom: 30,
  },
  startSwipingButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  startSwipingText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
  },
  languageSwitcher: {
    alignSelf: 'flex-end',
  },
});

export default MatchesScreen;