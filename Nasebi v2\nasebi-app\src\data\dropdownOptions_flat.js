// Dropdown options for various profile fields with flat translation keys

// Education levels
export const EDUCATION_LEVELS = [
  { value: 'less_than_highschool', label: 'educationLessThanHighschool' },
  { value: 'highschool', label: 'educationHighschool' },
  { value: 'college_degree', label: 'educationCollegeDegree' },
  { value: 'bachelors', label: 'educationBachelors' },
  { value: 'masters', label: 'educationMasters' },
  { value: 'doctorate', label: 'educationDoctorate' },
  { value: 'religious_education', label: 'educationReligiousEducation' }
];

// Religious levels
export const RELIGIOUS_LEVELS = [
  { value: 'very_religious', label: 'religiousVeryReligious' },
  { value: 'religious', label: 'religiousReligious' },
  { value: 'somewhat_religious', label: 'religiousSomewhatReligious' },
  { value: 'not_religious', label: 'religiousNotReligious' }
];

// Prayer levels
export const PRAYER_LEVELS = [
  { value: 'daily', label: 'prayerDaily' },
  { value: 'weekly', label: 'prayerWeekly' },
  { value: 'sometimes', label: 'prayerSometimes' },
  { value: 'religious_occasions', label: 'prayerReligiousOccasions' },
  { value: 'rarely', label: 'prayerRarely' },
  { value: 'never', label: 'prayerNever' }
];

// Fasting levels
export const FASTING_LEVELS = [
  { value: 'always', label: 'fastingAlways' },
  { value: 'sometimes', label: 'fastingSometimes' },
  { value: 'never', label: 'fastingNever' },
  { value: 'prefer_not_to_say', label: 'fastingPreferNotToSay' }
];

// Hajj status
export const HAJJ_STATUS = [
  { value: 'completed', label: 'hajjCompleted' },
  { value: 'planning_soon', label: 'hajjPlanningSoon' },
  { value: 'planning_future', label: 'hajjPlanningFuture' },
  { value: 'not_planned', label: 'hajjNotPlanned' }
];

// Marital status
export const MARITAL_STATUS = [
  { value: 'single', label: 'maritalStatusSingle' },
  { value: 'divorced', label: 'maritalStatusDivorced' },
  { value: 'widowed', label: 'maritalStatusWidowed' },
  { value: 'married', label: 'maritalStatusMarried' }
];

// Marriage readiness
export const MARRIAGE_READINESS = [
  { value: 'immediately', label: 'marriageReadinessImmediately' },
  { value: 'within_year', label: 'marriageReadinessWithinYear' },
  { value: 'after_two_years', label: 'marriageReadinessAfterTwoYears' },
  { value: 'not_decided', label: 'marriageReadinessNotDecided' }
];

// Preferred residence
export const PREFERRED_RESIDENCE = [
  { value: 'own_home', label: 'residenceOwnHome' },
  { value: 'family_home', label: 'residenceFamilyHome' },
  { value: 'family_home_temporarily', label: 'residenceFamilyHomeTemporarily' },
  { value: 'undecided', label: 'residenceUndecided' }
];

// Children preferences
export const CHILDREN_PREFERENCES = [
  { value: 'soon', label: 'childrenSoon' },
  { value: 'after_two_years', label: 'childrenAfterTwoYears' },
  { value: 'depends', label: 'childrenDepends' },
  { value: 'no', label: 'childrenNo' }
];

// Work preferences
export const WORK_PREFERENCES = [
  { value: 'yes', label: 'workYes' },
  { value: 'yes_from_home', label: 'workYesFromHome' },
  { value: 'depends', label: 'workDepends' },
  { value: 'no', label: 'workNo' }
];

// Health status
export const HEALTH_STATUS = [
  { value: 'good_health', label: 'healthGoodHealth' },
  { value: 'special_needs', label: 'healthSpecialNeeds' },
  { value: 'chronic_disease', label: 'healthChronicDisease' },
  { value: 'infertile', label: 'healthInfertile' }
];

// Smoking status
export const SMOKING_STATUS = [
  { value: 'yes', label: 'smokingYes' },
  { value: 'sometimes', label: 'smokingSometimes' },
  { value: 'no', label: 'smokingNo' }
];

// Job levels
export const JOB_LEVELS = [
  { value: 'student', label: 'jobStudent' },
  { value: 'employee', label: 'jobEmployee' },
  { value: 'senior_employee', label: 'jobSeniorEmployee' },
  { value: 'manager', label: 'jobManager' },
  { value: 'unemployed', label: 'jobUnemployed' },
  { value: 'prefer_not_to_say', label: 'jobPreferNotToSay' }
];

// Income levels
export const INCOME_LEVELS = [
  { value: 'no_income', label: 'incomeNoIncome' },
  { value: 'low', label: 'incomeLow' },
  { value: 'average', label: 'incomeAverage' },
  { value: 'high', label: 'incomeHigh' }
];

// Religious sects
export const RELIGIOUS_SECTS = [
  { value: 'sunni', label: 'religiousSectSunni' },
  { value: 'shia', label: 'religiousSectShia' },
  { value: 'other', label: 'religiousSectOther' }
];

// Gender options
export const GENDER_OPTIONS = [
  { value: 'male', label: 'genderMale' },
  { value: 'female', label: 'genderFemale' }
];

// Boolean options (Yes/No)
export const BOOLEAN_OPTIONS = [
  { value: 'yes', label: 'yes' },
  { value: 'no', label: 'no' }
];

// Languages
export const LANGUAGES = [
  { value: 'arabic', label: 'languageArabic' },
  { value: 'english', label: 'languageEnglish' },
  { value: 'french', label: 'languageFrench' },
  { value: 'spanish', label: 'languageSpanish' },
  { value: 'turkish', label: 'languageTurkish' },
  { value: 'urdu', label: 'languageUrdu' },
  { value: 'hindi', label: 'languageHindi' },
  { value: 'persian', label: 'languagePersian' }
];
