import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Image,
  Switch,
  Platform,
  ActivityIndicator,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { useLanguage } from '../../context/LanguageContext';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { getUser, updateUser } from '../../services/userService';
import LoadingSpinner from '../../components/LoadingSpinner';
import { useRTL } from '../../hooks/useRTL';
import { countries } from '../../data/countries';
import { cities } from '../../data/cities';
import { skinColors } from '../../data/skinColors';
import CustomDropdown from '../../components/CustomDropdown';
import CountryCityDropdown from '../../components/CountryCityDropdown';
import * as DropdownOptions from '../../data/dropdownOptions_flat';

// Create a custom DateTimePicker component to handle platform differences
const CustomDateTimePicker = (props) => {
  if (Platform.OS === 'android') {
    // For Android, use a simpler date picker approach
    return (
      <TouchableOpacity
        style={[
          styles.textInput,
          { backgroundColor: props.colors.background, borderColor: props.colors.border, justifyContent: 'center' }
        ]}
        onPress={props.onPress}
      >
        <Text style={{ color: props.colors.text }}>
          {props.value.toLocaleDateString()}
        </Text>
      </TouchableOpacity>
    );
  } else {
    // Use the imported DateTimePicker for iOS
    return <DateTimePicker {...props} />;
  }
};

const SECTIONS = {
  BASIC: 'basic',
  PERSONAL: 'personal',
  RELIGIOUS: 'religious',
  MARRIAGE: 'marriage',
  EDUCATION: 'education',
  BIO: 'bio',
};

const EditProfile = ({ navigation }) => {
  const { user: authUser, updateUser: updateAuthUser, skipProfileCompletion } = useAuth();
  const { colors, isDark } = useTheme();
  const { t } = useTranslationFlat();
  const { isRTL } = useLanguage();
  const rtl = useRTL();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeSection, setActiveSection] = useState(SECTIONS.BASIC);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const [userData, setUserData] = useState({
    name: '',
    birth_date: new Date(),
    gender: 'male',
    height: '',
    weight: '',
    nationality: '',
    country_of_residence: '',
    skin_color: '',
    location: '',
    city: '',
    bio: '',
    partner_description: '',
    marital_status: '',
    has_children: false,
    number_of_children: '0',
    wants_children: '',
    allows_wife_to_work: '',
    education_level: '',
    job_level: '',
    income_level: '',
    religious_level: '',
    religious_sect: 'sunni',
    prayer_level: '',
    fasting_level: '',
    hajj_status: '',
    smoking: 'no',
    preferred_residence: '',
    preferred_marriage_type: '',
    tribal_affiliation: false,
    marriage_readiness: '',
    chat_languages: '',
    health_status: 'good_health',
  });

  // State to track the selected country of residence for city selection
  const [selectedResidenceCountry, setSelectedResidenceCountry] = useState('');

  // Render custom dropdown component
  const renderDropdown = (label, field, options, placeholder = '', helperText = '', customOnChange = null) => {
    // Special handling for tribal_affiliation which is a boolean but displayed as yes/no
    let selectedValue;
    if (customOnChange && field === 'tribal_affiliation_str') {
      selectedValue = userData.tribal_affiliation ? 'yes' : 'no';
    } else {
      selectedValue = userData[field] || '';
    }

    // Debug log to check the value
    console.log(`Dropdown ${field}: value = ${selectedValue}`);

    return (
      <View style={styles.formGroup}>
        <Text style={[styles.inputLabel, { color: colors.text }]}>
          {label}
        </Text>
        <CustomDropdown
          label={label}
          options={options}
          selectedValue={selectedValue}
          onValueChange={(value) => {
            console.log(`Dropdown ${field} changed to: ${value}`);
            if (customOnChange) {
              customOnChange(value);
            } else {
              handleInputChange(field, value);
            }
          }}
          placeholder={placeholder}
        />
        {helperText ? (
          <Text style={[styles.helperText, { color: colors.subtext }]}>
            {helperText}
          </Text>
        ) : null}
      </View>
    );
  };

  // Render country and city dropdowns
  const renderCountryCityDropdowns = () => (
    <>
      <View style={styles.formGroup}>
        <Text style={[styles.inputLabel, { color: colors.text }]}>
          {t('countryOfResidence')}
        </Text>
        <CountryCityDropdown
          selectedCountry={selectedResidenceCountry}
          selectedCity={userData.city}
          onCountryChange={(value) => {
            setSelectedResidenceCountry(value);
            handleInputChange('country_of_residence', value);
            handleInputChange('city', '');
          }}
          onCityChange={(value) => handleInputChange('city', value)}
        />
      </View>
    </>
  );

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);
      const data = await getUser(authUser.id);
      if (data) {
        // Convert date string to Date object if it exists
        if (data.birth_date) {
          data.birth_date = new Date(data.birth_date);
        } else {
          // If no birth date is set but age is available, calculate an approximate birth date
          if (data.age) {
            const today = new Date();
            const birthYear = today.getFullYear() - parseInt(data.age, 10);
            data.birth_date = new Date(birthYear, today.getMonth(), today.getDate());
          } else {
            // Default to 18 years ago if neither birth_date nor age is available
            const today = new Date();
            data.birth_date = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
          }
        }

        // Convert numeric values to strings for TextInput
        if (data.height) data.height = data.height.toString();
        if (data.weight) data.weight = data.weight.toString();
        if (data.number_of_children) data.number_of_children = data.number_of_children.toString();

        // Set tribal affiliation string for dropdown
        data.tribal_affiliation_str = data.tribal_affiliation ? 'yes' : 'no';

        setUserData({
          ...userData,
          ...data,
        });

        // Set the selected country of residence
        if (data.country_of_residence) {
          setSelectedResidenceCountry(data.country_of_residence);
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      Alert.alert(t('error'), t('loadError'));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Remove any fields that shouldn't be sent to the server
      const { tribal_affiliation_str, ...cleanedUserData } = userData;

      // Convert fields to appropriate types before saving
      const dataToSave = {
        ...cleanedUserData,
        height: userData.height ? parseInt(userData.height, 10) : null,
        weight: userData.weight ? parseInt(userData.weight, 10) : null,
        number_of_children: userData.number_of_children ? parseInt(userData.number_of_children, 10) : 0,
      };

      // Ensure gender field is explicitly included
      if (!dataToSave.gender) {
        console.log('Gender field missing, setting default value');
        dataToSave.gender = userData.gender || 'male';
      }

      // Log the gender value being saved
      console.log(`Saving gender value: ${dataToSave.gender}`);

      // Format birth_date as string in YYYY-MM-DD format for MySQL
      if (dataToSave.birth_date instanceof Date) {
        // Format as YYYY-MM-DD without timezone offset
        const year = dataToSave.birth_date.getFullYear();
        const month = String(dataToSave.birth_date.getMonth() + 1).padStart(2, '0');
        const day = String(dataToSave.birth_date.getDate()).padStart(2, '0');
        dataToSave.birth_date = `${year}-${month}-${day}`;

        // Calculate and include age based on birth date
        dataToSave.age = calculateAge(userData.birth_date);

        console.log('Formatted birth date:', dataToSave.birth_date);
      }

      // Ensure all dropdown fields are properly included
      // Basic info
      if (!dataToSave.gender) dataToSave.gender = userData.gender || 'male';

      // Religious info
      if (!dataToSave.religious_sect) dataToSave.religious_sect = userData.religious_sect || 'sunni';
      if (!dataToSave.fasting_level) dataToSave.fasting_level = userData.fasting_level || '';
      if (!dataToSave.hajj_status) dataToSave.hajj_status = userData.hajj_status || '';

      // Education info
      if (!dataToSave.education_level) dataToSave.education_level = userData.education_level || '';
      if (!dataToSave.job_level) dataToSave.job_level = userData.job_level || '';
      if (!dataToSave.income_level) dataToSave.income_level = userData.income_level || '';

      // Marriage info
      if (!dataToSave.marital_status) dataToSave.marital_status = userData.marital_status || '';
      if (!dataToSave.marriage_readiness) dataToSave.marriage_readiness = userData.marriage_readiness || '';
      if (!dataToSave.preferred_residence) dataToSave.preferred_residence = userData.preferred_residence || '';
      if (!dataToSave.wants_children) dataToSave.wants_children = userData.wants_children || '';
      if (!dataToSave.allows_wife_to_work) dataToSave.allows_wife_to_work = userData.allows_wife_to_work || '';
      if (!dataToSave.health_status) dataToSave.health_status = userData.health_status || 'good_health';
      if (!dataToSave.smoking) dataToSave.smoking = userData.smoking || 'no';

      // Partner description
      if (!dataToSave.partner_description) dataToSave.partner_description = userData.partner_description || '';

      console.log('Saving profile data:', JSON.stringify(dataToSave, null, 2));

      // Use the updated userService function to update the profile
      const result = await updateUser(authUser.id, dataToSave);

      if (result.success) {
        console.log('Profile updated successfully:', JSON.stringify(result.data, null, 2));

        // Update the auth context with new user data
        // Include all the updated profile data, not just name
        updateAuthUser({
          ...authUser,
          ...result.data, // Include all updated fields from the response
          name: userData.name,
          profile_completion_percentage: result.profile_completion_percentage || 0,
        });

        Alert.alert(
          t('success'),
          t('updateSuccess'),
          [{
            text: t('ok'),
            onPress: async () => {
              // Check if we can go back (if there's a previous screen)
              const canGoBack = navigation.canGoBack();
              if (canGoBack) {
                navigation.goBack();
              } else {
                // If we can't go back, we're likely in the CompleteProfile screen
                // Use skipProfileCompletion to update the profile completion status
                const skipResult = await skipProfileCompletion();
                if (skipResult.success) {
                  // The AppNavigator will automatically redirect to the main app
                  // when the profile_completion_percentage is updated
                  console.log('Profile completion skipped successfully');
                } else {
                  console.error('Failed to skip profile completion:', skipResult.error);
                }
              }
            }
          }]
        );
      } else {
        console.error('Failed to update profile:', result.message);
        Alert.alert(t('error'), result.message || t('updateError'));
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert(t('error'), t('updateError'));
    } finally {
      setSaving(false);
    }
  };

  // Calculate age from birth date
  const calculateAge = (birthDate) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    // If birth month is after current month or same month but birth day is after current day
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  const handleInputChange = (field, value) => {
    // Special logging for gender field to debug issues
    if (field === 'gender') {
      console.log(`Setting gender to: ${value}`);
    }

    setUserData(prev => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // Log the updated value for gender
      if (field === 'gender') {
        console.log(`Updated gender in userData: ${newData.gender}`);
      }

      return newData;
    });

    // If country of residence changes, update the selected country and reset city
    if (field === 'country_of_residence') {
      setSelectedResidenceCountry(value);
      setUserData(prev => ({
        ...prev,
        city: ''
      }));
    }
  };

  // Handle date change for birth date picker
  const handleDateChange = (_, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setUserData(prev => ({
        ...prev,
        birth_date: selectedDate
      }));
    }
  };

  const renderBasicInfoSection = () => {
    return (
      <View style={styles.formSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('basicInfo')}
        </Text>

        <View style={styles.formGroup}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>
            {t('name')}
          </Text>
          <TextInput
            style={[
              styles.textInput,
              { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
            ]}
            placeholder={t('namePlaceholder')}
            placeholderTextColor={colors.subtext}
            value={userData.name}
            onChangeText={(text) => handleInputChange('name', text)}
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>
            {t('birthDate')}
          </Text>
          <TouchableOpacity
            style={[
              styles.textInput,
              { backgroundColor: colors.background, color: colors.text, borderColor: colors.border, justifyContent: 'center' }
            ]}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={{ color: colors.text }}>
              {userData.birth_date ? userData.birth_date.toLocaleDateString() : t('selectBirthDate')}
            </Text>
          </TouchableOpacity>
          <Text style={[styles.helperText, { color: colors.subtext }]}>
            {userData.birth_date ? `${t('age')}: ${calculateAge(userData.birth_date)}` : ''}
          </Text>

          {showDatePicker && (
            <DateTimePicker
              value={userData.birth_date || new Date()}
              mode="date"
              display={Platform.OS === 'ios' ? "spinner" : "default"}
              onChange={handleDateChange}
              maximumDate={new Date()}
              minimumDate={new Date(1940, 0, 1)}
            />
          )}
        </View>

        <View style={styles.formGroup}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>
            {t('height')} (cm)
          </Text>
          <TextInput
            style={[
              styles.textInput,
              { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
            ]}
            placeholder={t('heightPlaceholder')}
            placeholderTextColor={colors.subtext}
            value={userData.height}
            onChangeText={(text) => handleInputChange('height', text)}
            keyboardType="number-pad"
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>
            {t('weight')} (kg)
          </Text>
          <TextInput
            style={[
              styles.textInput,
              { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
            ]}
            placeholder={t('weightPlaceholder')}
            placeholderTextColor={colors.subtext}
            value={userData.weight}
            onChangeText={(text) => handleInputChange('weight', text)}
            keyboardType="number-pad"
          />
        </View>

        {renderDropdown(
          t('gender'),
          'gender',
          DropdownOptions.GENDER_OPTIONS,
          t('selectGender'),
          '',
          (value) => {
            console.log(`Gender changed to: ${value}`);
            handleInputChange('gender', value);
          }
        )}

        {renderDropdown(
          t('nationality'),
          'nationality',
          countries,
          t('selectCountry')
        )}

        {renderCountryCityDropdowns()}

        <View style={styles.formGroup}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>
            {t('specificLocation')}
          </Text>
          <TextInput
            style={[
              styles.textInput,
              { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
            ]}
            placeholder={t('specificLocationPlaceholder')}
            placeholderTextColor={colors.subtext}
            value={userData.location}
            onChangeText={(text) => handleInputChange('location', text)}
          />
        </View>

        {renderDropdown(
          t('skinColor'),
          'skin_color',
          skinColors,
          t('selectSkinColor')
        )}

        {renderDropdown(
          t('tribalAffiliation'),
          'tribal_affiliation_str',
          DropdownOptions.BOOLEAN_OPTIONS,
          '',
          '',
          (value) => handleInputChange('tribal_affiliation', value === 'yes')
        )}
      </View>
    );
  };

  const renderReligiousInfoSection = () => {
    return (
      <View style={styles.formSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('religiousInfo')}
        </Text>

        {renderDropdown(
          t('religiousLevel'),
          'religious_level',
          DropdownOptions.RELIGIOUS_LEVELS
        )}

        {renderDropdown(
          t('religiousSect'),
          'religious_sect',
          DropdownOptions.RELIGIOUS_SECTS
        )}

        {renderDropdown(
          t('prayerFrequency'),
          'prayer_level',
          DropdownOptions.PRAYER_LEVELS
        )}

        {renderDropdown(
          t('fastingFrequency'),
          'fasting_level',
          DropdownOptions.FASTING_LEVELS
        )}

        {renderDropdown(
          t('hajjStatus'),
          'hajj_status',
          DropdownOptions.HAJJ_STATUS
        )}
      </View>
    );
  };

  const renderEducationSection = () => {
    return (
      <View style={styles.formSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('educationAndWork')}
        </Text>

        {renderDropdown(
          t('educationLevel'),
          'education_level',
          DropdownOptions.EDUCATION_LEVELS
        )}

        {renderDropdown(
          t('jobLevel'),
          'job_level',
          DropdownOptions.JOB_LEVELS
        )}

        {/* Add debug logging for income level */}
        {console.log('Income level options:', JSON.stringify(DropdownOptions.INCOME_LEVELS))}
        {console.log('Current income level value:', userData.income_level)}
        {renderDropdown(
          t('incomeLevel'),
          'income_level',
          DropdownOptions.INCOME_LEVELS
        )}

        {renderDropdown(
          t('chatLanguages'),
          'chat_languages',
          [
            { label: t('languageArabic'), value: t('languageArabic') },
            { label: t('languageEnglish'), value: t('languageEnglish') }
          ]
        )}
      </View>
    );
  };

  const renderMarriageSection = () => {
    return (
      <View style={styles.formSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('marriagePreferences')}
        </Text>

        {renderDropdown(
          t('maritalStatus'),
          'marital_status',
          DropdownOptions.MARITAL_STATUS
        )}

        {renderDropdown(
          t('marriageReadiness'),
          'marriage_readiness',
          DropdownOptions.MARRIAGE_READINESS
        )}

        {renderDropdown(
          t('preferredResidence'),
          'preferred_residence',
          DropdownOptions.PREFERRED_RESIDENCE
        )}

        {renderDropdown(
          t('wantsChildren'),
          'wants_children',
          DropdownOptions.CHILDREN_PREFERENCES
        )}

        {renderDropdown(
          t('allowsWifeToWork'),
          'allows_wife_to_work',
          DropdownOptions.WORK_PREFERENCES
        )}

        {renderDropdown(
          t('healthStatus'),
          'health_status',
          DropdownOptions.HEALTH_STATUS
        )}

        {renderDropdown(
          t('smoking'),
          'smoking',
          DropdownOptions.SMOKING_STATUS
        )}
      </View>
    );
  };

  const renderBioSection = () => {
    return (
      <View style={styles.formSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('aboutYou')}
        </Text>

        <View style={styles.formGroup}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>
            {t('bio')}
          </Text>
          <TextInput
            style={[
              styles.textArea,
              { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
            ]}
            placeholder={t('bioPlaceholder')}
            placeholderTextColor={colors.subtext}
            value={userData.bio}
            onChangeText={(text) => handleInputChange('bio', text)}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>
            {t('partnerDescription')}
          </Text>
          <TextInput
            style={[
              styles.textArea,
              { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
            ]}
            placeholder={t('partnerDescriptionPlaceholder')}
            placeholderTextColor={colors.subtext}
            value={userData.partner_description}
            onChangeText={(text) => handleInputChange('partner_description', text)}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
      </View>
    );
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tab,
              activeSection === SECTIONS.BASIC && {
                borderBottomWidth: 2,
                borderBottomColor: colors.primary
              }
            ]}
            onPress={() => setActiveSection(SECTIONS.BASIC)}
          >
            <MaterialCommunityIcons
              name="account"
              size={24}
              color={activeSection === SECTIONS.BASIC ? colors.primary : colors.text}
            />
            <Text
              style={[
                styles.tabText,
                { color: activeSection === SECTIONS.BASIC ? colors.primary : colors.text }
              ]}
            >
              {t('basic')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tab,
              activeSection === SECTIONS.RELIGIOUS && {
                borderBottomWidth: 2,
                borderBottomColor: colors.primary
              }
            ]}
            onPress={() => setActiveSection(SECTIONS.RELIGIOUS)}
          >
            <MaterialCommunityIcons
              name="mosque"
              size={24}
              color={activeSection === SECTIONS.RELIGIOUS ? colors.primary : colors.text}
            />
            <Text
              style={[
                styles.tabText,
                { color: activeSection === SECTIONS.RELIGIOUS ? colors.primary : colors.text }
              ]}
            >
              {t('religious')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tab,
              activeSection === SECTIONS.EDUCATION && {
                borderBottomWidth: 2,
                borderBottomColor: colors.primary
              }
            ]}
            onPress={() => setActiveSection(SECTIONS.EDUCATION)}
          >
            <MaterialCommunityIcons
              name="school"
              size={24}
              color={activeSection === SECTIONS.EDUCATION ? colors.primary : colors.text}
            />
            <Text
              style={[
                styles.tabText,
                { color: activeSection === SECTIONS.EDUCATION ? colors.primary : colors.text }
              ]}
            >
              {t('education')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tab,
              activeSection === SECTIONS.MARRIAGE && {
                borderBottomWidth: 2,
                borderBottomColor: colors.primary
              }
            ]}
            onPress={() => setActiveSection(SECTIONS.MARRIAGE)}
          >
            <MaterialCommunityIcons
              name="ring"
              size={24}
              color={activeSection === SECTIONS.MARRIAGE ? colors.primary : colors.text}
            />
            <Text
              style={[
                styles.tabText,
                { color: activeSection === SECTIONS.MARRIAGE ? colors.primary : colors.text }
              ]}
            >
              {t('marriage')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tab,
              activeSection === SECTIONS.BIO && {
                borderBottomWidth: 2,
                borderBottomColor: colors.primary
              }
            ]}
            onPress={() => setActiveSection(SECTIONS.BIO)}
          >
            <MaterialCommunityIcons
              name="text"
              size={24}
              color={activeSection === SECTIONS.BIO ? colors.primary : colors.text}
            />
            <Text
              style={[
                styles.tabText,
                { color: activeSection === SECTIONS.BIO ? colors.primary : colors.text }
              ]}
            >
              {t('bio')}
            </Text>
          </TouchableOpacity>
        </View>

        {activeSection === SECTIONS.BASIC && renderBasicInfoSection()}
        {activeSection === SECTIONS.RELIGIOUS && renderReligiousInfoSection()}
        {activeSection === SECTIONS.EDUCATION && renderEducationSection()}
        {activeSection === SECTIONS.MARRIAGE && renderMarriageSection()}
        {activeSection === SECTIONS.BIO && renderBioSection()}

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.cancelButton, { borderColor: colors.border }]}
            onPress={async () => {
              // Check if we can go back (if there's a previous screen)
              const canGoBack = navigation.canGoBack();
              if (canGoBack) {
                navigation.goBack();
              } else {
                // If we can't go back, we're likely in the CompleteProfile screen
                // Use skipProfileCompletion to update the profile completion status
                const skipResult = await skipProfileCompletion();
                if (skipResult.success) {
                  // The AppNavigator will automatically redirect to the main app
                  // when the profile_completion_percentage is updated
                  console.log('Profile completion skipped successfully');
                } else {
                  console.error('Failed to skip profile completion:', skipResult.error);
                }
              }
            }}
            disabled={saving}
          >
            <Text style={[styles.cancelButtonText, { color: colors.text }]}>
              {t('cancel')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.primary }]}
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <Text style={styles.saveButtonText}>
                {t('save')}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
  tab: {
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 5,
    width: '20%',
  },
  tabText: {
    fontSize: 12,
    marginTop: 5,
    textAlign: 'center',
  },
  formSection: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  formGroup: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  textInput: {
    height: 45,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 8,
    overflow: 'hidden',
    height: 45,
    justifyContent: 'center',
    // We'll set background color dynamically, but ensure it's visible
    elevation: 1, // Add elevation for Android
    shadowColor: '#000', // Add shadow for iOS
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  textArea: {
    height: 100,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
    textAlignVertical: 'top',
    paddingTop: 10,
  },
  buttonContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    padding: 15,
    marginBottom: 30,
  },
  cancelButton: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginEnd: 10,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#ffffff',
  },
});

export default EditProfile;