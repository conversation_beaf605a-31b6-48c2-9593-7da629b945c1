import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  I18nManager,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslationFlat } from '../hooks/useTranslationFlat';
import { useLanguage } from '../context/LanguageContext';

const LanguageSwitcher = ({ style, showLabel = true, compact = false }) => {
  const { t } = useTranslationFlat();
  const { language, changeLanguage, isRTL } = useLanguage();

  const toggleLanguage = async () => {
    const newLang = language === 'en' ? 'ar' : 'en';
    await changeLanguage(newLang);
  };

  if (compact) {
    return (
      <TouchableOpacity
        style={[styles.compactButton, style]}
        onPress={toggleLanguage}
        accessibilityLabel={language === 'en' ? 'Switch to Arabic' : 'Switch to English'}
      >
        <Text style={styles.languageCode}>
          {language === 'en' ? 'عربي' : 'EN'}
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.container, 
        {flexDirection: isRTL ? 'row-reverse' : 'row'},
        style
      ]}
      onPress={toggleLanguage}
      accessibilityLabel={language === 'en' ? 'Switch to Arabic' : 'Switch to English'}
    >
      <Ionicons name="language" size={24} color="#fff" />
      {showLabel && (
        <Text style={[
          styles.text,
          {marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0}
        ]}>
          {language === 'en' ? 'عربي' : 'English'}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: '#8A2BE2',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  text: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  compactButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#8A2BE2',
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  languageCode: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
});

export default LanguageSwitcher;