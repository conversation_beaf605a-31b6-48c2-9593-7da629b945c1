import React, { createContext, useContext } from 'react';
import { I18nManager } from 'react-native';
import { useLanguage } from './LanguageContext';

// Create RTL context
const RTLContext = createContext({
  isRTL: false,
  align: {},
  direction: {},
  margin: () => ({}),
  padding: () => ({}),
  position: () => ({}),
  positionAbsolute: () => ({}),
  iconTransform: {},
});

// RTL Provider component
export const RTLProvider = ({ children }) => {
  const { language } = useLanguage();
  const isRTL = language === 'ar';
  
  // RTL-aware styles and utilities
  const rtlUtils = {
    isRTL,
    align: {
      textAlign: isRTL ? 'right' : 'left',
    },
    direction: {
      flexDirection: isRTL ? 'row-reverse' : 'row',
    },
    margin: (start, top, end, bottom) => ({
      marginTop: top,
      marginBottom: bottom,
      marginStart: start,
      marginEnd: end,
    }),
    padding: (start, top, end, bottom) => ({
      paddingTop: top,
      paddingBottom: bottom,
      paddingStart: start,
      paddingEnd: end,
    }),
    position: (start) => ({
      [isRTL ? 'right' : 'left']: start,
    }),
    positionAbsolute: (top, end, bottom, start) => ({
      position: 'absolute',
      top,
      bottom,
      start,
      end,
    }),
    iconTransform: {
      transform: [{ scaleX: isRTL ? -1 : 1 }],
    },
  };

  return (
    <RTLContext.Provider value={rtlUtils}>
      {children}
    </RTLContext.Provider>
  );
};

// Custom hook to use RTL context
export const useRTL = () => useContext(RTLContext);
