# ninja log v5
10347	12114	7695271040772757	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/Desktop/Nasebi_v2/nasebi-app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	d76b9554dc459c1b
9463	11697	7695271036689710	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	fa524d3acf33b623
134	6251	7695305757843004	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	38f47f8d8bc1a8a3
70	11228	7695305807704535	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	59dc25346ca631e3
37	5506	7695305750541470	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/Desktop/Nasebi_v2/nasebi-app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	a944cba1f9bc966d
87	6057	7695305755908898	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	573e15e4fb93f481
147	6987	7695305765497614	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	4b80c209fef933df
129	6573	7695305761252403	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	d4dfa36f1db1d35
122	6886	7695305764458061	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	42110fab65cba825
141	7032	7695305765888238	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	8cd3d223c01c26a9
59	11078	7695305806234578	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	2380bd3bdae708ee
10612	12118	7695271040926610	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/Desktop/Nasebi_v2/nasebi-app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	46df6edf4ecab38
11016	13326	7695271052770479	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	6e5d086da58637c6
99	10437	7695305799710877	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	1187ef89847a3a51
9408	19624	7695271113358668	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	37006cc79fe49414
105	10698	7695305802449965	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	7ac3d0a87f8b611a
13979	15039	7695305845430610	../../../../build/intermediates/cxx/Debug/235c3n3i/obj/arm64-v8a/libexpo-modules-core.so	ae7c0e3d5ab8dc1a
111	10933	7695305804833636	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	4f45ef590244722
11698	20740	7695271126947032	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	caeaf4b7f475cffe
94	11753	7695305813055246	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIInteropModuleRegistry.cpp.o	db4417e799645ef4
10839	22993	7695271149496039	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	ff68a23dfdf8ff87
117	13978	7695305835065174	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	5cec62c10a205e4
8508	24342	7695271162912990	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	d8110722a7a9ddcd
1	58	0	clean	6455a38a52da88c5
