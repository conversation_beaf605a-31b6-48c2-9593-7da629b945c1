const fetchMessages = async () => {
  setLoading(true);
  try {
    const response = await api.get(`/api/conversations/${matchId}/messages`);

    // Process messages to ensure all required properties exist
    const processedMessages = (response.data.messages || []).map(msg => {
      return {
        ...msg,
        id: msg.id || Math.random().toString(36).substring(2, 9),
        text: msg.text || '',
        timestamp: msg.timestamp || new Date().toISOString(),
        isSender: msg.isSender !== undefined ? msg.isSender : false,
        isRead: msg.isRead !== undefined ? msg.isRead : true,
      };
    });

    setMessages(processedMessages);
  } catch (error) {
    console.log('Error fetching messages:', error);
    // Show error to user instead of using dummy data
    Alert.alert('Error', 'Failed to load messages. Please try again later.');
  } finally {
    setLoading(false);
    if (initialLoad) {
      setInitialLoad(false);
    }
  }
};