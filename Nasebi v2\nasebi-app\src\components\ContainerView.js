import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { useRTL } from '../hooks/useRTL';
import { useLanguage } from '../context/LanguageContext';

/**
 * A container component that automatically handles theme and RTL layout
 * Use this as the root container for screens
 */
const ContainerView = ({
  children,
  style,
  useSafeArea = true,
  withPadding = false,
  forceDirection,
}) => {
  const { colors } = useTheme();
  const rtl = useRTL();
  const { language } = useLanguage();
  
  // Determine the flexDirection based on RTL or force parameter
  const flexDirection = forceDirection || (rtl.isRTL ? 'row-reverse' : 'row');
  
  const containerStyles = [
    styles.container,
    { backgroundColor: colors.background },
    withPadding && styles.withPadding,
    style,
  ];
  
  const contentWrapper = (
    <View style={[styles.contentContainer, { direction: rtl.isRTL ? 'rtl' : 'ltr' }]}>
      {children}
    </View>
  );
  
  if (useSafeArea) {
    return (
      <SafeAreaView style={containerStyles}>
        {contentWrapper}
      </SafeAreaView>
    );
  }
  
  return (
    <View style={containerStyles}>
      {contentWrapper}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  withPadding: {
    padding: 15,
  },
});

export default ContainerView; 