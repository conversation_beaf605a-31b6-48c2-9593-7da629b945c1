import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '../services/api';
import config from '../config/config';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [userToken, setUserToken] = useState(null);
  const [userData, setUserData] = useState(null);
  const [isSignout, setIsSignout] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Bootstrap async function
    const bootstrapAsync = async () => {
      try {
        // Try to retrieve the user token
        const token = await AsyncStorage.getItem('auth-token');
        const userJSON = await AsyncStorage.getItem('user-data');

        if (token && userJSON) {
          const user = JSON.parse(userJSON);
          setUserToken(token);
          setUserData(user);
          setIsAuthenticated(true);

          console.log("Loaded stored auth data:", { token: token.substring(0, 10) + '...', user: user.email });

          // Always validate token with the server
          try {
            console.log("Validating token with server...");
            const validateResponse = await api.get('/api/auth/validate', {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            // If validation successful, fetch the latest user profile
            if (validateResponse.data && validateResponse.data.valid) {
              console.log("Token is valid, fetching latest profile data");
              try {
                // Fetch user profile data
                const profileResponse = await api.get('/api/profile', {
                  headers: {
                    'Authorization': `Bearer ${token}`
                  }
                });

                if (profileResponse.data) {
                  // Update userData with the latest profile data
                  const updatedUserData = {
                    ...user,
                    ...profileResponse.data
                  };
                  setUserData(updatedUserData);

                  // Store the updated user data
                  await AsyncStorage.setItem('user-data', JSON.stringify(updatedUserData));
                  console.log("Updated user data with latest profile");
                }
              } catch (profileError) {
                console.log("Error fetching profile data:", profileError);
                if (profileError.response && profileError.response.status === 401) {
                  console.log("Profile fetch failed due to auth error, logging out");
                  await logout();
                }
              }
            } else {
              console.log("Token validation returned invalid status, logging out");
              await logout();
            }
          } catch (error) {
            console.log("Token validation error:", error);
            // If token is invalid, log the user out
            if (error.response && error.response.status === 401) {
              console.log("Token validation failed with 401, logging out");
              await logout();
            } else {
              console.log("Token validation failed with non-auth error, continuing with stored data");
              // For other errors (like network issues), continue with stored data
            }
          }
        } else {
          console.log("No stored auth data found");
          setUserToken(null);
          setUserData(null);
          setIsAuthenticated(false);
        }
      } catch (e) {
        // Error retrieving data, provide default values
        console.log('Failed to load auth state:', e);
        setUserToken(null);
        setUserData(null);
        setIsAuthenticated(false);
      } finally {
        // Always set loading to false regardless of outcome
        setIsLoading(false);
      }
    };

    bootstrapAsync();
  }, []);

  const register = async (userData) => {
    try {
      setIsLoading(true);
      console.log('Attempting to register user:', userData.email);

      let response;
      try {
        response = await api.post('/api/auth/register', userData);
        console.log('Register API response:', response.data);
      } catch (apiError) {
        console.error('API call failed:', apiError.message);
        if (apiError.response) {
          console.error('Server response:', apiError.response.status, apiError.response.data);
        }

        // No mock data fallbacks - using real authentication only
        console.log('Registration API call failed - no fallback to mock data');

        throw apiError;
      }

      // Check if we have a valid response with token and user data
      if (!response.data || !response.data.token || !response.data.user) {
        console.error('Invalid server response:', response.data);
        throw new Error('Invalid response from server');
      }

      const { token, user } = response.data;

      // Store user token and data
      await AsyncStorage.setItem('auth-token', token);
      await AsyncStorage.setItem('user-data', JSON.stringify(user));

      setUserToken(token);
      setUserData(user);
      setIsSignout(false);
      setIsAuthenticated(true);

      console.log('Registration successful for:', user.email);
      return { success: true, user };
    } catch (error) {
      console.error('Registration error:', error);
      let errorMessage = 'Registration failed. Please try again.';

      if (error.message === 'Network Error') {
        errorMessage = 'Cannot connect to server. Please check your internet connection and try again.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Server is taking too long to respond. Please try again later.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setIsLoading(true);
      console.log('Attempting login for:', email);

      // No mock data or test accounts - using real authentication only
      console.log('Attempting to login with real credentials');

      let response;
      try {
        response = await api.post('/api/auth/login', { email, password });
        console.log('Login API response:', response.data);
      } catch (apiError) {
        console.error('API call failed:', apiError.message);
        if (apiError.response) {
          console.error('Server response:', apiError.response.status, apiError.response.data);
        }

        // No fallback to mock mode - using real authentication only
        console.log('API call failed - no fallback to mock data');

        throw apiError;
      }

      // Check if we have a valid response with token and user data
      if (!response.data || !response.data.token || !response.data.user) {
        console.error('Invalid server response:', response.data);
        throw new Error('Invalid response from server');
      }

      const { token, user } = response.data;

      // Store user token and data
      await AsyncStorage.setItem('auth-token', token);
      await AsyncStorage.setItem('user-data', JSON.stringify(user));

      setUserToken(token);
      setUserData(user);
      setIsSignout(false);
      setIsAuthenticated(true);

      // Fetch additional profile data
      try {
        const profileResponse = await api.get('/api/profile');
        if (profileResponse.data) {
          // Update userData with the latest profile data
          const updatedUserData = {
            ...user,
            ...profileResponse.data
          };
          setUserData(updatedUserData);

          // Store the updated user data
          await AsyncStorage.setItem('user-data', JSON.stringify(updatedUserData));
          console.log("Updated user data with profile after login");

          return { success: true, user: updatedUserData };
        }
      } catch (profileError) {
        console.log("Error fetching profile data after login:", profileError);
      }

      console.log('Login successful for:', user.email);
      return { success: true, user };
    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = 'Invalid email or password';

      if (error.message === 'Network Error') {
        errorMessage = 'Cannot connect to server. Please check your internet connection and try again.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Server is taking too long to respond. Please try again later.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      // Call logout API if needed
      try {
        await api.post('/api/auth/logout');
      } catch (e) {
        // Continue with local logout even if API call fails
        console.log('Logout API error:', e);
      }

      // Remove user token and data
      await AsyncStorage.removeItem('auth-token');
      await AsyncStorage.removeItem('user-data');
    } catch (e) {
      console.log('Logout error:', e);
    } finally {
      setUserToken(null);
      setUserData(null);
      setIsSignout(true);
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  };

  const updateUser = async (updatedUserData) => {
    setIsLoading(true);
    try {
      console.log('Updating user with data:', JSON.stringify(updatedUserData, null, 2));

      // First try to update via the profile endpoint
      try {
        const profileResponse = await api.post('/api/profile', updatedUserData);
        if (profileResponse.data && profileResponse.data.profile) {
          // Merge the updated profile with existing user data
          const mergedUserData = {
            ...userData,
            ...profileResponse.data.profile
          };

          // Update stored user data
          await AsyncStorage.setItem('user-data', JSON.stringify(mergedUserData));
          setUserData(mergedUserData);

          setIsLoading(false);
          return { success: true, user: mergedUserData };
        }
      } catch (profileError) {
        console.log('Error updating via profile endpoint, trying users endpoint:', profileError);
      }

      // Fallback to users endpoint
      console.log('Using fallback endpoint: PUT /api/users/profile');
      const response = await api.put('/api/users/profile', updatedUserData);
      const updatedUser = response.data;

      console.log('Profile updated successfully:', JSON.stringify(updatedUser, null, 2));

      // Merge with existing user data
      const mergedUserData = {
        ...userData,
        ...updatedUser
      };

      // Update stored user data
      await AsyncStorage.setItem('user-data', JSON.stringify(mergedUserData));
      setUserData(mergedUserData);

      return { success: true, user: mergedUserData };
    } catch (error) {
      console.log('Update user error:', error);

      // Try to provide more detailed error information
      let errorMessage = 'Failed to update profile';
      if (error.response) {
        errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;
        console.log('Error response data:', error.response.data);
      } else if (error.request) {
        errorMessage = 'No response from server. Check your network connection.';
      } else {
        errorMessage = error.message || 'Unknown error occurred';
      }

      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Function to bypass profile completion requirement
  const skipProfileCompletion = async () => {
    try {
      console.log('Skipping profile completion');

      // Update the user data with a profile_completion_percentage of 80
      // This will allow the user to bypass the profile completion screen
      const updatedUserData = {
        ...userData,
        profile_completion_percentage: 80
      };

      // Update stored user data
      await AsyncStorage.setItem('user-data', JSON.stringify(updatedUserData));
      setUserData(updatedUserData);

      return { success: true };
    } catch (error) {
      console.log('Skip profile completion error:', error);
      return { success: false, error: 'Failed to skip profile completion' };
    }
  };

  const updatePassword = async (currentPassword, newPassword) => {
    setIsLoading(true);
    try {
      const response = await api.post('/api/auth/update-password', {
        currentPassword,
        newPassword
      });

      return { success: true, message: response.data.message || 'Password updated successfully' };
    } catch (error) {
      console.error('Update password error:', error);
      let errorMessage = 'Failed to update password. Please try again.';

      if (error.message === 'Network Error') {
        errorMessage = 'Cannot connect to server. Please check your internet connection and try again.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const forgotPassword = async (email) => {
    setIsLoading(true);
    try {
      const response = await api.post('/api/auth/forgot-password', { email });
      return { success: true, message: response.data.message || 'Password reset email sent' };
    } catch (error) {
      console.error('Forgot password error:', error);
      let errorMessage = 'Failed to send reset email. Please try again.';

      if (error.message === 'Network Error') {
        errorMessage = 'Cannot connect to server. Please check your internet connection and try again.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (token, newPassword) => {
    setIsLoading(true);
    try {
      const response = await api.post('/api/auth/reset-password', { token, newPassword });
      return { success: true, message: response.data.message || 'Password has been reset successfully' };
    } catch (error) {
      console.error('Reset password error:', error);
      let errorMessage = 'Failed to reset password. Please try again.';

      if (error.message === 'Network Error') {
        errorMessage = 'Cannot connect to server. Please check your internet connection and try again.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isLoading,
        isAuthenticated,
        isSignout,
        userData,
        user: userData, // Add user property that references userData for backward compatibility
        userToken,
        login,
        logout,
        register,
        updateUser,
        updatePassword,
        forgotPassword,
        resetPassword,
        skipProfileCompletion,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;