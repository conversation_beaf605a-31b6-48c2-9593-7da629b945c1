import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import CardCarousel from '../../components/CardCarousel';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../context/ThemeContext';
import { useRTL } from '../../hooks/useRTL';


const CardsScreen = () => {
  const { t } = useTranslation();
  const { colors } = useTheme();

  // Example data - replace with your actual data
  const cardData = [
    { id: '1', name: '<PERSON>', age: 28, image: require('../../assets/profile1.jpg') },
    { id: '2', name: '<PERSON>', age: 24, image: require('../../assets/profile2.jpg') },
    { id: '3', name: '<PERSON>', age: 30, image: require('../../assets/profile3.jpg') },
    // Add more cards as needed
  ];

  const renderCard = (item) => {
    return (
      <View style={[styles.card, { backgroundColor: colors.card }]}>
        <Image source={item.image} style={styles.image} />
        <View style={styles.cardContent}>
          <Text style={[styles.name, { color: colors.text }]}>{item.name}, {item.age}</Text>
          <Text style={[styles.bio, { color: colors.subtext }]}>{t('profile.bioPlaceholder')}</Text>
        </View>
      </View>
    );
  };

  const handleSwipeLeft = (item) => {
    console.log(t('home.nope') + ':', item.name);
    // Add your unlike logic here (e.g., API call)
  };

  const handleSwipeRight = (item) => {
    console.log(t('home.like') + ':', item.name);
    // Add your like logic here (e.g., API call)
  };

  const renderNoMoreCards = () => {
    return (
      <View style={[styles.noMoreCards, { backgroundColor: colors.card }]}>
        <Text style={[styles.noMoreCardsText, { color: colors.text }]}>{t('home.noMoreProfiles')}</Text>
        <Text style={{ color: colors.subtext }}>{t('matches.tryAgainLater')}</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <CardCarousel
        data={cardData}
        renderCard={renderCard}
        onSwipeLeft={handleSwipeLeft}
        onSwipeRight={handleSwipeRight}
        renderNoMoreCards={renderNoMoreCards}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    flex: 1,
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  image: {
    width: '100%',
    height: '70%',
    resizeMode: 'cover',
  },
  cardContent: {
    padding: 15,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  bio: {
    marginTop: 10,
    fontSize: 16,
  },
  noMoreCards: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  noMoreCardsText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  }
});

export default CardsScreen;