# Nasebi App Style Guide for Multilingual and Theme Support

This guide provides best practices for ensuring consistent support for RTL languages, dark/light themes, and translations throughout the application.

## RTL (Right-to-Left) Support

### Use Logical Properties

Instead of using directional properties (left/right), use logical properties:

```jsx
// ❌ Don't use
const styles = StyleSheet.create({
  container: {
    marginLeft: 10,
    paddingRight: 15,
  }
});

// ✅ Do use
const styles = StyleSheet.create({
  container: {
    marginStart: 10,
    paddingEnd: 15,
  }
});
```

### Use the `useRTL` Hook

The `useRTL` hook provides utility functions for handling RTL layouts:

```jsx
import { useRTL } from '../hooks/useRTL';

const MyComponent = () => {
  const rtl = useRTL();
  
  return (
    <View style={rtl.direction}>
      <Text style={rtl.align}>Hello</Text>
    </View>
  );
};
```

### Key RTL utilities:

- `rtl.isRTL`: Boolean indicating if the layout is RTL
- `rtl.align`: Text alignment based on direction
- `rtl.direction`: Flex direction based on RTL
- `rtl.margin(start, top, end, bottom)`: RTL-aware margins
- `rtl.padding(start, top, end, bottom)`: RTL-aware paddings
- `rtl.position(start)`: RTL-aware positioning
- `rtl.positionAbsolute(top, end, bottom, start)`: Full RTL-aware absolute positioning
- `rtl.iconTransform`: Transform to mirror icons in RTL

### For Icons:

Always handle icon mirroring in RTL layouts:

```jsx
<Ionicons
  name="arrow-forward"
  style={rtl.iconTransform} // Flips icon horizontally in RTL mode
/>
```

## Theme Support

### Use Colors from Theme Context

Always use colors from the theme context instead of hardcoding colors:

```jsx
// ❌ Don't use
<Text style={{ color: '#333' }}>Hello</Text>

// ✅ Do use
const { colors } = useTheme();
<Text style={{ color: colors.text }}>Hello</Text>
```

### Available Theme Colors:

- `colors.background`: Main background color
- `colors.surface`: Secondary surfaces
- `colors.text`: Primary text color
- `colors.subtext`: Secondary text color
- `colors.border`: Border color
- `colors.card`: Card background
- `colors.divider`: Divider lines
- `colors.primary`: Brand primary color
- `colors.secondary`: Secondary accent color
- `colors.error`, `colors.success`, `colors.info`, `colors.warning`: Status colors
- `colors.buttonText`: Text color on buttons

## Translation Support

### Use Translation Hook

Always use the translation hook for text:

```jsx
// ❌ Don't use
<Text>Hello World</Text>

// ✅ Do use
const { t } = useTranslation();
<Text>{t('common.helloWorld')}</Text>
```

### Translation Organization

Translations are organized by feature and stored in:
- `src/translations/en.json` (English)
- `src/translations/ar.json` (Arabic)

### Adding New Translations

1. Add the new key to both language files
2. Use a consistent naming convention: `feature.action.description`
3. Verify both RTL and LTR rendering after adding

## Using Container Components

For new screens, use the ContainerView component for consistent handling:

```jsx
import ContainerView from '../../components/ContainerView';

const MyScreen = () => {
  return (
    <ContainerView useSafeArea withPadding>
      {/* Screen content */}
    </ContainerView>
  );
};
```

## Debugging Layout Issues

For development, you can use the layout checker utilities:

```jsx
import { debugLayoutIssues } from '../utils/layoutChecker';

// In your component
const styles = StyleSheet.create({...});
debugLayoutIssues('MyComponent', styles);
```

This will log any potential RTL or theme issues to the console during development. 