{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017", "es2018", "esnext"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "skipLibCheck": true, "downlevelIteration": true, "useDefineForClassFields": true, "experimentalDecorators": true, "types": ["react-native"], "baseUrl": ".", "paths": {"*": ["node_modules/*"]}}, "exclude": ["node_modules", "babel.config.js", "metro.config.js"], "extends": "expo/tsconfig.base"}