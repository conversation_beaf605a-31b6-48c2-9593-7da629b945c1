/**
 * <PERSON><PERSON>t to fix common RTL/LTR issues in a specific file
 * 
 * Usage:
 * node scripts/fix-rtl-issues.js path/to/file.js
 */

const fs = require('fs');
const path = require('path');

// RTL sensitive properties that should use logical equivalents
const RTL_SENSITIVE_PROPS = {
  'left': 'start',
  'right': 'end',
  'marginLeft': 'marginStart',
  'marginRight': 'marginEnd',
  'paddingLeft': 'paddingStart',
  'paddingRight': 'paddingEnd',
  'borderLeftWidth': 'borderStartWidth',
  'borderRightWidth': 'borderEndWidth',
  'borderLeftColor': 'borderStartColor',
  'borderRightColor': 'borderEndColor',
};

// Get file path from command line arguments
const filePath = process.argv[2];

if (!filePath) {
  console.error('Please provide a file path to fix');
  console.log('Usage: node scripts/fix-rtl-issues.js path/to/file.js');
  process.exit(1);
}

// Check if file exists
if (!fs.existsSync(filePath)) {
  console.error(`File not found: ${filePath}`);
  process.exit(1);
}

// Read file content
let content = fs.readFileSync(filePath, 'utf8');
const originalContent = content;

// Check if the file already imports useRTL
const hasUseRTLImport = content.includes('useRTL');

// Add useRTL import if needed
if (!hasUseRTLImport && (content.includes('StyleSheet') || content.includes('<View') || content.includes('<Text'))) {
  // Find the last import statement
  const importRegex = /import.*from.*;/g;
  const imports = [...content.matchAll(importRegex)];
  
  if (imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const insertPosition = lastImport.index + lastImport[0].length;
    
    // Insert useRTL import after the last import
    content = content.slice(0, insertPosition) + 
              '\nimport { useRTL } from \'../hooks/useRTL\';\n' + 
              content.slice(insertPosition);
    
    console.log('Added useRTL import');
  }
}

// Replace RTL-sensitive properties
Object.keys(RTL_SENSITIVE_PROPS).forEach(prop => {
  const regex = new RegExp(`${prop}\\s*:\\s*`, 'g');
  const replacement = `${RTL_SENSITIVE_PROPS[prop]}: `;
  
  const matches = content.match(regex);
  if (matches) {
    content = content.replace(regex, replacement);
    console.log(`Replaced ${matches.length} occurrences of ${prop} with ${RTL_SENSITIVE_PROPS[prop]}`);
  }
});

// Fix hardcoded text alignment
const textAlignLeftRegex = /textAlign:\s*['"]left['"]/g;
const textAlignRightRegex = /textAlign:\s*['"]right['"]/g;

if (content.match(textAlignLeftRegex) || content.match(textAlignRightRegex)) {
  // Check if we need to add rtl declaration
  if (!content.includes('const rtl = useRTL()') && !content.includes('const { isRTL } = useRTL()')) {
    // Find a good place to add rtl declaration - after component function start
    const componentRegex = /const\s+(\w+)\s*=\s*\(\s*\{[^}]*\}\s*\)\s*=>\s*\{/;
    const componentMatch = content.match(componentRegex);
    
    if (componentMatch) {
      const insertPosition = componentMatch.index + componentMatch[0].length;
      content = content.slice(0, insertPosition) + 
                '\n  const rtl = useRTL();\n' + 
                content.slice(insertPosition);
      
      console.log('Added rtl declaration');
    }
  }
  
  // Replace hardcoded text alignment
  content = content.replace(textAlignLeftRegex, 'textAlign: rtl.isRTL ? \'right\' : \'left\'');
  content = content.replace(textAlignRightRegex, 'textAlign: rtl.isRTL ? \'left\' : \'right\'');
  
  console.log('Fixed hardcoded text alignment');
}

// Check if content was modified
if (content !== originalContent) {
  // Write modified content back to file
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`Fixed RTL issues in ${path.basename(filePath)}`);
} else {
  console.log(`No RTL issues to fix in ${path.basename(filePath)}`);
}
