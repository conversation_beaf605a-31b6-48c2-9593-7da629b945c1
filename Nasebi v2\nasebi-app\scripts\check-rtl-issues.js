/**
 * <PERSON><PERSON>t to check for RTL/LTR issues across all screens and components
 * 
 * Usage:
 * node scripts/check-rtl-issues.js
 */

const fs = require('fs');
const path = require('path');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;

// Directories to scan
const DIRS_TO_SCAN = [
  path.join(__dirname, '..', 'src', 'screens'),
  path.join(__dirname, '..', 'src', 'components'),
];

// RTL sensitive properties that should use logical equivalents
const RTL_SENSITIVE_PROPS = {
  'left': 'start',
  'right': 'end',
  'marginLeft': 'marginStart',
  'marginRight': 'marginEnd',
  'paddingLeft': 'paddingStart',
  'paddingRight': 'paddingEnd',
  'borderLeftWidth': 'borderStartWidth',
  'borderRightWidth': 'borderEndWidth',
  'borderLeftColor': 'borderStartColor',
  'borderRightColor': 'borderEndColor',
};

// Track issues found
const issues = {
  rtlProps: [],
  missingRTLHook: [],
  absolutePositioning: [],
  hardcodedTextAlign: [],
};

/**
 * Check if a file contains RTL-sensitive properties
 */
function checkFileForRTLIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath);
    
    // Skip non-JS/JSX files
    if (!filePath.endsWith('.js') && !filePath.endsWith('.jsx')) {
      return;
    }
    
    console.log(`Checking ${fileName}...`);
    
    // Check for RTL-sensitive properties in StyleSheet
    Object.keys(RTL_SENSITIVE_PROPS).forEach(prop => {
      const regex = new RegExp(`${prop}\\s*:\\s*`, 'g');
      const matches = content.match(regex);
      
      if (matches) {
        issues.rtlProps.push({
          file: fileName,
          path: filePath,
          property: prop,
          suggestedReplacement: RTL_SENSITIVE_PROPS[prop],
          count: matches.length,
        });
      }
    });
    
    // Check for absolute positioning without RTL consideration
    if (content.includes('position: \'absolute\'') || content.includes('position:"absolute"')) {
      // Check if the file uses useRTL for positioning
      if (!content.includes('useRTL') || 
          !content.includes('positionAbsolute') && 
          !(content.includes('isRTL') && (content.includes('right') || content.includes('left')))) {
        issues.absolutePositioning.push({
          file: fileName,
          path: filePath,
        });
      }
    }
    
    // Check for hardcoded text alignment
    if ((content.includes('textAlign: \'left\'') || content.includes('textAlign:"left"') ||
         content.includes('textAlign: \'right\'') || content.includes('textAlign:"right"')) &&
        !content.includes('isRTL')) {
      issues.hardcodedTextAlign.push({
        file: fileName,
        path: filePath,
      });
    }
    
    // Check if component has UI but doesn't use useRTL
    if ((content.includes('StyleSheet.create') || content.includes('<View') || content.includes('<Text')) && 
        !content.includes('useRTL') && !content.includes('isRTL')) {
      issues.missingRTLHook.push({
        file: fileName,
        path: filePath,
      });
    }
    
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

/**
 * Recursively scan directories for files
 */
function scanDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      scanDirectory(filePath);
    } else {
      checkFileForRTLIssues(filePath);
    }
  });
}

// Start scanning directories
console.log('Scanning for RTL/LTR issues...');
DIRS_TO_SCAN.forEach(scanDirectory);

// Print report
console.log('\n===== RTL/LTR ISSUES REPORT =====\n');

console.log(`Found ${issues.rtlProps.length} files with RTL-sensitive properties:`);
issues.rtlProps.forEach(issue => {
  console.log(`- ${issue.file}: ${issue.property} should be ${issue.suggestedReplacement} (${issue.count} occurrences)`);
});

console.log(`\nFound ${issues.absolutePositioning.length} files with absolute positioning that might not handle RTL properly:`);
issues.absolutePositioning.forEach(issue => {
  console.log(`- ${issue.file}`);
});

console.log(`\nFound ${issues.hardcodedTextAlign.length} files with hardcoded text alignment:`);
issues.hardcodedTextAlign.forEach(issue => {
  console.log(`- ${issue.file}`);
});

console.log(`\nFound ${issues.missingRTLHook.length} UI components that don't use useRTL or isRTL:`);
issues.missingRTLHook.forEach(issue => {
  console.log(`- ${issue.file}`);
});

console.log('\n===== END OF REPORT =====');
console.log('\nTo fix these issues:');
console.log('1. Replace directional properties (left/right) with logical properties (start/end)');
console.log('2. Use the useRTL hook in components with UI elements');
console.log('3. Use rtl.positionAbsolute() for absolute positioning');
console.log('4. Use rtl.align for text alignment instead of hardcoding left/right');
