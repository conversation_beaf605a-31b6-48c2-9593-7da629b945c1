diff --git a/node_modules/metro-resolver/src/resolve.js b/node_modules/metro-resolver/src/resolve.js
index a1e8e872..3e4fbe82 100644
--- a/node_modules/metro-resolver/src/resolve.js
+++ b/node_modules/metro-resolver/src/resolve.js
@@ -144,6 +144,13 @@ function resolve(context, moduleName, platform) {
     }
   }
 
+  // Explicitly check for .js extension when resolving from auth directory
+  if (moduleName.includes('auth/ForgotPasswordScreen') && !moduleName.endsWith('.js')) {
+    try {
+      return resolveFile(context, moduleName + '.js', platform);
+    } catch (e) {}
+  }
+
   if (!context.disableHierarchicalLookup) {
     // Relative imports are guaranteed to start with a dot
     if (moduleName.startsWith('.'))
diff --git a/node_modules/metro-resolver/lib/resolve.js b/node_modules/metro-resolver/lib/resolve.js
index a1e8e872..3e4fbe82 100644
--- a/node_modules/metro-resolver/lib/resolve.js
+++ b/node_modules/metro-resolver/lib/resolve.js
@@ -144,6 +144,13 @@ function resolve(context, moduleName, platform) {
     }
   }

+  // Explicitly check for .js extension when resolving from auth directory
+  if (moduleName.includes('auth/ForgotPasswordScreen') && !moduleName.endsWith('.js')) {
+    try {
+      return resolveFile(context, moduleName + '.js', platform);
+    } catch (e) {}
+  }
+
   if (!context.disableHierarchicalLookup) {
     // Relative imports are guaranteed to start with a dot
     if (moduleName.startsWith('.')) 