import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { MIN_PASSWORD_LENGTH } from '../../config/constants';
import { useRTL } from '../../hooks/useRTL';


const ChangePasswordScreen = ({ navigation }) => {
  const { updatePassword } = useAuth();
  const { colors } = useTheme();
  const { t } = useTranslation();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const validateInputs = () => {
    // Reset previous errors
    setError('');

    // Check if current password is provided
    if (!currentPassword) {
      setError(t('password.currentPasswordRequired'));
      return false;
    }

    // Check if new password is provided
    if (!newPassword) {
      setError(t('password.newPasswordRequired'));
      return false;
    }

    // Check if new password meets minimum length requirement
    if (newPassword.length < MIN_PASSWORD_LENGTH) {
      setError(t('password.passwordTooShort', { length: MIN_PASSWORD_LENGTH }));
      return false;
    }

    // Check if passwords match
    if (newPassword !== confirmPassword) {
      setError(t('password.passwordsDoNotMatch'));
      return false;
    }

    return true;
  };

  const handleChangePassword = async () => {
    if (!validateInputs()) {
      return;
    }

    setLoading(true);
    try {
      const result = await updatePassword(currentPassword, newPassword);
      
      if (result.success) {
        setSuccess(true);
        setTimeout(() => {
          navigation.goBack();
        }, 2000);
      } else {
        setError(result.error);
      }
    } catch (error) {
      console.error('Change password error:', error);
      setError(t('password.changePasswordFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.formContainer}>
            <Text style={[styles.title, { color: colors.text }]}>
              {t('Change Password')}
            </Text>
            
            {success ? (
              <View style={styles.successContainer}>
                <Text style={[styles.successText, { color: colors.success }]}>
                  {t('password.passwordChangedSuccess')}
                </Text>
                <ActivityIndicator color={colors.primary} style={styles.redirectLoader} />
              </View>
            ) : (
              <View style={styles.form}>
                <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                  <TextInput
                    style={[styles.input, { color: colors.text }]}
                    placeholder={t('Current Password')}
                    placeholderTextColor={colors.subtext}
                    secureTextEntry
                    value={currentPassword}
                    onChangeText={(text) => {
                      setCurrentPassword(text);
                      setError('');
                    }}
                  />
                </View>
                
                <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                  <TextInput
                    style={[styles.input, { color: colors.text }]}
                    placeholder={t('New Password')}
                    placeholderTextColor={colors.subtext}
                    secureTextEntry
                    value={newPassword}
                    onChangeText={(text) => {
                      setNewPassword(text);
                      setError('');
                    }}
                  />
                </View>
                
                <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                  <TextInput
                    style={[styles.input, { color: colors.text }]}
                    placeholder={t('Confirm New Password')}
                    placeholderTextColor={colors.subtext}
                    secureTextEntry
                    value={confirmPassword}
                    onChangeText={(text) => {
                      setConfirmPassword(text);
                      setError('');
                    }}
                  />
                </View>
                
                {error ? (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {error}
                  </Text>
                ) : null}
                
                <TouchableOpacity
                  style={[styles.button, loading && styles.disabledButton]}
                  onPress={handleChangePassword}
                  disabled={loading}
                >
                  <LinearGradient
                    colors={[colors.primary, colors.primary]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.gradient}
                  >
                    {loading ? (
                      <ActivityIndicator color="#FFFFFF" />
                    ) : (
                      <Text style={styles.buttonText}>
                        {t('Change Password')}
                      </Text>
                    )}
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
    marginTop: 20,
  },
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  inputContainer: {
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 20,
  },
  input: {
    padding: 15,
    fontSize: 16,
  },
  button: {
    marginTop: 10,
    height: 55,
    borderRadius: 8,
    overflow: 'hidden',
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.7,
  },
  errorText: {
    marginTop: -10,
    marginBottom: 15,
    fontSize: 14,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
  },
  successText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
    textAlign: 'center',
  },
  redirectLoader: {
    marginTop: 20,
  },
});

export default ChangePasswordScreen; 