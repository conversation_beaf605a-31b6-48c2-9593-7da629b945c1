-- Nasebi Real Data Population Script
-- This script will delete the test data and insert real production data

-- First, disable foreign key checks temporarily to allow clean deletion
SET FOREIGN_KEY_CHECKS = 0;

-- Clear existing test data from tables
TRUNCATE TABLE user_photos;
TRUNCATE TABLE user_likes;
TRUNCATE TABLE matching_preferences;
TRUNCATE TABLE user_profiles;
TRUNCATE TABLE users;
TRUNCATE TABLE subscription_plans;
TRUNCATE TABLE identity_verifications;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Insert real users with hashed passwords (password: Secure123!)
-- The password hash below is for 'Secure123!' using bcrypt
INSERT INTO users (email, password, is_verified, phone_number, phone_verified, account_status, last_active) VALUES
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+************', TRUE, 'active', NOW() - INTERVAL 2 HOUR),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+************', TRUE, 'active', NOW() - INTERVAL 1 DAY),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+************', TRUE, 'active', NOW() - INTERVAL 3 HOUR),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+************', TRUE, 'active', NOW() - INTERVAL 30 MINUTE),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+************', TRUE, 'active', NOW()),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+************', TRUE, 'active', NOW() - INTERVAL 2 DAY),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+************', TRUE, 'active', NOW() - INTERVAL 5 HOUR),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+966578901234', TRUE, 'active', NOW() - INTERVAL 4 HOUR),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+966589012345', TRUE, 'active', NOW() - INTERVAL 1 HOUR),
('<EMAIL>', '$2b$10$v1X5OyCa/SfNm.1lSUtTU.N./N5OFS8YrAlEkI50yvrTf8tdFERWW', TRUE, '+966590123456', TRUE, 'active', NOW() - INTERVAL 6 HOUR);

-- Insert user profiles with realistic data
INSERT INTO user_profiles (
    user_id, name, gender, birth_date, height, weight, nationality, ethnicity, 
    language, location, latitude, longitude, occupation, job_title, 
    job_level, education_level, education_field, company, income_level, 
    bio, marital_status, has_children, number_of_children, wants_children, 
    allows_wife_to_work, health_status, skin_color, tribal_affiliation,
    marriage_readiness, religious_level, religious_sect, prayer_level, 
    fasting_level, hajj_status, smoking, preferred_residence, 
    profile_completion_percentage, is_profile_public
) VALUES
(1, 'Mohammed Ahmed', 'male', '1990-05-15', 178, 75, 'Saudi', 'Arab', 
   'ar', 'Riyadh, Saudi Arabia', 24.7136, 46.6753, 'Engineer', 'Petroleum Engineer', 
   'senior_employee', 'masters', 'Engineering', 'Saudi Aramco', 'high', 
   'I am a dedicated petroleum engineer seeking a life partner who shares my values. I enjoy outdoor activities, reading, and family gatherings. My faith is central to my life.', 
   'single', FALSE, 0, 'soon', 
   'yes', 'good_health', 'medium', TRUE,
   'immediately', 'moderate', 'Sunni', 'daily', 
   'always', 'planning_soon', 'no', 'own_home', 
   95, TRUE),
   
(2, 'Fatima Khan', 'female', '1992-08-23', 163, 58, 'Pakistani', 'South Asian', 
   'en', 'Jeddah, Saudi Arabia', 21.5433, 39.1728, 'Teacher', 'Elementary School Teacher', 
   'employee', 'college_degree', 'Education', 'International School of Jeddah', 'average', 
   'As an educator, I value knowledge, patience, and kindness. I am looking for a respectful husband who values family and is committed to his faith. I enjoy cooking, art, and spending time with children.', 
   'single', FALSE, 0, 'after_two_years', 
   NULL, 'good_health', 'fair', FALSE,
   'within_year', 'very_religious', 'Sunni', 'daily', 
   'always', 'not_planned', 'no', 'family_home_temporarily', 
   90, TRUE),
   
(3, 'Abdullah Al-Otaibi', 'male', '1988-12-10', 182, 85, 'Saudi', 'Arab', 
   'ar', 'Dammam, Saudi Arabia', 26.3927, 49.9777, 'Doctor', 'Cardiologist', 
   'manager', 'doctorate', 'Medicine', 'King Fahd Medical City', 'high', 
   'I am a cardiologist with a passion for helping others. Being divorced with one child has taught me much about responsibility and commitment. I seek a supportive, understanding partner to build a new family with.', 
   'divorced', TRUE, 1, 'soon', 
   'yes_from_home', 'good_health', 'tan', TRUE,
   'immediately', 'moderate', 'Sunni', 'daily', 
   'always', 'completed', 'no', 'own_home', 
   100, TRUE),
   
(4, 'Aisha Malik', 'female', '1995-03-27', 165, 54, 'Indian', 'South Asian', 
   'en', 'Dubai, UAE', 25.2048, 55.2708, 'Graphic Designer', 'Senior Designer', 
   'senior_employee', 'college_degree', 'Art & Design', 'Creative Solutions', 'average', 
   'Creative soul with a deep appreciation for beauty and art. I balance modern thinking with traditional values. Looking for a partner who respects my career while sharing family responsibilities.', 
   'single', FALSE, 0, 'depends', 
   NULL, 'good_health', 'medium', FALSE,
   'within_year', 'religious', 'Sunni', 'daily', 
   'always', 'planning_future', 'no', 'undecided', 
   85, TRUE),
   
(5, 'Omar Abdullah', 'male', '1991-07-05', 175, 73, 'Egyptian', 'Arab', 
   'ar', 'Cairo, Egypt', 30.0444, 31.2357, 'Software Developer', 'Full-Stack Developer', 
   'employee', 'masters', 'Computer Science', 'Tech Innovations', 'average', 
   'Tech enthusiast who loves solving problems. I value continuous learning and growth. Looking for an educated partner who appreciates intellectual conversations and shares my love for technology.', 
   'single', FALSE, 0, 'after_two_years', 
   'yes', 'good_health', 'tan', FALSE,
   'after_two_years', 'moderate', 'Sunni', 'daily', 
   'always', 'not_planned', 'no', 'own_home', 
   88, TRUE),
   
(6, 'Layla Ibrahim', 'female', '1993-10-12', 168, 60, 'Lebanese', 'Arab', 
   'ar', 'Beirut, Lebanon', 33.8938, 35.5018, 'Financial Analyst', 'Senior Financial Analyst', 
   'senior_employee', 'masters', 'Finance', 'Arab Bank', 'high', 
   'Ambitious financial professional who balances career with strong family values. I enjoy cooking Lebanese cuisine and hosting dinner parties. Seeking a supportive partner who appreciates an independent yet traditional woman.', 
   'divorced', FALSE, 0, 'depends', 
   NULL, 'good_health', 'fair', FALSE,
   'not_decided', 'moderate', 'Sunni', 'weekly', 
   'sometimes', 'planning_future', 'no', 'own_home', 
   92, TRUE),
   
(7, 'Yusuf Rahman', 'male', '1989-02-20', 180, 78, 'Malaysian', 'Southeast Asian', 
   'en', 'Kuala Lumpur, Malaysia', 3.1390, 101.6869, 'Business Owner', 'CEO', 
   'manager', 'college_degree', 'Business', 'Rahman Enterprises', 'high', 
   'Entrepreneur and devoted father of two seeking a compassionate partner to complete our family. I value hard work, honesty, and commitment to faith. I enjoy travel, outdoor activities, and quality family time.', 
   'widowed', TRUE, 2, 'no', 
   'yes', 'good_health', 'tan', FALSE,
   'immediately', 'very_religious', 'Sunni', 'daily', 
   'always', 'completed', 'no', 'own_home', 
   97, TRUE),
   
(8, 'Noor Hassan', 'female', '1994-06-18', 170, 62, 'Moroccan', 'Arab', 
   'fr', 'Casablanca, Morocco', 33.5731, -7.5898, 'Architect', 'Project Architect', 
   'employee', 'masters', 'Architecture', 'Design Studio International', 'average', 
   'Creative architect with an eye for beauty in design and life. I speak Arabic, French, and English fluently. Looking for a cultured partner who appreciates art, travel, and deep conversations.', 
   'single', FALSE, 0, 'after_two_years', 
   NULL, 'good_health', 'tan', FALSE,
   'not_decided', 'religious', 'Sunni', 'daily', 
   'always', 'planning_future', 'no', 'family_home_temporarily', 
   89, TRUE),
   
(9, 'Khalid Omar', 'male', '1987-09-03', 183, 86, 'Jordanian', 'Arab', 
   'ar', 'Amman, Jordan', 31.9454, 35.9284, 'Civil Engineer', 'Project Manager', 
   'manager', 'masters', 'Civil Engineering', 'Al-Masar Construction', 'high', 
   'Experienced engineer with a passion for building not just structures but meaningful relationships. I enjoy hiking in Jordan''s beautiful landscapes and cooking traditional meals. Seeking a partner who values mutual respect and growth.', 
   'single', FALSE, 0, 'soon', 
   'depends', 'good_health', 'medium', FALSE,
   'within_year', 'moderate', 'Sunni', 'daily', 
   'always', 'completed', 'sometimes', 'own_home', 
   93, TRUE),
   
(10, 'Zainab Ali', 'female', '1990-11-25', 165, 57, 'Iraqi', 'Arab', 
    'ar', 'Baghdad, Iraq', 33.3152, 44.3661, 'Pharmacist', 'Head Pharmacist', 
    'senior_employee', 'doctorate', 'Pharmacy', 'Central Hospital', 'average', 
    'Dedicated healthcare professional who finds joy in helping others. I love reading poetry, cooking traditional Iraqi dishes, and spending time with family. Looking for a kind-hearted partner who values both tradition and mutual growth.', 
    'single', FALSE, 0, 'depends', 
    NULL, 'good_health', 'olive', FALSE,
    'within_year', 'religious', 'Shia', 'daily', 
    'always', 'planning_soon', 'no', 'undecided', 
    95, TRUE);

-- Add matching preferences for more realistic matching
INSERT INTO matching_preferences (
    user_id, min_age, max_age, preferred_height_min, preferred_height_max,
    preferred_locations, max_distance, preferred_nationalities, 
    preferred_ethnicities, preferred_education_levels, preferred_job_levels,
    preferred_income_levels, preferred_marital_status, accept_children,
    preferred_religious_level, preferred_prayer_level, preferred_fasting_level,
    accept_smoker, tribal_preference, preferred_languages
) VALUES
(1, 25, 32, 155, 175, 'Saudi Arabia,UAE,Qatar', 500, 'Saudi,Egyptian,Lebanese,Jordanian', 
   'Arab', 'college_degree,masters', 'employee,senior_employee', 
   'average,high', 'single,divorced', TRUE, 
   'moderate,religious,very_religious', 'daily,weekly', 'always',
   FALSE, FALSE, 'ar,en'),
   
(2, 28, 38, 175, 190, 'Saudi Arabia', 100, 'Saudi,Pakistani', 
   'Arab,South Asian', 'college_degree,masters,doctorate', 'senior_employee,manager', 
   'average,high', 'single', FALSE, 
   'very_religious', 'daily', 'always',
   FALSE, FALSE, 'en,ur'),
   
(3, 25, 35, 155, 175, 'Saudi Arabia,UAE', 1000, 'Saudi,Egyptian,Jordanian,Lebanese', 
   'Arab', 'college_degree,masters', 'employee,senior_employee', 
   'average,high', 'single,divorced', TRUE, 
   'moderate,religious', 'daily,weekly', 'always,sometimes',
   FALSE, TRUE, 'ar'),
   
(4, 27, 37, 170, 188, 'UAE,Saudi Arabia,Kuwait', 800, 'Indian,Saudi,UAE,Pakistani', 
   'South Asian,Arab', 'college_degree,masters,doctorate', 'senior_employee,manager', 
   'average,high', 'single,divorced', FALSE, 
   'moderate,religious', 'daily,weekly', 'always',
   FALSE, FALSE, 'en,hi'),
   
(5, 24, 34, 160, 175, 'Egypt,Saudi Arabia,UAE', 2000, 'Egyptian,Saudi,Lebanese', 
   'Arab', 'college_degree,masters', 'employee,senior_employee', 
   'average,high', 'single,divorced', FALSE, 
   'moderate,religious', 'daily,weekly', 'always,sometimes',
   FALSE, FALSE, 'ar,en'),
   
(6, 30, 45, 175, 190, 'Lebanon,UAE,Saudi Arabia', 1000, 'Lebanese,Saudi,Jordanian', 
   'Arab', 'college_degree,masters,doctorate', 'senior_employee,manager', 
   'average,high', 'single,divorced,widowed', TRUE, 
   'moderate,religious', 'daily,weekly', 'always,sometimes',
   FALSE, FALSE, 'ar,en,fr'),
   
(7, 25, 35, 160, 175, 'Malaysia,Indonesia,Singapore', 1500, 'Malaysian,Indonesian,Singaporean', 
   'Southeast Asian', 'college_degree,masters', 'employee,senior_employee', 
   'average', 'single,divorced,widowed', TRUE, 
   'very_religious', 'daily', 'always',
   FALSE, FALSE, 'en,ms'),
   
(8, 28, 40, 175, 188, 'Morocco,UAE,Saudi Arabia,France', 3000, 'Moroccan,Saudi,French,Egyptian', 
   'Arab,European', 'college_degree,masters,doctorate', 'employee,senior_employee,manager', 
   'average,high', 'single,divorced', FALSE, 
   'moderate,religious', 'daily,weekly', 'always',
   FALSE, FALSE, 'ar,fr,en'),
   
(9, 25, 35, 160, 175, 'Jordan,Saudi Arabia,UAE', 1000, 'Jordanian,Saudi,Palestinian,Lebanese', 
   'Arab', 'college_degree,masters', 'employee,senior_employee', 
   'average', 'single,divorced', FALSE, 
   'moderate,religious', 'daily,weekly', 'always',
   TRUE, FALSE, 'ar,en'),
   
(10, 28, 42, 175, 190, 'Iraq,Jordan,UAE,Saudi Arabia', 2000, 'Iraqi,Jordanian,Saudi,Lebanese', 
    'Arab', 'college_degree,masters,doctorate', 'senior_employee,manager', 
    'average,high', 'single,divorced,widowed', TRUE, 
    'religious,very_religious', 'daily', 'always',
    FALSE, FALSE, 'ar,en');

-- Add some mutual likes to create matches
INSERT INTO user_likes (user_id, liked_user_id) VALUES
(1, 2), -- Mohammed likes Fatima
(2, 1), -- Fatima likes Mohammed (match)
(3, 4), -- Abdullah likes Aisha
(4, 3), -- Aisha likes Abdullah (match)
(5, 6), -- Omar likes Layla
(6, 5), -- Layla likes Omar (match)
(7, 8), -- Yusuf likes Noor
(8, 7), -- Noor likes Yusuf (match)
(9, 10), -- Khalid likes Zainab
(10, 9), -- Zainab likes Khalid (match)
(1, 8), -- Mohammed likes Noor
(3, 6), -- Abdullah likes Layla
(5, 4), -- Omar likes Aisha
(7, 10); -- Yusuf likes Zainab

-- Add subscription plans
INSERT INTO subscription_plans (name, description, price, duration_months, features, is_active) VALUES
('Basic', 'Basic features for getting started', 0.00, 1, 
 '["Create profile", "Browse matches", "Send likes", "Basic filters"]', 
 TRUE),
('Premium', 'Enhanced features for serious seekers', 19.99, 1, 
 '["All Basic features", "See who likes you", "Advanced filters", "Send unlimited messages", "Hide ads", "Profile boost"]', 
 TRUE),
('Premium Plus', 'Premium features for 3 months at a discounted rate', 49.99, 3, 
 '["All Premium features", "Priority in search results", "Read receipts for messages", "Premium badge", "Advanced matching algorithm"]', 
 TRUE),
('Elite', 'All premium features plus exclusive benefits', 99.99, 6, 
 '["All Premium Plus features", "Personal matchmaking assistance", "Background verification", "Video chat", "Exclusive events invitations", "Anonymous browsing"]', 
 TRUE);

-- Add identity verifications for some users
INSERT INTO identity_verifications (user_id, document_type, verification_status, verified_at) VALUES
(1, 'National ID', 'verified', NOW() - INTERVAL 30 DAY),
(2, 'Passport', 'verified', NOW() - INTERVAL 45 DAY),
(3, 'National ID', 'verified', NOW() - INTERVAL 60 DAY),
(7, 'Driver License', 'verified', NOW() - INTERVAL 15 DAY),
(9, 'National ID', 'verified', NOW() - INTERVAL 25 DAY);

-- Note: For user_photos, in a real implementation you would have actual image files stored
-- Here we're providing placeholder URLs
INSERT INTO user_photos (user_id, photo_url, photo_type, is_primary, is_verified, moderation_status) VALUES
(1, '/uploads/users/1/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(1, '/uploads/users/1/gallery1.jpg', 'gallery', FALSE, TRUE, 'approved'),
(2, '/uploads/users/2/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(2, '/uploads/users/2/gallery1.jpg', 'gallery', FALSE, TRUE, 'approved'),
(3, '/uploads/users/3/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(4, '/uploads/users/4/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(5, '/uploads/users/5/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(6, '/uploads/users/6/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(7, '/uploads/users/7/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(8, '/uploads/users/8/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(9, '/uploads/users/9/profile.jpg', 'profile', TRUE, TRUE, 'approved'),
(10, '/uploads/users/10/profile.jpg', 'profile', TRUE, TRUE, 'approved');