import api from './api';

export const getUser = async (userId) => {
  try {
    // First try to get the user profile from /api/profile
    try {
      const response = await api.get('/api/profile');
      if (response && response.data) {
        return response.data;
      }
    } catch (profileError) {
      console.log('Error fetching from /api/profile, trying /api/users endpoint');
    }

    // Fallback to /api/users/:userId
    const response = await api.get(`/api/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
};

export const updateUser = async (userId, userData) => {
  try {
    // Calculate profile completion percentage
    const completionPercentage = calculateProfileCompletionPercentage(userData);

    // Add completion percentage to data being sent
    const dataToUpdate = {
      ...userData,
      profile_completion_percentage: completionPercentage
    };

    console.log('Updating user profile with data:', JSON.stringify(dataToUpdate, null, 2));

    // First try the primary endpoint
    try {
      console.log('Attempting to update profile via PUT /api/users/profile');
      const response = await api.put('/api/users/profile', dataToUpdate);
      console.log('Profile updated successfully via /api/users/profile');
      console.log('Response data:', JSON.stringify(response.data, null, 2));

      // Check if all fields were updated correctly
      const updatedFields = Object.keys(dataToUpdate);
      const responseFields = Object.keys(response.data);

      console.log('Fields sent:', updatedFields.length);
      console.log('Fields received:', responseFields.length);

      // Check for specific fields we're having trouble with
      const criticalFields = ['skin_color', 'religious_sect', 'fasting_level', 'hajj_status', 'partner_description'];
      criticalFields.forEach(field => {
        console.log(`Field ${field}: sent=${dataToUpdate[field]}, received=${response.data[field]}`);
      });

      return {
        success: true,
        profile_completion_percentage: completionPercentage,
        data: response.data
      };
    } catch (primaryError) {
      console.error('Error updating via primary endpoint:', primaryError);
      console.error('Error details:', primaryError.response?.data);

      // Try fallback endpoint
      console.log('Trying fallback endpoint POST /api/profile');
      const fallbackResponse = await api.post('/api/profile', dataToUpdate);

      console.log('Profile updated successfully via fallback endpoint');
      console.log('Fallback response data:', JSON.stringify(fallbackResponse.data, null, 2));

      // Check if all fields were updated correctly
      const updatedFields = Object.keys(dataToUpdate);
      const responseFields = Object.keys(fallbackResponse.data.profile || fallbackResponse.data);

      console.log('Fields sent:', updatedFields.length);
      console.log('Fields received:', responseFields.length);

      // Check for specific fields we're having trouble with
      const criticalFields = ['skin_color', 'religious_sect', 'fasting_level', 'hajj_status', 'partner_description'];
      const responseData = fallbackResponse.data.profile || fallbackResponse.data;
      criticalFields.forEach(field => {
        console.log(`Field ${field}: sent=${dataToUpdate[field]}, received=${responseData[field]}`);
      });

      return {
        success: true,
        profile_completion_percentage: completionPercentage,
        data: fallbackResponse.data.profile || fallbackResponse.data
      };
    }
  } catch (error) {
    console.error('Error updating user data:', error);
    console.error('Error response:', error.response?.data);
    return {
      success: false,
      message: error.response?.data?.message || 'An error occurred while updating profile'
    };
  }
};

export const updateProfileImage = async (userId, imageUri) => {
  try {
    const formData = new FormData();
    formData.append('profileImage', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'profile-image.jpg',
    });

    const response = await api.post(`/api/users/${userId}/profile-image`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return {
      success: true,
      imageUrl: response.data.imageUrl
    };
  } catch (error) {
    console.error('Error uploading profile image:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'An error occurred while uploading image'
    };
  }
};

// Calculate profile completion percentage based on filled fields
export const calculateProfileCompletionPercentage = (userData) => {
  // Essential fields that contribute more to completion percentage
  const essentialFields = [
    'name',
    'birth_date', // Use birth_date instead of age
    'gender',
    'height',
    'weight',
    'nationality',
    'country_of_residence', // Added country_of_residence
    'city', // Added city
    'education_level',
    'job_level',
    'marital_status',
    'bio',
    'religious_level',
    'prayer_level',
  ];

  // Additional fields
  const additionalFields = [
    'skin_color',
    'income_level',
    'has_children',
    'number_of_children',
    'wants_children',
    'allows_wife_to_work',
    'religious_sect',
    'fasting_level',
    'hajj_status',
    'smoking',
    'preferred_residence',
    'tribal_affiliation',
    'marriage_readiness',
    'chat_languages',
    'health_status',
    'partner_description',
    'location', // Moved to additional fields
  ];

  // Calculate how many essential fields are filled
  const essentialFieldsCount = essentialFields.length;
  let filledEssentialFields = 0;

  essentialFields.forEach(field => {
    // Special handling for birth_date which could be a Date object or string
    if (field === 'birth_date') {
      if (userData[field] instanceof Date ||
          (typeof userData[field] === 'string' && userData[field].length > 0)) {
        filledEssentialFields++;
      }
    }
    // Special handling for boolean fields
    else if (typeof userData[field] === 'boolean') {
      filledEssentialFields++;
    }
    // Special handling for numeric fields that might be 0
    else if (field === 'height' || field === 'weight') {
      if (userData[field] !== undefined && userData[field] !== null &&
          (userData[field] !== '' || userData[field] === 0)) {
        filledEssentialFields++;
      }
    }
    // General case for string and other fields
    else if (userData[field] !== undefined && userData[field] !== null && userData[field] !== '') {
      filledEssentialFields++;
    }
  });

  // Calculate how many additional fields are filled
  const additionalFieldsCount = additionalFields.length;
  let filledAdditionalFields = 0;

  additionalFields.forEach(field => {
    // Special handling for boolean fields
    if (typeof userData[field] === 'boolean') {
      filledAdditionalFields++;
    }
    // Special handling for numeric fields that might be 0
    else if (field === 'number_of_children') {
      if (userData[field] !== undefined && userData[field] !== null &&
          (userData[field] !== '' || userData[field] === 0)) {
        filledAdditionalFields++;
      }
    }
    // General case for string and other fields
    else if (userData[field] !== undefined && userData[field] !== null && userData[field] !== '') {
      filledAdditionalFields++;
    }
  });

  // Essential fields contribute 70% of the total, additional fields 30%
  const essentialPercentage = (filledEssentialFields / essentialFieldsCount) * 70;
  const additionalPercentage = (filledAdditionalFields / additionalFieldsCount) * 30;

  // Round to the nearest integer
  const totalPercentage = Math.round(essentialPercentage + additionalPercentage);

  // Debug information
  console.log('Profile completion calculation:');
  console.log(`Essential fields: ${filledEssentialFields}/${essentialFieldsCount} (${essentialPercentage.toFixed(2)}%)`);
  console.log(`Additional fields: ${filledAdditionalFields}/${additionalFieldsCount} (${additionalPercentage.toFixed(2)}%)`);
  console.log(`Total percentage: ${totalPercentage}%`);

  // Ensure percentage is at least 1 if any fields are filled
  return Math.max(totalPercentage, (filledEssentialFields + filledAdditionalFields > 0) ? 1 : 0);
};

export const addProfilePhoto = async (userId, photoUri) => {
  try {
    const formData = new FormData();
    formData.append('photo', {
      uri: photoUri,
      type: 'image/jpeg',
      name: 'photo.jpg',
    });

    const response = await api.post(`/api/users/${userId}/photos`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return {
      success: true,
      photoUrl: response.data.url,
      photoId: response.data.id
    };
  } catch (error) {
    console.error('Error uploading photo:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'An error occurred while uploading photo'
    };
  }
};

export const deleteProfilePhoto = async (userId, photoId) => {
  try {
    await api.delete(`/api/users/${userId}/photos/${photoId}`);
    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting photo:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'An error occurred while deleting photo'
    };
  }
};

export const deleteUser = async (userId) => {
  try {
    const response = await api.delete(`/api/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Delete user error:', error);
    throw error;
  }
};

export default {
  getUser,
  updateUser,
  updateProfileImage,
  deleteUser
};