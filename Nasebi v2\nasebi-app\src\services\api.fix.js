import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import config from '../config/config';

// Use the API URL from config, with special handling for Android
let API_URL = config.API_URL;

// For Android physical devices, ensure we're using the correct IP
if (Platform.OS === 'android') {
  // Use direct IP address instead of ********
  // Replace with your actual local IP address
  API_URL = 'http://************:3000';
  console.log('Android detected, using direct IP:', API_URL);
}

console.log(`Using API URL: ${API_URL}`);

// Use the mock data flag from config
const useMockData = config.useMockData;
console.log(`Mock data mode: ${useMockData ? 'ENABLED' : 'DISABLED'}`);

// Get the device's IP address for debugging
const getDeviceIP = async () => {
  try {
    // This is just for debugging purposes
    console.log('Platform:', Platform.OS);
    console.log('API URL configured as:', API_URL);

    // Log additional information about the environment
    if (Platform.OS === 'web') {
      console.log('Web environment detected, using relative URL');
    } else if (Platform.OS === 'android') {
      console.log('Android environment detected, using direct IP address');
    } else if (Platform.OS === 'ios') {
      console.log('iOS environment detected, using localhost');
    }
  } catch (error) {
    console.log('Error getting device info:', error);
  }
};

// Call this function to log device information
getDeviceIP();

// Create custom axios instance with optimized settings
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: config.apiTimeout,
  // Add HTTP keep-alive to prevent connection issues
  httpAgent: new axios.HttpAgent({ keepAlive: true }),
  httpsAgent: new axios.HttpsAgent({ keepAlive: true }),
  // Disable proxy to prevent connection issues
  proxy: false
});

// Create a secondary instance with a different base URL for fallback
const fallbackApi = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: config.apiTimeout * 2, // Double the timeout for fallback attempts
  // Add HTTP keep-alive to prevent connection issues
  httpAgent: new axios.HttpAgent({ keepAlive: true }),
  httpsAgent: new axios.HttpsAgent({ keepAlive: true }),
  // Disable proxy to prevent connection issues
  proxy: false
});

// Add request interceptor to fallbackApi as well
fallbackApi.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('auth-token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    } catch (error) {
      console.error('Error setting auth token in fallbackApi:', error);
      return config;
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a request interceptor to include auth token in all requests
api.interceptors.request.use(
  async (config) => {
    try {
      // Try to get the token from AsyncStorage
      const token = await AsyncStorage.getItem('auth-token');

      // If token exists, add it to the headers
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('Added auth token to request:', config.url);
      } else {
        console.log('No auth token available for request:', config.url);
      }
    } catch (error) {
      console.error('Error adding auth token to request:', error);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// No more mock data - using real API only
console.log('Using real API data only - all mock data removed');

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    console.log(`API Success: ${response.config.method.toUpperCase()} ${response.config.url}`);
    return response;
  },
  async (error) => {
    // Default error message
    let errorMessage = 'An unexpected error occurred';

    // Log detailed error information for debugging
    console.log('API Error Details:');
    console.log(`- Request: ${error.config?.method?.toUpperCase() || 'UNKNOWN'} ${error.config?.url || 'UNKNOWN'}`);
    console.log(`- Error Message: ${error.message}`);

    // Handle connection errors
    if (!error.response) {
      errorMessage = 'Network error - please check your internet connection';
      console.log('Network error occurred');
      console.log(`- API URL: ${API_URL}`);
      console.log(`- Full Error:`, error);

      // Try to retry the request with direct IP if it's a network error
      if ((error.message === 'Network Error' || error.code === 'ECONNABORTED') && error.config) {
        const originalRequest = error.config;

        // Try with direct IP as a last resort
        try {
          console.log('Retrying with direct IP address');
          
          // Use direct IP address for the retry
          const directIpUrl = 'http://************:3000';
          
          const retryConfig = { 
            ...originalRequest,
            baseURL: directIpUrl,
            timeout: 60000 // Increase timeout for retry
          };
          
          console.log(`Retrying request to: ${directIpUrl}${originalRequest.url}`);
          return await axios(retryConfig);
        } catch (retryError) {
          console.log('Retry failed:', retryError.message);
        }
      }
    } else {
      // Server returned an error response
      console.log(`- Status Code: ${error.response.status}`);
      console.log(`- Response Data:`, error.response.data);

      if (error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else {
        // Map HTTP status codes to readable messages
        switch (error.response.status) {
          case 400:
            errorMessage = 'Bad request - please check your input';
            break;
          case 401:
            errorMessage = 'Authentication failed - please log in again';
            break;
          case 403:
            errorMessage = 'You do not have permission to access this resource';
            break;
          case 404:
            errorMessage = 'The requested resource was not found';
            break;
          case 500:
            errorMessage = 'Server error - please try again later';
            break;
        }
      }
    }

    // Attach a readable error message
    error.userMessage = errorMessage;
    return Promise.reject(error);
  }
);

// Create a wrapper for the API that adds error handling and logging
const apiWrapper = {
  get: async (url, config = {}) => {
    try {
      console.log(`API GET request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to GET request: ${url}`);
        }
      }

      const response = await api.get(url, config);
      return response;
    } catch (error) {
      console.error(`API Error in GET ${url}:`, error.message);
      throw error;
    }
  },
  post: async (url, data, config = {}) => {
    try {
      console.log(`API POST request to: ${url}`);
      console.log('Request data:', data);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to POST request: ${url}`);
        }
      }

      const response = await api.post(url, data, config);
      return response;
    } catch (error) {
      console.error(`API Error in POST ${url}:`, error.message);
      throw error;
    }
  },
  // Other methods (put, delete, patch) remain the same
};

// Export the API wrapper instead of the raw API
export default apiWrapper;
