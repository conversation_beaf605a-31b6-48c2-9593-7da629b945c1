# RTL/LTR Issues Guide for Nasebi App

This guide provides instructions for identifying and fixing RTL/LTR layout issues in the Nasebi app.

## Common RTL Issues

1. **Using directional properties instead of logical properties**
   - ❌ `left`, `right`, `marginLeft`, `marginRight`, etc.
   - ✅ `start`, `end`, `marginStart`, `marginEnd`, etc.

2. **Hardcoded text alignment**
   - ❌ `textAlign: 'left'` or `textAlign: 'right'`
   - ✅ `textAlign: isRTL ? 'right' : 'left'` or use `rtl.align`

3. **Absolute positioning without RTL consideration**
   - ❌ `position: 'absolute', left: 10`
   - ✅ `rtl.positionAbsolute(top, end, bottom, start)`

4. **Flex direction without RTL consideration**
   - ❌ `flexDirection: 'row'`
   - ✅ `flexDirection: isRTL ? 'row-reverse' : 'row'` or use `rtl.direction`

5. **Icon direction not mirrored in RTL**
   - ❌ `<Icon name="arrow-forward" />`
   - ✅ `<Icon name="arrow-forward" style={rtl.iconTransform} />`

## Tools for Identifying Issues

### 1. Check RTL Issues Script

Run the script to scan all components and screens for potential RTL issues:

```bash
node scripts/check-rtl-issues.js
```

This will generate a report of files with potential RTL issues.

### 2. Run Layout Checker

Run the layout checker to see RTL issues in the console while running the app:

```bash
node scripts/run-layout-checker.js
```

Then run the app and check the console for RTL issues. Press Ctrl+C when done to restore the original files.

### 3. Fix RTL Issues Script

Fix common RTL issues in a specific file:

```bash
node scripts/fix-rtl-issues.js path/to/file.js
```

## Manual Fixes for Common Issues

### 1. Add useRTL Hook

```jsx
import { useRTL } from '../hooks/useRTL';

const MyComponent = () => {
  const rtl = useRTL();
  // ...
}
```

### 2. Replace Directional Properties

```jsx
// Before
const styles = StyleSheet.create({
  container: {
    marginLeft: 10,
    paddingRight: 15,
  }
});

// After
const styles = StyleSheet.create({
  container: {
    marginStart: 10,
    paddingEnd: 15,
  }
});
```

### 3. Fix Text Alignment

```jsx
// Before
<Text style={{ textAlign: 'left' }}>Hello</Text>

// After
<Text style={rtl.align}>Hello</Text>
```

### 4. Fix Absolute Positioning

```jsx
// Before
<View style={{ 
  position: 'absolute', 
  top: 10, 
  right: 20, 
  bottom: 30, 
  left: 40 
}}>...</View>

// After
<View style={rtl.positionAbsolute(10, 20, 30, 40)}>...</View>
```

### 5. Fix Flex Direction

```jsx
// Before
<View style={{ flexDirection: 'row' }}>...</View>

// After
<View style={rtl.direction}>...</View>
```

### 6. Fix Icon Direction

```jsx
// Before
<Ionicons name="arrow-forward" />

// After
<Ionicons name="arrow-forward" style={rtl.iconTransform} />
```

## Testing RTL/LTR Layout

1. **Test in both languages**
   - Switch between Arabic and English to verify layout works correctly in both directions

2. **Check specific components**
   - Text alignment
   - Button icons
   - Input fields
   - Lists and grid layouts
   - Absolute positioned elements
   - Swipe gestures

3. **Common problem areas**
   - Chat bubbles
   - Card swiping in HomeScreen
   - Form fields in profile screens
   - Navigation elements
   - Modals and popups

## Troubleshooting

If you encounter issues with RTL layout:

1. **Check if the component uses useRTL**
   - Make sure the component imports and uses the useRTL hook

2. **Check for hardcoded directional properties**
   - Replace with logical properties or use rtl utilities

3. **Check for transforms and animations**
   - Make sure transforms and animations account for RTL direction

4. **Check for third-party components**
   - Some third-party components may not support RTL layout
   - You may need to wrap them with custom RTL-aware components

## Resources

- [React Native RTL Support](https://reactnative.dev/blog/2016/08/19/right-to-left-support-for-react-native-apps)
- [I18nManager Documentation](https://reactnative.dev/docs/i18nmanager)
- [Nasebi App RTL Theme Guide](./RTL_THEME_GUIDE.md)
