import { useTranslation as useOriginalTranslation } from 'react-i18next';
import { flattenKey } from '../utils/translationHelper';

/**
 * Custom hook that wraps the useTranslation hook to handle flat translation keys
 * 
 * @returns {Object} - The translation object with a modified t function
 */
export const useTranslationFlat = () => {
  const { t: originalT, i18n, ...rest } = useOriginalTranslation();
  
  // Create a new t function that handles flat keys
  const t = (key, options = {}) => {
    if (!key) return '';
    
    // Convert dotted keys to flat keys
    const flatKey = flattenKey(key);
    
    // Use the flat key for translation
    return originalT(flatKey, options);
  };
  
  return { t, i18n, ...rest };
};

export default useTranslationFlat;
