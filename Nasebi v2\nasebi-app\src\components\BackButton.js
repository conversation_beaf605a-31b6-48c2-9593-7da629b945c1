import React from 'react';
import { TouchableOpacity, StyleSheet, View, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../context';
import { useRTL } from '../hooks';

/**
 * Custom back button component for navigation headers
 *
 * @param {Object} props - Component props
 * @param {Function} props.onPress - Optional custom onPress handler
 * @param {string} props.color - Optional custom color
 * @param {number} props.size - Optional custom size
 * @returns {React.ReactElement} - Rendered component
 */
const BackButton = ({ onPress, color, size = 24 }) => {
  const navigation = useNavigation();
  const { colors } = useTheme();
  const { isRTL } = useRTL();

  // Use custom onPress or default to navigation.goBack
  const handlePress = onPress || (() => navigation.goBack());

  // Use custom color or default to theme primary color
  // (which is #ef9ac5 in dark mode and #93060d in light mode)
  const iconColor = color || colors.primary;

  // Use RTL-aware icon
  const iconName = isRTL ? 'chevron-forward' : 'chevron-back';

  return (
    <TouchableOpacity
      style={styles.button}
      onPress={handlePress}
      activeOpacity={0.6}
    >
      <View style={styles.iconContainer}>
        <Ionicons name={iconName} size={size} color={iconColor} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: 8,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  iconContainer: {
    padding: 4,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default BackButton;
