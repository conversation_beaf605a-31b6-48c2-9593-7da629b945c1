// Dropdown options for various profile fields

// Education levels
export const EDUCATION_LEVELS = [
  { value: 'less_than_highschool', label: 'education_less_than_highschool' },
  { value: 'highschool', label: 'education_highschool' },
  { value: 'college_degree', label: 'education_college_degree' },
  { value: 'bachelors', label: 'education_bachelors' },
  { value: 'masters', label: 'education_masters' },
  { value: 'doctorate', label: 'education_doctorate' },
  { value: 'religious_education', label: 'education_religious_education' }
];

// Religious levels
export const RELIGIOUS_LEVELS = [
  { value: 'very_religious', label: 'religious_very_religious' },
  { value: 'religious', label: 'religious_religious' },
  { value: 'somewhat_religious', label: 'religious_somewhat_religious' },
  { value: 'not_religious', label: 'religious_not_religious' }
];

// Prayer levels
export const PRAYER_LEVELS = [
  { value: 'daily', label: 'prayer_daily' },
  { value: 'weekly', label: 'prayer_weekly' },
  { value: 'sometimes', label: 'prayer_sometimes' },
  { value: 'religious_occasions', label: 'prayer_religious_occasions' },
  { value: 'rarely', label: 'prayer_rarely' },
  { value: 'never', label: 'prayer_never' }
];

// Fasting levels
export const FASTING_LEVELS = [
  { value: 'always', label: 'fasting_always' },
  { value: 'sometimes', label: 'fasting_sometimes' },
  { value: 'never', label: 'fasting_never' },
  { value: 'prefer_not_to_say', label: 'fasting_prefer_not_to_say' }
];

// Hajj status
export const HAJJ_STATUS = [
  { value: 'completed', label: 'hajj_completed' },
  { value: 'planning_soon', label: 'hajj_planning_soon' },
  { value: 'planning_future', label: 'hajj_planning_future' },
  { value: 'not_planned', label: 'hajj_not_planned' }
];

// Marital status
export const MARITAL_STATUS = [
  { value: 'single', label: 'maritalStatus_single' },
  { value: 'divorced', label: 'maritalStatus_divorced' },
  { value: 'widowed', label: 'maritalStatus_widowed' },
  { value: 'married', label: 'maritalStatus_married' }
];

// Marriage readiness
export const MARRIAGE_READINESS = [
  { value: 'immediately', label: 'marriageReadiness_immediately' },
  { value: 'within_year', label: 'marriageReadiness_within_year' },
  { value: 'after_two_years', label: 'marriageReadiness_after_two_years' },
  { value: 'not_decided', label: 'marriageReadiness_not_decided' }
];

// Preferred residence
export const PREFERRED_RESIDENCE = [
  { value: 'own_home', label: 'residence_own_home' },
  { value: 'family_home', label: 'residence_family_home' },
  { value: 'family_home_temporarily', label: 'residence_family_home_temporarily' },
  { value: 'undecided', label: 'residence_undecided' }
];

// Children preferences
export const CHILDREN_PREFERENCES = [
  { value: 'soon', label: 'children_soon' },
  { value: 'after_two_years', label: 'children_after_two_years' },
  { value: 'depends', label: 'children_depends' },
  { value: 'no', label: 'children_no' }
];

// Work preferences
export const WORK_PREFERENCES = [
  { value: 'yes', label: 'work_yes' },
  { value: 'yes_from_home', label: 'work_yes_from_home' },
  { value: 'depends', label: 'work_depends' },
  { value: 'no', label: 'work_no' }
];

// Health status
export const HEALTH_STATUS = [
  { value: 'good_health', label: 'health_good_health' },
  { value: 'special_needs', label: 'health_special_needs' },
  { value: 'chronic_disease', label: 'health_chronic_disease' },
  { value: 'infertile', label: 'health_infertile' }
];

// Smoking status
export const SMOKING_STATUS = [
  { value: 'yes', label: 'smoking_yes' },
  { value: 'sometimes', label: 'smoking_sometimes' },
  { value: 'no', label: 'smoking_no' }
];

// Job levels
export const JOB_LEVELS = [
  { value: 'student', label: 'job_student' },
  { value: 'employee', label: 'job_employee' },
  { value: 'senior_employee', label: 'job_senior_employee' },
  { value: 'manager', label: 'job_manager' },
  { value: 'unemployed', label: 'job_unemployed' },
  { value: 'prefer_not_to_say', label: 'job_prefer_not_to_say' }
];

// Income levels
export const INCOME_LEVELS = [
  { value: 'no_income', label: 'income_no_income' },
  { value: 'low', label: 'income_low' },
  { value: 'average', label: 'income_average' },
  { value: 'high', label: 'income_high' }
];

// Religious sects
export const RELIGIOUS_SECTS = [
  { value: 'sunni', label: 'religiousSect_sunni' },
  { value: 'shia', label: 'religiousSect_shia' },
  { value: 'other', label: 'religiousSect_other' }
];

// Gender options
export const GENDER_OPTIONS = [
  { value: 'male', label: 'gender_male' },
  { value: 'female', label: 'gender_female' }
];

// Boolean options (Yes/No)
export const BOOLEAN_OPTIONS = [
  { value: 'yes', label: 'common_yes' },
  { value: 'no', label: 'common_no' }
];

// Languages
export const LANGUAGES = [
  { value: 'arabic', label: 'language_arabic' },
  { value: 'english', label: 'language_english' },
  { value: 'french', label: 'language_french' },
  { value: 'spanish', label: 'language_spanish' },
  { value: 'turkish', label: 'language_turkish' },
  { value: 'urdu', label: 'language_urdu' },
  { value: 'hindi', label: 'language_hindi' },
  { value: 'persian', label: 'language_persian' }
];
