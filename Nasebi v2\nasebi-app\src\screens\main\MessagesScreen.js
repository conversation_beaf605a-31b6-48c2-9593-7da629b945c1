import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ActivityIndicator,
  RefreshControl,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import api from '../../services/api';
import { useTheme } from '../../context/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../context/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { useRTL } from '../../hooks/useRTL';


const { width } = Dimensions.get('window');

const MessagesScreen = ({ navigation }) => {
  const rtl = useRTL();

  const { t } = useTranslation();
  const { colors, isDark } = useTheme();
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredConversations, setFilteredConversations] = useState([]);

  // Initial data loading
  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredConversations(conversations);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = conversations.filter(
        conversation => conversation.name.toLowerCase().includes(query)
      );
      setFilteredConversations(filtered);
    }
  }, [searchQuery, conversations]);

  const fetchConversations = async () => {
    setLoading(true);
    try {
      const response = await api.get('/api/conversations');
      console.log('Conversations response:', response.data);

      // Extract conversations data based on different possible response structures
      let conversationsData = [];
      if (Array.isArray(response.data)) {
        conversationsData = response.data;
      } else if (response.data && Array.isArray(response.data.conversations)) {
        conversationsData = response.data.conversations;
      } else if (response.data && Array.isArray(response.data.data)) {
        conversationsData = response.data.data;
      } else {
        console.warn('Response data does not contain conversations array:', response.data);
        conversationsData = [];
      }

      // Process conversations to ensure all required properties exist
      const processedConversations = conversationsData.map(conv => {
        // Ensure each conversation has an id, name, and image
        const conversation = {
          ...conv,
          id: conv.id || conv.conversation_id || conv.match_id || Math.random().toString(36).substring(2, 9),
          matchId: conv.matchId || conv.match_id || conv.id || 0,
          userId: conv.userId || conv.user_id || conv.other_user_id || 0,
          name: conv.name || conv.user_name || 'Unknown',
          image: conv.photo || conv.image || conv.profile_photo || conv.avatar || 'https://randomuser.me/api/portraits/lego/1.jpg',
          online: !!conv.online,
        };

        // Ensure lastMessage has all required properties
        if (!conv.lastMessage && !conv.last_message) {
          conversation.lastMessage = {
            text: '',
            timestamp: new Date().toISOString(),
            isRead: true,
            isSender: false
          };
        } else {
          const lastMsg = conv.lastMessage || conv.last_message || {};
          conversation.lastMessage = {
            ...lastMsg,
            text: lastMsg.text || lastMsg.content || lastMsg.message || '',
            timestamp: lastMsg.timestamp || lastMsg.created_at || lastMsg.date || new Date().toISOString(),
            isRead: lastMsg.isRead !== undefined ? lastMsg.isRead :
                   lastMsg.read !== undefined ? lastMsg.read : true,
            isSender: lastMsg.isSender !== undefined ? lastMsg.isSender :
                      lastMsg.is_sender !== undefined ? lastMsg.is_sender :
                      lastMsg.sender_id === user?.id
          };
        }

        return conversation;
      });

      console.log(`Processed ${processedConversations.length} conversations`);
      setConversations(processedConversations);
      setFilteredConversations(processedConversations);
    } catch (error) {
      console.log('Error fetching conversations:', error);
      Alert.alert(
        t('common.error'),
        t('messages.errorFetchingConversations'),
        [{ text: t('common.ok') }]
      );
      setConversations([]);
      setFilteredConversations([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchConversations();
  };

  const handleOpenChat = (item) => {
    navigation.navigate('Chat', {
      matchId: item.matchId,
      name: item.name,
      userId: item.userId,
      photo: item.image,
    });
  };

  const getTimeAgo = (dateString) => {
    try {
      const date = new Date(dateString);
      const now = new Date();

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date:', dateString);
        return '';
      }

      const diffInMs = now - date;
      const diffInSeconds = diffInMs / 1000;
      const diffInMinutes = diffInSeconds / 60;
      const diffInHours = diffInMinutes / 60;
      const diffInDays = diffInHours / 24;

      // Format the date for "today at time" or date display
      const formatTime = (date) => {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      };

      // Check if same day
      const isSameDay = (date1, date2) => {
        return date1.getDate() === date2.getDate() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getFullYear() === date2.getFullYear();
      };

      // Check if yesterday
      const isYesterday = (date) => {
        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        return isSameDay(date, yesterday);
      };

      // Just now - less than 1 minute ago
      if (diffInMinutes < 1) {
        return t('Now');
      }
      // Minutes ago - less than 1 hour ago
      else if (diffInMinutes < 60) {
        const minutes = Math.floor(diffInMinutes);
        return `${minutes} ${minutes === 1 ? t('min Ago') : t('mins Ago')}`;
      }
      // Hours ago - less than 24 hours ago but same day
      else if (diffInHours < 24 && isSameDay(date, now)) {
        const hours = Math.floor(diffInHours);
        return `${hours} ${hours === 1 ? t('hours Ago') : t('hours Ago')}`;
      }
      // Yesterday
      else if (isYesterday(date)) {
        return `${t('yesterday')} ${formatTime(date)}`;
      }
      // Days ago - less than 7 days ago
      else if (diffInDays < 7) {
        const days = Math.floor(diffInDays);
        return `${days} ${days === 1 ? t('day Ago') : t('days Ago')}`;
      }
      // This year - show month and day
      else if (date.getFullYear() === now.getFullYear()) {
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
      }
      // Different year - show month, day and year
      else {
        return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
      }
    } catch (error) {
      console.error('Error calculating time ago:', error);
      return '';
    }
  };

  const renderItem = ({ item }) => {
    // Add null checks for lastMessage and its properties
    if (!item) {
      return null;
    }

    // Ensure all required properties exist
    const safeItem = {
      ...item,
      image: item.image || 'https://randomuser.me/api/portraits/lego/1.jpg',
      name: item.name || 'Unknown',
      matchId: item.matchId || 0,
      userId: item.userId || 0,
      online: !!item.online,
      lastMessage: item.lastMessage || {
        text: '',
        timestamp: new Date().toISOString(),
        isRead: true,
        isSender: false
      }
    };

    // Ensure lastMessage has all required properties
    safeItem.lastMessage = {
      ...safeItem.lastMessage,
      text: safeItem.lastMessage.text || '',
      timestamp: safeItem.lastMessage.timestamp || new Date().toISOString(),
      isRead: safeItem.lastMessage.isRead !== undefined ? safeItem.lastMessage.isRead : true,
      isSender: safeItem.lastMessage.isSender !== undefined ? safeItem.lastMessage.isSender : false
    };

    const lastMessageTime = safeItem.lastMessage.timestamp ? getTimeAgo(safeItem.lastMessage.timestamp) : '';
    const isUnread = safeItem.lastMessage.isRead !== undefined &&
                     safeItem.lastMessage.isSender !== undefined &&
                     !safeItem.lastMessage.isRead &&
                     !safeItem.lastMessage.isSender;

    return (
      <TouchableOpacity
        style={[styles.conversationItem, { backgroundColor: colors.card }]}
        onPress={() => handleOpenChat(safeItem)}
      >
        <View style={styles.avatarContainer}>
          <Image source={{ uri: safeItem.image }} style={styles.avatar} />
          {safeItem.online && <View style={styles.onlineIndicator} />}
        </View>
        <View style={styles.conversationContent}>
          <View style={styles.conversationHeader}>
            <Text style={[styles.name, { color: colors.text }]}>
              {safeItem.name}
            </Text>
            <Text style={[styles.time, { color: colors.subtext }]}>
              {lastMessageTime}
            </Text>
          </View>
          <View style={styles.messageContainer}>
            <Text
              style={[
                styles.message,
                { color: isUnread ? colors.text : colors.subtext },
                isUnread && styles.unreadMessage,
              ]}
              numberOfLines={1}
            >
              {safeItem.lastMessage.isSender ? `${t('messages.you')}: ` : ''}
              {safeItem.lastMessage.text || ''}
            </Text>
            {isUnread && (
              <View style={[styles.unreadBadge, { backgroundColor: colors.primary }]} />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const EmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="chatbubble-ellipses-outline" size={64} color={colors.subtext} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {t('messages.noMessagesTitle')}
      </Text>
      <Text style={[styles.emptyDescription, { color: colors.subtext }]}>
        {t('messages.noMessagesDesc')}
      </Text>
      <TouchableOpacity
        style={[styles.emptyButton, { backgroundColor: colors.primary }]}
        onPress={() => navigation.navigate('Matches')}
      >
        <Text style={styles.emptyButtonText}>
          {t('messages.viewMatches')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView
      style={[
        styles.container,
        {
          backgroundColor: colors.background,
          paddingBottom: 10 // Add padding to the bottom of the SafeAreaView
        }
      ]}
      edges={['top', 'left', 'right']} // Don't include bottom edge to avoid navigation bar overlap
    >
      {/* <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {t('messages.title')}
        </Text>
      </View> */}

      <View style={[styles.searchContainer, { backgroundColor: colors.card }]}>
        <Ionicons name="search" size={20} color={colors.subtext} />
        <TextInput
          style={[styles.searchInput, { color: colors.text }]}
          placeholder={t('search')}
          placeholderTextColor={colors.subtext}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={colors.subtext} />
          </TouchableOpacity>
        )}
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <View style={styles.flatListContainer}>
          <FlatList
            data={filteredConversations}
            renderItem={renderItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={
              filteredConversations.length === 0
                ? { flex: 1 }
                : [styles.list, { paddingBottom: 100 }] // Extra padding to ensure content isn't hidden by navigation bar
            }
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }
            ListEmptyComponent={<EmptyState />}
            showsVerticalScrollIndicator={true}
            initialNumToRender={10}
            windowSize={10}
            removeClippedSubviews={false}
          />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
  },
  searchContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 15,
    paddingHorizontal: 15,
    height: 50,
    borderRadius: 25,
  },
  searchInput: {
    flex: 1,
    height: 50,
    marginStart: 10,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  flatListContainer: {
    flex: 1, // Take up all available space
    width: '100%',
    marginBottom: 10, // Add margin at the bottom to prevent overlap with navigation bar
  },
  list: {
    paddingHorizontal: 20,
    paddingBottom: 80, // Increased bottom padding to account for the navigation bar height
  },
  conversationItem: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    padding: 15,
    borderRadius: 15,
    marginBottom: 15,
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    end: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: 'white',
  },
  conversationContent: {
    flex: 1,
    marginStart: 15,
    justifyContent: 'center',
  },
  conversationHeader: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  name: {
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
    flex: 1,
    marginEnd: 10,
  },
  time: {
    fontSize: 14,
    fontFamily: 'Roboto',
  },
  messageContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  message: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Roboto',
  },
  unreadMessage: {
    fontFamily: 'Roboto-Medium',
  },
  unreadBadge: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginStart: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyDescription: {
    fontSize: 16,
    fontFamily: 'Roboto',
    textAlign: 'center',
    marginBottom: 30,
  },
  emptyButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
  },
});

export default MessagesScreen;