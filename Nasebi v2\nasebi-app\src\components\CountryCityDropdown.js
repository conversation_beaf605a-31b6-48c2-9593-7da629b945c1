import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTranslationFlat } from '../hooks/useTranslationFlat';
import CustomDropdown from './CustomDropdown';
import { countries } from '../data/countries';
import { cities } from '../data/cities';
import { useRTL } from '../hooks/useRTL';


const CountryCityDropdown = ({
  selectedCountry,
  selectedCity,
  onCountryChange,
  onCityChange,
}) => {
  const { t } = useTranslationFlat();

  // Get available cities based on selected country
  const getAvailableCities = () => {
    if (!selectedCountry) return [];
    return cities[selectedCountry] || cities.default;
  };

  return (
    <View style={styles.container}>
      <CustomDropdown
        label={t('countryOfResidence')}
        options={countries}
        selectedValue={selectedCountry}
        onValueChange={(value) => {
          onCountryChange(value);
          // Reset city when country changes
          onCityChange('');
        }}
        placeholder={t('selectCountry')}
      />

      <CustomDropdown
        label={t('city')}
        options={getAvailableCities()}
        selectedValue={selectedCity}
        onValueChange={onCityChange}
        placeholder={selectedCountry ? t('selectCity') : t('selectCountryFirst')}
        disabled={!selectedCountry}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
});

export default CountryCityDropdown;
