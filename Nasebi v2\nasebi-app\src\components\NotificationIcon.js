import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  Image,
  Animated,
  TouchableWithoutFeedback
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useLanguage } from '../context/LanguageContext';
import { useTranslation } from 'react-i18next';

const NotificationIcon = ({ count = 0, notifications = [], onPressNotification }) => {
  const { colors } = useTheme();
  const { isRTL } = useLanguage();
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Bounce animation when new notifications arrive
  React.useEffect(() => {
    if (count > 0) {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.3,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [count]);

  const renderNotificationItem = ({ item }) => {
    // Define which icon to use based on the notification type
    let iconName;
    let iconColor;

    switch (item.type) {
      case 'like':
        iconName = 'heart';
        iconColor = '#FF6B6B';
        break;
      case 'message':
        iconName = 'chatbubble';
        iconColor = '#4CAF50';
        break;
      case 'visit':
        iconName = 'eye';
        iconColor = '#2196F3';
        break;
      default:
        iconName = 'notifications';
        iconColor = '#FFC107';
    }

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          { backgroundColor: item.read ? colors.card : colors.primary + '15' },
          { borderStartColor: item.read ? colors.border : colors.primary }
        ]}
        onPress={() => {
          setModalVisible(false);
          onPressNotification(item);
        }}
      >
        <View style={styles.iconContainer}>
          <Ionicons name={iconName} size={24} color={iconColor} />
        </View>
        <View style={styles.notificationContent}>
          <Text style={[styles.notificationText, { color: colors.text }]}>
            {item.message}
          </Text>
          <Text style={[styles.notificationTime, { color: colors.subtext }]}>
            {item.time}
          </Text>
        </View>
        {!item.read && (
          <View style={[styles.unreadDot, { backgroundColor: colors.primary }]} />
        )}
      </TouchableOpacity>
    );
  };

  const emptyNotificationsView = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="notifications-off-outline" size={50} color={colors.subtext} />
      <Text style={[styles.emptyText, { color: colors.subtext }]}>
        {t('notifications.noNotifications')}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={() => setModalVisible(true)}>
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <Ionicons
            name="notifications-outline"
            size={30}
            color={colors.text}
            style={{ transform: [{ scaleX: isRTL ? -1 : 1 }] }}
          />
          {count > 0 && (
            <View style={[styles.badge, { backgroundColor: colors.primary }]}>
              <Text style={styles.badgeText}>
                {count > 99 ? '99+' : count}
              </Text>
            </View>
          )}
        </Animated.View>
      </TouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback onPress={() => {}}>
              <View style={[
                styles.notificationsContainer,
                { backgroundColor: colors.background,
                  borderColor: colors.border,
                  [isRTL ? 'left' : 'right']: 20
                }
              ]}>
                <View style={[styles.notificationsHeader, { borderBottomColor: colors.border }]}>
                  <Text style={[styles.notificationsTitle, { color: colors.text }]}>
                    {t('notifications.title')}
                  </Text>
                  <TouchableOpacity onPress={() => setModalVisible(false)}>
                    <Ionicons name="close" size={22} color={colors.text} />
                  </TouchableOpacity>
                </View>

                <FlatList
                  data={notifications}
                  renderItem={renderNotificationItem}
                  keyExtractor={(item) => item.id.toString()}
                  style={styles.notificationsList}
                  ListEmptyComponent={emptyNotificationsView}
                  maxHeight={400}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 10,
    marginHorizontal: 5,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badge: {
    position: 'absolute',
    end: -5,
    top: -5,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 3,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
  },
  notificationsContainer: {
    position: 'absolute',
    top: 55,
    width: 320,
    maxHeight: 450,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  notificationsHeader: {
    flexDirection: 'row', // Will be handled by I18nManager
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  notificationsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  notificationsList: {
    padding: 8,
  },
  notificationItem: {
    flexDirection: 'row', // Will be handled by I18nManager
    alignItems: 'center',
    padding: 12,
    marginVertical: 4,
    borderRadius: 8,
    borderStartWidth: 3,
  },
  iconContainer: {
    marginEnd: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationText: {
    fontSize: 14,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginStart: 8,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 10,
    fontSize: 16,
  },
});

export default NotificationIcon;