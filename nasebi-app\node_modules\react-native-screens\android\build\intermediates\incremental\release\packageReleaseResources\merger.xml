<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\build\generated\res\rs\release"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res"><file name="rns_default_enter_in" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_default_enter_in.xml" qualifiers="" type="anim"/><file name="rns_default_enter_out" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_default_enter_out.xml" qualifiers="" type="anim"/><file name="rns_default_exit_in" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_default_exit_in.xml" qualifiers="" type="anim"/><file name="rns_default_exit_out" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_default_exit_out.xml" qualifiers="" type="anim"/><file name="rns_fade_from_bottom" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_fade_from_bottom.xml" qualifiers="" type="anim"/><file name="rns_fade_in" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_fade_in.xml" qualifiers="" type="anim"/><file name="rns_fade_out" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_fade_out.xml" qualifiers="" type="anim"/><file name="rns_fade_to_bottom" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_fade_to_bottom.xml" qualifiers="" type="anim"/><file name="rns_no_animation_20" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_no_animation_20.xml" qualifiers="" type="anim"/><file name="rns_no_animation_250" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_no_animation_250.xml" qualifiers="" type="anim"/><file name="rns_no_animation_350" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_no_animation_350.xml" qualifiers="" type="anim"/><file name="rns_no_animation_medium" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_no_animation_medium.xml" qualifiers="" type="anim"/><file name="rns_slide_in_from_bottom" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_slide_in_from_bottom.xml" qualifiers="" type="anim"/><file name="rns_slide_in_from_left" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_slide_in_from_left.xml" qualifiers="" type="anim"/><file name="rns_slide_in_from_right" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_slide_in_from_right.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_bottom" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_slide_out_to_bottom.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_left" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_slide_out_to_left.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_right" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\main\res\anim\rns_slide_out_to_right.xml" qualifiers="" type="anim"/></source><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\build\generated\res\rs\release"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\react-native-screens\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>