<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\src\main\res"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\build\generated\res\rs\release"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\src\main\res"><file name="ic_dropdown" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\src\main\res\drawable\ic_dropdown.xml" qualifiers="" type="drawable"/><file name="spinner_dropdown_background" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\src\main\res\drawable\spinner_dropdown_background.xml" qualifiers="" type="drawable"/><file name="simple_spinner_dropdown_item" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\src\main\res\layout\simple_spinner_dropdown_item.xml" qualifiers="" type="layout"/><file name="simple_spinner_item" path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\src\main\res\layout\simple_spinner_item.xml" qualifiers="" type="layout"/></source><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\build\generated\res\rs\release"/><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Nasebi v2\nasebi-app\node_modules\@react-native-picker\picker\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>