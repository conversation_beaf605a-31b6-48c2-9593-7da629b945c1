import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  Platform,
  Image
} from 'react-native';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRTL } from '../../hooks/useRTL';


const SettingsScreen = ({ navigation }) => {
  const rtl = useRTL();

  const { t } = useTranslationFlat();
  const { colors, theme, toggleTheme, isDark } = useTheme();
  const { logout } = useAuth();
  const { language, changeLanguage, isRTL } = useLanguage();

  const handleLogout = () => {
    Alert.alert(
      t('logoutTitle'),
      t('logoutConfirm'),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('yes'),
          onPress: () => logout(),
        },
      ]
    );
  };

  const toggleLanguage = () => {
    const newLang = language === 'en' ? 'ar' : 'en';
    changeLanguage(newLang);
  };

  const MenuItem = ({ icon, title, onPress, toggle, value, chevron = true }) => (
    <TouchableOpacity
      style={[styles.menuItem, { borderBottomColor: colors.border }]}
      onPress={onPress}
    >
      <View style={styles.menuItemLeft}>
        <Ionicons name={icon} size={22} color={colors.primary} style={styles.menuIcon} />
        <Text style={[styles.menuText, { color: colors.text }]}>{title}</Text>
      </View>

      <View style={styles.menuItemRight}>
        {toggle ? (
          <Switch
            value={value}
            onValueChange={onPress}
            trackColor={{ false: '#767577', true: colors.primary }}
            thumbColor={Platform.OS === 'ios' ? '#FFFFFF' : value ? '#FFFFFF' : '#f4f3f4'}
            ios_backgroundColor="#3e3e3e"
          />
        ) : chevron ? (
          <Ionicons
            name={isRTL ? "chevron-back" : "chevron-forward"}
            size={22}
            color={colors.subtext}
          />
        ) : null}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView>
        <View style={styles.section}>
          <Text style={[styles.title, { color: colors.subtext }]}>
            {t('account')}
          </Text>

          <View style={[styles.menuContainer, { backgroundColor: colors.card }]}>
            <MenuItem
              icon="person-outline"
              title={t('editProfile')}
              onPress={() => navigation.navigate('EditProfile')}
            />

            <MenuItem
              icon="notifications-outline"
              title={t('notifications')}
              onPress={() => navigation.navigate('Notifications')}
            />

            <MenuItem
              icon="lock-closed-outline"
              title={t('privacy')}
              onPress={() => navigation.navigate('Privacy')}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.title, { color: colors.subtext }]}>
            {t('preferences')}
          </Text>

          <View style={[styles.menuContainer, { backgroundColor: colors.card }]}>
            <MenuItem
              icon="moon-outline"
              title={t('darkMode')}
              toggle
              value={isDark}
              onPress={toggleTheme}
              chevron={false}
            />

            <MenuItem
              icon="language-outline"
              title={t('language')}
              toggle
              value={language === 'ar'}
              onPress={toggleLanguage}
              chevron={false}
            />

            <MenuItem
              icon="options-outline"
              title={t('matchPreferences')}
              onPress={() => navigation.navigate('Preferences')}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.title, { color: colors.subtext }]}>
            {t('subscription')}
          </Text>

          <View style={[styles.menuContainer, { backgroundColor: colors.card }]}>
            <MenuItem
              icon="star-outline"
              title={t('premiumMembership')}
              onPress={() => navigation.navigate('Subscription')}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.title, { color: colors.subtext }]}>
            {t('info')}
          </Text>

          <View style={[styles.menuContainer, { backgroundColor: colors.card }]}>
            <MenuItem
              icon="information-circle-outline"
              title={t('about')}
              onPress={() => navigation.navigate('About')}
            />

            <MenuItem
              icon="help-circle-outline"
              title={t('help')}
              onPress={() => {
                // Open help resources
              }}
            />
          </View>
        </View>

        <TouchableOpacity
          style={[styles.logoutButton, { backgroundColor: colors.card }]}
          onPress={handleLogout}
        >
          <Text style={[styles.logoutText, { color: '#FF3B30' }]}>
            {t('logout')}
          </Text>
        </TouchableOpacity>

        <View style={styles.versionContainer}>
          <Image
            source={require('../../../assets/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={[styles.versionText, { color: colors.subtext }]}>
            {t('appName')} v1.0.0
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    marginBottom: 20,
  },
  title: {
    fontSize: 14,
    fontFamily: 'Roboto',
    marginHorizontal: 20,
    marginBottom: 8,
    marginTop: 20,
  },
  menuContainer: {
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
  },
  menuItemLeft: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  menuItemRight: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  menuIcon: {
    marginEnd: 14,
  },
  menuText: {
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  logoutButton: {
    marginHorizontal: 15,
    borderRadius: 10,
    paddingVertical: 16,
    marginTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoutText: {
    fontSize: 16,
    fontFamily: 'Roboto-Medium',
  },
  versionContainer: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 40,
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: 10,
  },
  versionText: {
    fontSize: 14,
    fontFamily: 'Roboto',
  },
});

export default SettingsScreen;