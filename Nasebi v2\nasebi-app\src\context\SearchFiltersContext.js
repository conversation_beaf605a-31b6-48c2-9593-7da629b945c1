import React, { createContext, useContext, useState } from 'react';

// Create the context
const SearchFiltersContext = createContext();

// Default filter values
const defaultFilters = {
  ageRange: [18, 50],
  hasProfilePhoto: true,

  // Basic information
  height: [150, 200],
  weight: [50, 100],
  nationality: '',
  skinColor: '',

  // Education and career
  educationLevel: '',
  jobLevel: '',
  incomeLevel: '',

  // Religious attributes
  religiousLevel: '',
  religiousSect: '',
  prayerLevel: '',
  fastingLevel: '',
  hajjStatus: '',

  // Lifestyle
  maritalStatus: '',
  hasChildren: null,
  wantsChildren: '',
  smoking: null,

  // Marriage preferences
  marriageReadiness: '',
  preferredResidence: '',
  allowsWifeToWork: '',
  tribalAffiliation: null,
  healthStatus: ''
};

// Provider component
export const SearchFiltersProvider = ({ children }) => {
  const [filters, setFilters] = useState(defaultFilters);

  // Function to update filters
  const updateFilters = (newFilters) => {
    setFilters({ ...filters, ...newFilters });
  };

  // Function to reset filters to default
  const resetFilters = () => {
    setFilters(defaultFilters);
  };

  // The context value that will be provided
  const value = {
    filters,
    updateFilters,
    resetFilters
  };

  return (
    <SearchFiltersContext.Provider value={value}>
      {children}
    </SearchFiltersContext.Provider>
  );
};

// Custom hook to use the search filters context
export const useSearchFilters = () => {
  const context = useContext(SearchFiltersContext);
  if (context === undefined) {
    throw new Error('useSearchFilters must be used within a SearchFiltersProvider');
  }
  return context;
};