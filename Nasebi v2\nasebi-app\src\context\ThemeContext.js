import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define light and dark themes
const lightTheme = {
  background: '#FFFFFF',
  text: '#000000',
  primary: '#93060d',   // Deep red
  secondary: '#6C757D',
  accent: '#93060d',    // Deep red
  border: '#E1E1E1',
  error: '#DC3545',
  success: '#93060d',   // Deep red
  warning: '#FFC107',
  info: '#93060d',      // Deep red
  card: '#F8F9FA',
  subtext: '#6C757D',
  subtleBg: '#F5F5F5',  // Subtle background for cards, inputs, etc.
  shadow: 'rgba(0, 0, 0, 0.1)', // Shadow color
  overlay: 'rgba(0, 0, 0, 0.5)', // Overlay color for modals
};

const darkTheme = {
  background: '#121212',
  text: '#FFFFFF',
  primary: '#ef9ac5',  // Pink
  secondary: '#6C757D',
  accent: '#ef9ac5',   // Pink
  border: '#333333',
  error: '#DC3545',
  success: '#ef9ac5',  // Pink
  warning: '#FFC107',
  info: '#ef9ac5',     // Pink
  card: '#1E1E1E',     // Card background color
  subtext: '#AAAAAA',  // Subtext color
  subtleBg: '#222222', // Subtle background for cards, inputs, etc.
  shadow: 'rgba(0, 0, 0, 0.3)', // Shadow color (darker for dark mode)
  overlay: 'rgba(0, 0, 0, 0.7)', // Overlay color for modals (darker for dark mode)
};

// Create the context
const ThemeContext = createContext();

// Theme provider component
// Remove the rtl parameter from ThemeProvider
export const ThemeProvider = ({ children }) => {
  const deviceTheme = useColorScheme();
  const [isDark, setIsDark] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load theme preference from storage
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('theme-preference');
        if (savedTheme !== null) {
          setIsDark(savedTheme === 'dark');
        } else {
          // Use device theme as default if no saved preference
          setIsDark(deviceTheme === 'dark');
        }
      } catch (error) {
        console.error('Error loading theme preference:', error);
        // Fallback to device theme
        setIsDark(deviceTheme === 'dark');
      } finally {
        setIsLoading(false);
      }
    };

    loadThemePreference();
  }, [deviceTheme]);

  // Toggle theme function
  const toggleTheme = async () => {
    try {
      const newTheme = !isDark;
      setIsDark(newTheme);
      await AsyncStorage.setItem('theme-preference', newTheme ? 'dark' : 'light');
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  // Get current theme
  const theme = isDark ? darkTheme : lightTheme;

  // Remove RTL-specific properties
  const themeWithoutRTL = {
    ...theme,
    // Remove these lines
    // rtl,
    // direction: rtl ? 'rtl' : 'ltr',
    // textAlign: rtl ? 'right' : 'left',
    // flexDirection: rtl ? 'row-reverse' : 'row',
  };

  // Context value
  const contextValue = {
    theme: themeWithoutRTL,
    colors: themeWithoutRTL, // Add colors property that references the theme for compatibility
    isDark,
    toggleTheme,
    isLoading,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};