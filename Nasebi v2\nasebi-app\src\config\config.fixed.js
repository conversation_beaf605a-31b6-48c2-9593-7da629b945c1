import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Default API URL based on platform
let API_URL;

// IMPORTANT: Replace this with your actual IP address
// This is the IP address of your computer on the network
const DIRECT_IP = '************'; // Replace with your actual IP address

// Use direct IP address for all platforms
// This ensures consistent access from both emulators and physical devices
API_URL = `http://${DIRECT_IP}:3000`;

// Log configuration for debugging
console.log('Platform:', Platform.OS);
console.log('Using direct IP address:', DIRECT_IP);
console.log('API URL configured as:', API_URL);

// Override with environment variables if available
if (Constants.expoConfig?.extra?.apiUrl) {
  API_URL = Constants.expoConfig.extra.apiUrl;
  console.log('Overriding API URL from environment:', API_URL);
}

// Environment settings
const isDevelopment = process.env.NODE_ENV === 'development' || __DEV__;

// Stripe configuration
const STRIPE_PUBLISHABLE_KEY =
  Constants.expoConfig?.extra?.stripePublishableKey ||
  'pk_test_51OpGOZFzxLuwKT1JYzL55vTzxr8iUP8FqQeN1ofUWeSZAuvsfgsLCmQDToy4wJXnEurXlR6VVRKdEYZ6cz1VZtjw00IWMZrpKb';

// App configuration
const config = {
  API_URL,
  STRIPE_PUBLISHABLE_KEY,
  isDevelopment,
  // Set to false to use real data from the backend
  useMockData: false, // Disable mock data to use real database
  // App settings
  defaultLanguage: 'ar',
  supportedLanguages: ['ar', 'en'],
  apiTimeout: 30000, // 30 seconds
  profileCompletionMinimum: 70, // Percentage required to be considered "complete"
  maxPhotos: 6, // Maximum number of photos allowed per user
  requestTimeoutMs: 30000, // 30 seconds (matching apiTimeout)
};

export default config;
