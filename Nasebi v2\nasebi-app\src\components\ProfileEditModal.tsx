import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Image, Modal } from 'react-native';
import { useTranslation } from 'react-i18next';

interface ProfileEditModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (userData: any) => void;
  user: {
    name: string;
    bio: string;
    email: string;
    phone: string;
    profileImage: string;
  };
}

export const ProfileEditModal: React.FC<ProfileEditModalProps> = ({ visible, onClose, onSave, user }) => {
  const { t } = useTranslation();
  const [name, setName] = useState(user.name);
  const [bio, setBio] = useState(user.bio);
  const [email, setEmail] = useState(user.email);
  const [phone, setPhone] = useState(user.phone);
  const [profileImage, setProfileImage] = useState(user.profileImage);

  const handleImagePress = () => {
    // Implementation of handleImagePress
  };

  const handleSave = () => {
    // Implementation of handleSave
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{t('profile.editModal.title')}</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>{t('profile.editModal.name')}</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder={t('profile.editModal.name')}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>{t('profile.editModal.bio')}</Text>
            <TextInput
              style={[styles.input, styles.bioInput]}
              value={bio}
              onChangeText={setBio}
              placeholder={t('profile.editModal.bio')}
              multiline
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>{t('profile.editModal.email')}</Text>
            <TextInput
              style={styles.input}
              value={email}
              onChangeText={setEmail}
              placeholder={t('profile.editModal.email')}
              keyboardType="email-address"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>{t('profile.editModal.phone')}</Text>
            <TextInput
              style={styles.input}
              value={phone}
              onChangeText={setPhone}
              placeholder={t('profile.editModal.phone')}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.imageContainer}>
            <Text style={styles.label}>{t('profile.editModal.profileImage')}</Text>
            <TouchableOpacity style={styles.imageButton} onPress={handleImagePress}>
              <Image
                source={profileImage ? { uri: profileImage } : require('../assets/default-avatar.png')}
                style={styles.profileImage}
              />
              <Text style={styles.changeImageText}>{t('profile.editModal.changeImage')}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Text style={styles.saveButtonText}>{t('profile.editModal.save')}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>{t('profile.editModal.cancel')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
  },
  bioInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  imageContainer: {
    marginBottom: 15,
  },
  imageButton: {
    alignItems: 'center',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 10,
  },
  changeImageText: {
    color: '#007AFF',
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    marginRight: 10,
  },
  saveButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    marginLeft: 10,
  },
  cancelButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 