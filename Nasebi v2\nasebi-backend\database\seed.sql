
-- Drop existing tables if they exist
DROP TABLE IF EXISTS users;

-- Recreate users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  is_verified BOOLEAN DEFAULT FALSE,
  verification_token VARCHAR(255),
  reset_token VARCHAR(255),
  reset_token_expires TIMESTAMP NULL,
  last_login TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert test users
INSERT INTO users (email, password, is_verified, is_active) VALUES
('<EMAIL>', '$2b$10$xxxxxxxxxxx', TRUE, TRUE),
('<EMAIL>', '$2b$10$xxxxxxxxxxx', TRUE, TRUE),
('<EMAIL>', '$2b$10$xxxxxxxxxxx', TRUE, TRUE),
('<EMAIL>', '$2b$10$xxxxxxxxxxx', TRUE, TRUE),
('<EMAIL>', '$2b$10$xxxxxxxxxxx', TRUE, TRUE);
