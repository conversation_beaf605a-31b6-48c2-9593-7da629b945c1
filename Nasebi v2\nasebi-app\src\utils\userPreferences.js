import AsyncStorage from '@react-native-async-storage/async-storage';

const PREFERENCE_KEYS = {
  LANGUAGE: 'user-language',
  THEME: 'user-theme',
  NOTIFICATIONS: 'notifications-enabled',
  HIDE_PHOTOS: 'hide-photos',
  LAST_LOCATION: 'last-location',
  SEARCH_RADIUS: 'search-radius',
  AGE_RANGE: 'age-range',
  PRAYER_LEVEL: 'prayer-level',
  RELIGIOUS_LEVEL: 'religious-level',
};

export const savePreference = async (key, value) => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (error) {
    console.error(`Error saving preference ${key}:`, error);
  }
};

export const getPreference = async (key, defaultValue = null) => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : defaultValue;
  } catch (error) {
    console.error(`Error getting preference ${key}:`, error);
    return defaultValue;
  }
};

export const getAllPreferences = async () => {
  try {
    const keys = Object.values(PREFERENCE_KEYS);
    const items = await AsyncStorage.multiGet(keys);
    
    return items.reduce((preferences, [key, value]) => {
      try {
        preferences[key] = value ? JSON.parse(value) : null;
      } catch {
        preferences[key] = value;
      }
      return preferences;
    }, {});
  } catch (error) {
    console.error('Error getting all preferences:', error);
    return {};
  }
};

export const clearAllPreferences = async () => {
  try {
    const keys = Object.values(PREFERENCE_KEYS);
    await AsyncStorage.multiRemove(keys);
  } catch (error) {
    console.error('Error clearing preferences:', error);
  }
};

export const getSearchPreferences = async () => {
  const [ageRange, searchRadius, prayerLevel, religiousLevel] = await Promise.all([
    getPreference(PREFERENCE_KEYS.AGE_RANGE, { min: 18, max: 50 }),
    getPreference(PREFERENCE_KEYS.SEARCH_RADIUS, 50),
    getPreference(PREFERENCE_KEYS.PRAYER_LEVEL, 'any'),
    getPreference(PREFERENCE_KEYS.RELIGIOUS_LEVEL, 'any'),
  ]);

  return {
    ageRange,
    searchRadius,
    prayerLevel,
    religiousLevel,
  };
};

export const saveSearchPreferences = async (preferences) => {
  const {
    ageRange,
    searchRadius,
    prayerLevel,
    religiousLevel,
  } = preferences;

  await Promise.all([
    savePreference(PREFERENCE_KEYS.AGE_RANGE, ageRange),
    savePreference(PREFERENCE_KEYS.SEARCH_RADIUS, searchRadius),
    savePreference(PREFERENCE_KEYS.PRAYER_LEVEL, prayerLevel),
    savePreference(PREFERENCE_KEYS.RELIGIOUS_LEVEL, religiousLevel),
  ]);
};

export { PREFERENCE_KEYS };