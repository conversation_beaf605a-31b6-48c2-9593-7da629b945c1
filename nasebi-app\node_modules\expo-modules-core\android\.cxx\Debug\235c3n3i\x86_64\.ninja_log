# ninja log v5
1	16199	7694248955436119	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	b7e476a2f7c56eeb
10	11343	7694248907070170	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/Desktop/Nasebi_v2/nasebi-app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	8f3a9981cd7b326f
33	11650	7694248910004901	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	18918330bfde2146
11654	15023	7694248938929099	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/Desktop/Nasebi_v2/nasebi-app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	e4ee0f23fd223c57
22	16381	7694248957757793	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	1109128ef2728f1b
6	11906	7694248912442857	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/Desktop/Nasebi_v2/nasebi-app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	1b7f44233a8d765c
66	12619	7694248919729724	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	f26621ffe37f6229
48	13817	7694248932148583	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	5ed20725a7086905
58	12972	7694248922790833	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	1cc0bcac86053125
14	12766	7694248919926601	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	7e61cce478f2a704
27	14623	7694248939189081	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	33bc10aa08aa7a8
43	15545	7694248948867787	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	3e21e28fcd7d50cf
12829	15677	7694248950435863	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	91de6bfe875e87bf
18	15736	7694248950375841	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	c27d03a29037852c
11347	16526	7694248959081182	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	ff9e3372dbb6230a
38	16653	7694248960541751	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIInteropModuleRegistry.cpp.o	e09dde76a0056ade
12975	16970	7694248963887104	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	ecfe3407081ed060
11907	17830	7694248972460615	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	b9043a1006d26b09
14659	18707	7694248981257273	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	da9e139d55643175
53	18740	7694248981467643	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	cd5e64509e275dbf
13818	19857	7694248992789227	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	4cdfb700e3391300
12620	20491	7694248999138927	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	a912b1f75099e58c
20491	20723	7694249001500401	../../../../build/intermediates/cxx/Debug/235c3n3i/obj/x86_64/libexpo-modules-core.so	591aad40d9288877
0	32	0	clean	6455a38a52da88c5
