# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 57ms
  [gap of 16ms]
create_cxx_tasks completed in 73ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 17ms
    [gap of 21ms]
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 16ms
    create-module-model
      [gap of 16ms]
      create-cmake-model 10ms
    create-module-model completed in 28ms
    [gap of 25ms]
    create-X86-model 17ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 169ms
  [gap of 44ms]
create_cxx_tasks completed in 213ms

# C/C++ build system timings# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 66ms]
      create-ndk-meta-abi-list 14ms
create_cxx_tasks
      [gap of 33ms]
  create-initial-cxx-model
    create-module-model completed in 113ms
    create-module-model
      [gap of 28ms]
      create-ndk-meta-abi-list 49ms
    create-variant-model 10ms
      [gap of 11ms]
      create-cmake-model 11ms
    create-module-model completed in 102ms
    create-ARMEABI_V7A-model 27ms
    [gap of 16ms]
    create-ARM64_V8A-model 10ms
    create-ARM64_V8A-model 12ms
    create-X86_64-model 12ms
    create-module-model
      [gap of 24ms]
      create-ndk-meta-abi-list 12ms
      create-cmake-model 10ms
    create-module-model
      [gap of 28ms]
    create-module-model completed in 50ms
      create-ndk-meta-abi-list 12ms
      [gap of 12ms]
    create-module-model completed in 52ms
    [gap of 16ms]
    create-ARM64_V8A-model 17ms
    [gap of 36ms]
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 249ms
    create-X86-model 10ms
  create-initial-cxx-model completed in 297ms
  [gap of 35ms]
create_cxx_tasks completed in 284ms


  [gap of 35ms]
create_cxx_tasks completed in 333ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 12ms
    [gap of 11ms]
    create-module-model 17ms
    [gap of 29ms]
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 113ms
create_cxx_tasks completed in 120ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 36ms]# C/C++ build system timings
      create-cmake-model 15ms
      [gap of 28ms]
    create-module-model completed in 79ms
    [gap of 11ms]
    create-variant-model 19ms
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 16ms]
      create-cmake-model 21ms
      [gap of 13ms]
    create-ARMEABI_V7A-model 25ms
    create-module-model completed in 50ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 29ms
    create-ARM64_V8A-model 47ms
    create-ARM64_V8A-model 16ms
    create-X86-model 21ms
    create-X86-model 22ms
    create-X86_64-model 13ms
    create-module-model
      [gap of 15ms]
      create-cmake-model 12ms
    create-X86_64-model 29ms
    create-module-model completed in 29ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-module-model
      [gap of 13ms]
      create-cmake-model 13ms
    create-module-model completed in 28ms
    create-ARM64_V8A-model 10ms
    create-variant-model 13ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    create-ARMEABI_V7A-model 15ms
  create-initial-cxx-model completed in 312ms
    create-ARM64_V8A-model 12ms
    create-X86_64-model 19ms
  create-initial-cxx-model completed in 273ms
  [gap of 50ms]
create_cxx_tasks completed in 363ms


  [gap of 33ms]
create_cxx_tasks completed in 307ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-ARMEABI_V7A-model 10ms
    create-X86-model 10ms
    [gap of 32ms]
  create-initial-cxx-model completed in 58ms
create_cxx_tasks completed in 67ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 66ms
create_cxx_tasks completed in 69ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-module-model
      [gap of 12ms]
      create-cmake-model 11ms
    create-module-model completed in 27ms
    create-variant-model 13ms
    [gap of 24ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 139ms
  [gap of 30ms]
create_cxx_tasks completed in 170ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    [gap of 29ms]
    create-module-model 10ms
    [gap of 16ms]
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 80ms
  [gap of 12ms]
create_cxx_tasks completed in 92ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-variant-model 14ms
    create-module-model
      create-project-model 15ms
    create-module-model completed in 16ms
    create-ARMEABI_V7A-model 17ms
    [gap of 17ms]
  create-initial-cxx-model completed in 93ms
  [gap of 19ms]
create_cxx_tasks completed in 112ms

