/**
 * <PERSON><PERSON><PERSON> to run the layout checker on all screens and components
 * This script adds temporary code to each file to run the debugLayoutIssues function
 * and then runs the app to see the issues in the console.
 * 
 * Usage:
 * node scripts/run-layout-checker.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Directories to scan
const DIRS_TO_SCAN = [
  path.join(__dirname, '..', 'src', 'screens'),
  path.join(__dirname, '..', 'src', 'components'),
];

// Files to skip (e.g., utility files, context providers)
const SKIP_FILES = [
  'index.js',
  'styles.js',
  'Context.js',
  'Provider.js',
  'utils.js',
];

// Track modified files to restore them later
const modifiedFiles = [];

/**
 * Add layout checker to a file
 */
function addLayoutChecker(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath);
    
    // Skip non-JS/JSX files and files in the skip list
    if ((!filePath.endsWith('.js') && !filePath.endsWith('.jsx')) || 
        SKIP_FILES.some(skipFile => fileName.includes(skipFile))) {
      return;
    }
    
    // Skip files that don't have StyleSheet
    if (!content.includes('StyleSheet.create')) {
      return;
    }
    
    console.log(`Adding layout checker to ${fileName}...`);
    
    // Check if the file already imports debugLayoutIssues
    if (content.includes('debugLayoutIssues')) {
      console.log(`  Layout checker already added to ${fileName}`);
      return;
    }
    
    // Create modified content
    let modifiedContent = content;
    
    // Add import for debugLayoutIssues
    const importRegex = /import.*from.*;/g;
    const imports = [...content.matchAll(importRegex)];
    
    if (imports.length > 0) {
      const lastImport = imports[imports.length - 1];
      const insertPosition = lastImport.index + lastImport[0].length;
      
      modifiedContent = modifiedContent.slice(0, insertPosition) + 
                        '\nimport { debugLayoutIssues } from \'../../utils/layoutChecker\';\n' + 
                        modifiedContent.slice(insertPosition);
    }
    
    // Add debugLayoutIssues call after StyleSheet.create
    const styleSheetRegex = /(const\s+styles\s*=\s*StyleSheet\.create\(\s*\{[^}]*\}\s*\)\s*;)/;
    const styleSheetMatch = modifiedContent.match(styleSheetRegex);
    
    if (styleSheetMatch) {
      const insertPosition = styleSheetMatch.index + styleSheetMatch[0].length;
      const componentName = fileName.replace(/\.(js|jsx)$/, '');
      
      modifiedContent = modifiedContent.slice(0, insertPosition) + 
                        `\n\n// DEBUG RTL ISSUES\ndebugLayoutIssues('${componentName}', styles);\n` + 
                        modifiedContent.slice(insertPosition);
      
      // Save original content to restore later
      modifiedFiles.push({
        path: filePath,
        originalContent: content
      });
      
      // Write modified content
      fs.writeFileSync(filePath, modifiedContent, 'utf8');
      console.log(`  Added layout checker to ${fileName}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

/**
 * Recursively scan directories for files
 */
function scanDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      scanDirectory(filePath);
    } else {
      addLayoutChecker(filePath);
    }
  });
}

/**
 * Restore original files
 */
function restoreFiles() {
  console.log('\nRestoring original files...');
  
  modifiedFiles.forEach(file => {
    fs.writeFileSync(file.path, file.originalContent, 'utf8');
    console.log(`  Restored ${path.basename(file.path)}`);
  });
  
  console.log('All files restored');
}

// Start scanning directories
console.log('Adding layout checker to files...');
DIRS_TO_SCAN.forEach(scanDirectory);

console.log(`\nAdded layout checker to ${modifiedFiles.length} files`);
console.log('\nNow run the app and check the console for RTL issues.');
console.log('Press Ctrl+C when done to restore the original files.');

// Set up cleanup on exit
process.on('SIGINT', () => {
  console.log('\nReceived SIGINT. Cleaning up...');
  restoreFiles();
  process.exit(0);
});

// Keep the script running until user terminates it
console.log('\nWaiting for you to check the app and press Ctrl+C...');
setInterval(() => {}, 1000);
