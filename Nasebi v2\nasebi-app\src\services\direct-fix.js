/**
 * DIRECT FIX FOR API CONNECTION ISSUES
 * 
 * This file contains a direct fix for the API connection issues in the Nasebi app.
 * Follow these steps to implement the fix:
 * 
 * 1. Run the test server: node direct-login-test.js
 * 2. Note the IP address displayed in the console
 * 3. Update the DIRECT_IP_ADDRESS constant below with your IP address
 * 4. Copy the entire apiWrapper object from this file
 * 5. Replace the apiWrapper object in src/services/api.js with this one
 */

// ============================================================================
// STEP 1: Update this constant with your actual IP address
// ============================================================================
const DIRECT_IP_ADDRESS = '************'; // Replace with your actual IP address
// ============================================================================

// Create a wrapper for the API that adds error handling and logging
const apiWrapper = {
  get: async (url, config = {}) => {
    try {
      console.log(`API GET request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to GET request: ${url}`);
        }
      }

      // Use direct IP address for the request
      const directUrl = `http://${DIRECT_IP_ADDRESS}:3000${url}`;
      console.log(`Using direct URL: ${directUrl}`);
      
      const response = await axios.get(directUrl, {
        ...config,
        timeout: 30000
      });
      
      return response;
    } catch (error) {
      console.error(`API Error in GET ${url}:`, error.message);
      throw error;
    }
  },
  post: async (url, data, config = {}) => {
    try {
      console.log(`API POST request to: ${url}`);
      console.log('Request data:', data);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to POST request: ${url}`);
        }
      }

      // Use direct IP address for the request
      const directUrl = `http://${DIRECT_IP_ADDRESS}:3000${url}`;
      console.log(`Using direct URL: ${directUrl}`);
      
      const response = await axios.post(directUrl, data, {
        ...config,
        timeout: 30000
      });
      
      return response;
    } catch (error) {
      console.error(`API Error in POST ${url}:`, error.message);
      throw error;
    }
  },
  put: async (url, data, config = {}) => {
    try {
      console.log(`API PUT request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to PUT request: ${url}`);
        }
      }

      // Use direct IP address for the request
      const directUrl = `http://${DIRECT_IP_ADDRESS}:3000${url}`;
      console.log(`Using direct URL: ${directUrl}`);
      
      const response = await axios.put(directUrl, data, {
        ...config,
        timeout: 30000
      });
      
      return response;
    } catch (error) {
      console.error(`API Error in PUT ${url}:`, error.message);
      throw error;
    }
  },
  delete: async (url, config = {}) => {
    try {
      console.log(`API DELETE request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to DELETE request: ${url}`);
        }
      }

      // Use direct IP address for the request
      const directUrl = `http://${DIRECT_IP_ADDRESS}:3000${url}`;
      console.log(`Using direct URL: ${directUrl}`);
      
      const response = await axios.delete(directUrl, {
        ...config,
        timeout: 30000
      });
      
      return response;
    } catch (error) {
      console.error(`API Error in DELETE ${url}:`, error.message);
      throw error;
    }
  },
  patch: async (url, data, config = {}) => {
    try {
      console.log(`API PATCH request to: ${url}`);

      // Ensure we have a config object with headers
      if (!config.headers) {
        config.headers = {};
      }

      // Add auth token if not already present
      if (!config.headers.Authorization) {
        const token = await AsyncStorage.getItem('auth-token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log(`Added auth token to PATCH request: ${url}`);
        }
      }

      // Use direct IP address for the request
      const directUrl = `http://${DIRECT_IP_ADDRESS}:3000${url}`;
      console.log(`Using direct URL: ${directUrl}`);
      
      const response = await axios.patch(directUrl, data, {
        ...config,
        timeout: 30000
      });
      
      return response;
    } catch (error) {
      console.error(`API Error in PATCH ${url}:`, error.message);
      throw error;
    }
  }
};
