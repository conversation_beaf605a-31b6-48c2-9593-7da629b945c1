export default {
  common: {
    appName: '<PERSON><PERSON><PERSON>',
    findMatch: 'Find your perfect match',
    loading: 'Loading...',
    cancel: 'Cancel',
    save: 'Save',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    error: 'Error',
    success: 'Success',
  },
  auth: {
    login: 'Login',
    register: 'Register',
    email: 'Email',
    password: 'Password',
    forgotPassword: 'Forgot Password?',
    resetPassword: 'Reset Password',
    resetPasswordInstructions: 'Enter your email address and we will send you instructions to reset your password.',
    resetPasswordSent: 'Password reset email sent!',
    redirectingToLogin: 'Redirecting to login...',
    resetPasswordError: 'Failed to send reset email. Please try again.',
    emailRequired: 'Email is required',
    invalidEmailFormat: 'Invalid email format',
    hasAccount: 'Already have an account?',
    noAccount: 'Don\'t have an account?',
    iAmA: 'I am a',
    male: 'Male',
    female: 'Female',
    preferredLanguage: 'Preferred Language',
    continue: 'Continue',
  },
  home: {
    noMoreProfiles: 'No more profiles to show',
    refresh: 'Refresh',
    tabLabel: 'Discover',
    like: 'LIKE',
    nope: 'NOPE',
  },
  matches: {
    tabLabel: 'Matches',
    noMatches: 'No matches yet',
    likeHint: 'Tap to see who likes you',
  },
  messages: {
    tabLabel: 'Messages',
    noMessagesTitle: 'No messages yet',
    noMessagesDesc: 'Start a conversation with your matches',
    justNow: 'Just now',
    hoursAgo: 'hours ago',
    daysAgo: 'days ago',
    you: 'You',
  },
  profile: {
    tabLabel: 'Profile',
    edit: 'Edit',
    addPhoto: 'Add Photo',
    basicInfo: 'Basic Information',
    name: 'Name',
    age: 'Age',
    location: 'Location',
    bio: 'Bio',
  },
  settings: {
    account: 'Account',
    editProfile: 'Edit Profile',
    notifications: 'Notifications',
    privacy: 'Privacy',
    preferences: 'Preferences',
    darkMode: 'Dark Mode',
    language: 'Arabic',
    matchPreferences: 'Match Preferences',
    subscription: 'Subscription',
    premiumPlan: 'Premium Plan',
    info: 'Information',
    about: 'About',
    help: 'Help & Support',
    logout: 'Logout',
    logoutTitle: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',
  },
  notifications: {
    pushNotifications: 'Push Notifications',
    messages: 'Messages',
    messagesDescription: 'Get notified when you receive a new message',
    matches: 'New Matches',
    matchesDescription: 'Get notified when you have a new match',
    likes: 'Likes',
    likesDescription: 'Get notified when someone likes your profile',
    appUpdates: 'App Updates',
    appUpdatesDescription: 'Get notified about app updates and new features',
    events: 'Events',
    eventsDescription: 'Get notified about events and promotions',
    emailNotifications: 'Email Notifications',
    marketing: 'Marketing',
    marketingDescription: 'Receive marketing emails about offers and updates',
    preferences: 'Notification Preferences',
    vibration: 'Vibration',
    sound: 'Sound',
    resetToDefault: 'Reset to Default',
    disclaimer: 'You can change your notification preferences at any time in settings.',
  },
  about: {
    version: 'Version',
    description: 'The halal way to find your perfect match',
    info: 'Information',
    website: 'Website',
    contactUs: 'Contact Us',
    termsOfService: 'Terms of Service',
    viewTerms: 'View Terms',
    privacyPolicy: 'Privacy Policy',
    viewPrivacy: 'View Privacy',
    followUs: 'Follow Us',
    technical: 'Technical Information',
    appId: 'App ID',
    deviceInfo: 'Device Info',
    rateApp: 'Rate this App',
    allRightsReserved: 'All Rights Reserved',
  },
  subscription: {
    subscribeNow: 'Subscribe Now',
    subtitle: 'Unlock premium features to enhance your experience',
    monthlyTitle: 'Monthly',
    quarterlyTitle: 'Quarterly',
    yearlyTitle: 'Yearly',
    month: 'month',
    quarter: 'quarter',
    year: 'year',
    mostPopular: 'Most Popular',
    feature1: 'Create profile',
    feature2: 'Browse matches',
    feature3: 'Send likes',
    feature4: 'Advanced filters',
    feature5: 'Priority in search results',
    subscribe: 'Subscribe Now',
    premium: 'Premium',
    activeSubscription: 'Active Subscription',
    validUntil: 'Valid until',
    autoRenewal: 'Auto-renewal is enabled',
    noAutoRenewal: 'Auto-renewal is disabled',
    cancelSubscription: 'Cancel Subscription',
    premiumBenefits: 'Premium Benefits',
    benefit1Title: 'See Who Likes You',
    benefit1Description: 'See who has liked your profile before you like them',
    benefit2Title: 'Unlimited Messages',
    benefit2Description: 'Send unlimited messages to your matches',
    benefit3Title: 'Advanced Filters',
    benefit3Description: 'Use advanced filters to find your perfect match',
    securePayment: 'Secure payment processing',
    cancelTitle: 'Cancel Subscription',
    cancelConfirm: 'Are you sure you want to cancel your subscription?',
    cancelSuccess: 'Your subscription has been cancelled successfully',
    cancelError: 'There was an error cancelling your subscription',
    successMessage: 'Your subscription has been activated successfully',
    processingError: 'There was an error processing your subscription',
    initError: 'There was an error initializing the payment',
    paymentError: 'There was an error processing your payment',
    planNotFound: 'The selected subscription plan was not found',
    fetchError: 'There was an error fetching subscription plans',
    demoMode: 'Demo Mode',
    demoMessage: 'The payment endpoint is not available. For demonstration purposes, your subscription will be activated without payment processing.',
    demoSuccessMessage: 'Your subscription has been activated for demonstration purposes.',
    free: 'Free',
    freeSubscription: 'Free Basic Plan',
    freeSubscriptionInfo: 'You are currently on the free basic plan. Upgrade to premium for more features.',
    upgradeNow: 'Upgrade Now',
    cannotCancelDefault: 'You cannot cancel the free basic plan.',
    selectPlanMessage: 'Please select a subscription plan to continue',
  },
};