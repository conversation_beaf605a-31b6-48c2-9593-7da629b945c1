import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
  Animated,
  Platform,
  Easing,
  ImageBackground,
  Pressable,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Carousel from 'react-native-snap-carousel-v4';
import { Ionicons, MaterialCommunityIcons, FontAwesome5, AntDesign } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useLanguage } from '../../context/LanguageContext';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import api from '../../services/api';
import { useRTL } from '../../hooks/useRTL';
import BlurredProfileImage from '../../components/BlurredProfileImage';


const SCREEN_WIDTH = Dimensions.get('window').width;

const ViewProfile = ({ route, navigation }) => {
  const rtl = useRTL();
  const { colors, isDark } = useTheme();
  const { isRTL } = useLanguage();
  const { t } = useTranslationFlat();

  // Refs for animations
  const scrollY = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const carouselRef = useRef(null);

  // State variables
  const { userId } = route.params;
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activePhotoIndex, setActivePhotoIndex] = useState(0);
  const [error, setError] = useState(null);
  const [showFullBio, setShowFullBio] = useState(false);
  const [visibleSections, setVisibleSections] = useState({
    basicInfo: true,
    education: true,
    religious: true,
    marriage: true,
    lifestyle: true
  });

  useEffect(() => {
    loadProfile();
  }, [userId]);

  // Animation functions
  const animateIn = () => {
    // Reset animation values
    fadeAnim.setValue(0);
    scaleAnim.setValue(0.95);

    // Run parallel animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
    ]).start();
  };

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      // Record this profile view in the backend
      try {
        await api.post('/api/profile/view', { viewed_user_id: userId });
      } catch (viewError) {
        console.log('Error recording profile view:', viewError);
        // Continue even if recording the view fails
      }

      // Fetch the profile data
      const response = await api.get(`/api/profile/${userId}`);
      console.log('Profile data fetched:', response.data);

      if (response.data) {
        setProfile(response.data);
        // Start animations after data is loaded
        setTimeout(animateIn, 150);
      } else {
        setError('Profile data not found');
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      setError(error.userMessage || 'Failed to load profile');
      Alert.alert(
        t('error') || 'Error',
        error.userMessage || t('failedToLoadProfile') || 'Failed to load profile'
      );
    } finally {
      setLoading(false);
    }
  };

  // Toggle section visibility
  const toggleSection = (section) => {
    setVisibleSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleBlock = () => {
    Alert.alert(
      'Block User',
      'Are you sure you want to block this user? You will no longer see each other in matches.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Block',
          style: 'destructive',
          onPress: async () => {
            try {
              await api.post(`/api/users/block/${userId}`);
              navigation.goBack();
            } catch (error) {
              console.error('Error blocking user:', error);
              Alert.alert('Error', 'Failed to block user');
            }
          },
        },
      ],
    );
  };

  const handleReport = () => {
    Alert.alert(
      'Report User',
      'Please select a reason for reporting:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Inappropriate Content',
          onPress: () => reportUser('inappropriate_content'),
        },
        {
          text: 'Harassment',
          onPress: () => reportUser('harassment'),
        },
        {
          text: 'Fake Profile',
          onPress: () => reportUser('fake_profile'),
        },
        {
          text: 'Other',
          onPress: () => reportUser('other'),
        },
      ],
    );
  };

  const reportUser = async (reason) => {
    try {
      await api.post(`/api/users/report/${userId}`, { reason });
      Alert.alert('Thank You', 'Your report has been submitted for review.');
    } catch (error) {
      console.error('Error reporting user:', error);
      Alert.alert('Error', 'Failed to submit report');
    }
  };

  // Navigate through photos
  const goToNextPhoto = () => {
    if (profile.photos && profile.photos.length > 0 && carouselRef.current) {
      const nextIndex = (activePhotoIndex + 1) % profile.photos.length;
      carouselRef.current.snapToItem(nextIndex);
    }
  };

  const goToPrevPhoto = () => {
    if (profile.photos && profile.photos.length > 0 && carouselRef.current) {
      const prevIndex = activePhotoIndex === 0 ? profile.photos.length - 1 : activePhotoIndex - 1;
      carouselRef.current.snapToItem(prevIndex);
    }
  };

  const renderPhotoItem = ({ item }) => (
    <View style={styles.photoItemContainer}>
      <ImageBackground
        source={{ uri: item }}
        style={[
          styles.carouselPhoto,
          isRTL && { transform: [{ scaleX: -1 }] }
        ]}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.5)', 'transparent', 'transparent', 'rgba(0,0,0,0.5)']}
          style={styles.photoGradient}
        />
      </ImageBackground>

      {/* Photo navigation controls */}
      <View style={styles.photoControls}>
        <TouchableOpacity
          style={styles.photoControlButton}
          onPress={goToPrevPhoto}
          activeOpacity={0.7}
        >
          <Ionicons
            name={isRTL ? "chevron-forward" : "chevron-back"}
            size={28}
            color="#fff"
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.photoControlButton}
          onPress={goToNextPhoto}
          activeOpacity={0.7}
        >
          <Ionicons
            name={isRTL ? "chevron-back" : "chevron-forward"}
            size={28}
            color="#fff"
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  // Create a new section header component
  const SectionHeader = ({ title, section, icon }) => {
    const isVisible = visibleSections[section];

    return (
      <TouchableOpacity
        style={[
          styles.sectionHeader,
          { borderBottomColor: colors.border }
        ]}
        onPress={() => toggleSection(section)}
        activeOpacity={0.7}
      >
        <View style={styles.sectionHeaderLeft}>
          <View style={[styles.sectionIconContainer, { backgroundColor: colors.primary }]}>
            <Ionicons name={icon} size={18} color="#fff" />
          </View>
          <Text style={[
            styles.sectionTitle,
            { color: colors.text },
            isRTL && styles.textRTL
          ]}>
            {t(title) || title}
          </Text>
        </View>

        <Animated.View style={{
          transform: [{
            rotate: isVisible ? '0deg' : '180deg'
          }]
        }}>
          <Ionicons
            name="chevron-up"
            size={22}
            color={colors.subtext}
          />
        </Animated.View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Ionicons name={isRTL ? "chevron-forward" : "chevron-back"} size={28} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {t('profile') || 'Profile'}
          </Text>
          <View style={{ width: 40 }} />
        </View>

        <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            {t('loading') || 'Loading...'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !profile) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Ionicons name={isRTL ? "chevron-forward" : "chevron-back"} size={28} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {t('profile') || 'Profile'}
          </Text>
          <View style={{ width: 40 }} />
        </View>

        <View style={[styles.errorContainer, { backgroundColor: colors.background }]}>
          <View style={styles.errorIconContainer}>
            <Ionicons name="alert-circle-outline" size={70} color={colors.error} />
          </View>
          <Text style={[styles.errorTitle, { color: colors.text }]}>
            {t('oops') || 'Oops!'}
          </Text>
          <Text style={[styles.errorText, { color: colors.subtext }]}>
            {error || t('profileNotFound') || 'Profile not found'}
          </Text>
          <TouchableOpacity
            style={[styles.errorButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.errorButtonText}>
              {t('goBack') || 'Go Back'}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const renderDetailItem = (label, value, iconName) => {
    if (!value) return null;

    return (
      <Animated.View
        style={[
          styles.detailItem,
          {
            backgroundColor: isDark ? colors.card : '#f8f9fa',
            borderColor: colors.border,
          },
          isRTL && styles.detailItemRTL,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        <View style={[
          styles.detailIconContainer,
          {
            backgroundColor: colors.primary,
            shadowColor: colors.primary,
          }
        ]}>
          <Ionicons name={iconName} size={22} color="#fff" />
        </View>

        <View style={styles.detailTextContainer}>
          <Text style={[
            styles.detailLabel,
            { color: colors.subtext },
            isRTL && styles.textRTL
          ]}>
            {t(label) || label}
          </Text>
          <Text style={[
            styles.detailValue,
            { color: colors.text },
            isRTL && styles.textRTL
          ]}>
            {value}
          </Text>
        </View>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Floating Header with back button and user name */}
      <Animated.View style={[
        styles.header,
        {
          backgroundColor: colors.background,
          borderBottomColor: colors.border,
          shadowColor: colors.shadow,
        }
      ]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons
            name={isRTL ? "chevron-forward" : "chevron-back"}
            size={28}
            color={colors.text}
          />
        </TouchableOpacity>

        <Text style={[styles.headerTitle, { color: colors.text }]}>
          {profile.name}
        </Text>

        <TouchableOpacity
          style={styles.reportIconButton}
          onPress={handleReport}
          activeOpacity={0.7}
        >
          <Ionicons name="flag-outline" size={24} color={colors.text} />
        </TouchableOpacity>
      </Animated.View>

      <Animated.ScrollView
        style={{ backgroundColor: colors.background }}
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
        scrollEventThrottle={16}
      >
        {/* Photo Carousel Section */}
        <View style={styles.photoSection}>
          {profile.photos && profile.photos.length > 0 ? (
            <>
              <Carousel
                ref={carouselRef}
                data={profile.photos}
                renderItem={renderPhotoItem}
                sliderWidth={SCREEN_WIDTH}
                itemWidth={SCREEN_WIDTH}
                onSnapToItem={(index) => setActivePhotoIndex(index)}
                inactiveSlideOpacity={1}
                inactiveSlideScale={1}
                loop={true}
                autoplay={false}
                lockScrollWhileSnapping={true}
              />
              <View style={styles.photoIndicators}>
                {profile.photos.map((_, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.photoIndicatorTouchable}
                    onPress={() => carouselRef.current.snapToItem(index)}
                    activeOpacity={0.8}
                  >
                    <View
                      style={[
                        styles.photoIndicator,
                        {
                          backgroundColor: index === activePhotoIndex
                            ? colors.primary
                            : 'rgba(255,255,255,0.4)',
                          width: index === activePhotoIndex ? 20 : 8,
                        },
                      ]}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </>
          ) : (
            <View style={styles.photoItemContainer}>
              <BlurredProfileImage
                source={{ uri: 'https://randomuser.me/api/portraits/men/1.jpg' }}
                style={styles.fallbackPhoto}
                showEyeIcon={true}
                blurIntensity={110}
              />
              <LinearGradient
                colors={['rgba(0,0,0,0.5)', 'transparent', 'transparent', 'rgba(0,0,0,0.5)']}
                style={styles.photoGradient}
              />
            </View>
          )}
        </View>

        {/* Profile Info Container */}
        <Animated.View
          style={[
            styles.infoContainer,
            {
              backgroundColor: colors.background,
              opacity: fadeAnim,
              transform: [{ translateY: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              })}]
            }
          ]}
        >
          {/* Basic Info Header */}
          <View style={[
            styles.profileHeader,
            isRTL && styles.profileHeaderRTL
          ]}>
            <View style={styles.nameContainer}>
              <Text style={[
                styles.name,
                { color: colors.text },
                isRTL && styles.textRTL
              ]}>
                {profile.name}, {profile.age || '--'}
              </Text>

              <View style={[
                styles.locationRow,
                isRTL && styles.locationRowRTL
              ]}>
                <Ionicons
                  name="location-outline"
                  size={16}
                  color={colors.subtext}
                />
                <Text style={[
                  styles.location,
                  { color: colors.subtext },
                  isRTL && styles.textRTL
                ]}>
                  {profile.location || profile.city || t('locationNotSpecified')}
                </Text>
              </View>
            </View>

            {profile.is_verified && (
              <View style={[
                styles.verifiedBadge,
                { backgroundColor: colors.primary }
              ]}>
                <Ionicons name="checkmark-circle" size={16} color="#fff" />
                <Text style={styles.verifiedText}>
                  {t('verified')}
                </Text>
              </View>
            )}
          </View>

          {/* About Section */}
          <View style={[
            styles.section,
            {
              backgroundColor: colors.background,
              marginBottom: 15,
            }
          ]}>
            <View style={styles.bioContainer}>
              <Text
                style={[
                  styles.bio,
                  { color: colors.text },
                  isRTL && styles.textRTL,
                  showFullBio ? null : { maxHeight: 80 }
                ]}
                numberOfLines={showFullBio ? undefined : 3}
              >
                {profile.bio || t('bioNotProvided')}
              </Text>

              {profile.bio && profile.bio.length > 100 && (
                <TouchableOpacity
                  style={[styles.readMoreButton, { borderColor: colors.primary }]}
                  onPress={() => setShowFullBio(!showFullBio)}
                >
                  <Text style={[styles.readMoreText, { color: colors.primary }]}>
                    {showFullBio ? t('readLess') || 'Read Less' : t('readMore') || 'Read More'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Basic Information Section */}
          <View style={styles.section}>
            <SectionHeader
              title="basicInformation"
              section="basicInfo"
              icon="information-circle-outline"
            />

            {visibleSections.basicInfo && (
              <View style={styles.detailsGrid}>
                {profile.height && renderDetailItem('height', `${profile.height} cm`, 'resize-outline')}
                {profile.nationality && renderDetailItem('nationality', profile.nationality, 'flag-outline')}
                {profile.marital_status && renderDetailItem('maritalStatus', t(`maritalStatus_${profile.marital_status}`) || profile.marital_status, 'heart-outline')}
                {profile.has_children !== undefined && renderDetailItem('hasChildren', profile.has_children ? t('yes') : t('no'), 'people-outline')}
                {profile.wants_children && renderDetailItem('wantsChildren', t(`children_${profile.wants_children}`) || profile.wants_children, 'happy-outline')}
              </View>
            )}
          </View>

          {/* Professional Section */}
          <View style={styles.section}>
            <SectionHeader
              title="educationAndWork"
              section="education"
              icon="school-outline"
            />

            {visibleSections.education && (
              <View style={styles.detailsGrid}>
                {profile.education_level && renderDetailItem('educationLevel', t(`education_${profile.education_level}`) || profile.education_level, 'school-outline')}
                {profile.job_level && renderDetailItem('jobLevel', t(`job_${profile.job_level}`) || profile.job_level, 'briefcase-outline')}
                {profile.income_level && renderDetailItem('incomeLevel', t(`income_${profile.income_level}`) || profile.income_level, 'cash-outline')}
                {profile.occupation && renderDetailItem('occupation', profile.occupation, 'business-outline')}
              </View>
            )}
          </View>

          {/* Religious Background Section */}
          <View style={styles.section}>
            <SectionHeader
              title="religiousInfo"
              section="religious"
              icon="moon-outline"
            />

            {visibleSections.religious && (
              <View style={styles.detailsGrid}>
                {profile.religious_level && renderDetailItem('religiousLevel', t(`religious_${profile.religious_level}`) || profile.religious_level, 'moon-outline')}
                {profile.prayer_level && renderDetailItem('prayerLevel', t(`prayer_${profile.prayer_level}`) || profile.prayer_level, 'time-outline')}
                {profile.fasting_level && renderDetailItem('fastingLevel', t(`fasting_${profile.fasting_level}`) || profile.fasting_level, 'calendar-outline')}
                {profile.hajj_status && renderDetailItem('hajjStatus', t(`hajj_${profile.hajj_status}`) || profile.hajj_status, 'navigate-outline')}
              </View>
            )}
          </View>

          {/* Marriage Preferences Section */}
          <View style={styles.section}>
            <SectionHeader
              title="marriagePreferences"
              section="marriage"
              icon="heart-outline"
            />

            {visibleSections.marriage && (
              <View style={styles.detailsGrid}>
                {profile.marriage_readiness && renderDetailItem('marriageReadiness', t(`marriageReadiness_${profile.marriage_readiness}`) || profile.marriage_readiness, 'calendar-outline')}
                {profile.preferred_residence && renderDetailItem('preferredResidence', t(`residence_${profile.preferred_residence}`) || profile.preferred_residence, 'home-outline')}
                {profile.allows_wife_to_work && renderDetailItem('allowsWifeToWork', t(`work_${profile.allows_wife_to_work}`) || profile.allows_wife_to_work, 'woman-outline')}
                {profile.tribal_affiliation !== undefined && renderDetailItem('tribalAffiliation', profile.tribal_affiliation ? t('yes') : t('no'), 'people-circle-outline')}
              </View>
            )}
          </View>

          {/* Lifestyle Section */}
          <View style={styles.section}>
            <SectionHeader
              title="lifestyle"
              section="lifestyle"
              icon="fitness-outline"
            />

            {visibleSections.lifestyle && (
              <View style={styles.detailsGrid}>
                {profile.smoking !== undefined && renderDetailItem('smoking', profile.smoking ? t('yes') : t('no'), 'close-circle-outline')}
                {profile.health_status && renderDetailItem('healthStatus', t(`health_${profile.health_status}`) || profile.health_status, 'medical-outline')}
                {profile.skin_color && renderDetailItem('skinColor', t(`skinColor_${profile.skin_color}`) || profile.skin_color, 'color-palette-outline')}
                {profile.weight && renderDetailItem('weight', `${profile.weight} kg`, 'fitness-outline')}
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <Animated.View
            style={[
              styles.actionsContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0]
                })}]
              }
            ]}
          >
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: colors.error }
              ]}
              onPress={handleBlock}
              activeOpacity={0.8}
            >
              <Ionicons name="ban-outline" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>
                {t('blockUser')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: colors.warning }
              ]}
              onPress={handleReport}
              activeOpacity={0.8}
            >
              <Ionicons name="flag-outline" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>
                {t('reportUser')}
              </Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Bottom padding for better scrolling */}
          <View style={{ height: 30 }} />
        </Animated.View>
      </Animated.ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Header styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 60,
    borderBottomWidth: 1,
    zIndex: 10,
    elevation: 3,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  reportIconButton: {
    padding: 8,
    borderRadius: 20,
  },

  // Loading and error styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorIconContainer: {
    marginBottom: 20,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
    maxWidth: '80%',
  },
  errorButton: {
    paddingVertical: 14,
    paddingHorizontal: 30,
    borderRadius: 25,
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  errorButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },

  // Photo section styles
  photoSection: {
    height: SCREEN_WIDTH,
    position: 'relative',
  },
  photoItemContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH,
    position: 'relative',
  },
  carouselPhoto: {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH,
    justifyContent: 'space-between',
  },
  photoGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  fallbackPhoto: {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH,
  },
  photoIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    zIndex: 2,
  },
  photoIndicatorTouchable: {
    padding: 5,
  },
  photoIndicator: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 2,
  },
  photoControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    zIndex: 1,
  },
  photoControlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Profile info styles
  infoContainer: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
    paddingTop: 25,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  profileHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  nameContainer: {
    flex: 1,
  },
  name: {
    fontSize: 26,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  locationRowRTL: {
    flexDirection: 'row-reverse',
  },
  location: {
    fontSize: 16,
    marginLeft: 5,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    marginLeft: 10,
  },
  verifiedText: {
    color: '#fff',
    marginLeft: 5,
    fontSize: 12,
    fontWeight: 'bold',
  },

  // Bio section
  bioContainer: {
    marginBottom: 10,
  },
  bio: {
    fontSize: 16,
    lineHeight: 24,
    overflow: 'hidden',
  },
  readMoreButton: {
    alignSelf: 'flex-start',
    marginTop: 8,
    paddingVertical: 5,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderRadius: 15,
  },
  readMoreText: {
    fontSize: 14,
    fontWeight: '500',
  },

  // Section styles
  section: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    marginBottom: 15,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },

  // Details grid
  detailsGrid: {
    flexDirection: 'column',
    paddingTop: 5,
  },

  // Detail item styles
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    padding: 16,
    borderRadius: 16,
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    borderWidth: 0.5,
  },
  detailItemRTL: {
    flexDirection: 'row-reverse',
  },
  detailIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    elevation: 3,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  detailTextContainer: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 17,
    fontWeight: '600',
  },

  // Text RTL support
  textRTL: {
    textAlign: 'right',
  },

  // Action buttons
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
    gap: 15,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default ViewProfile;