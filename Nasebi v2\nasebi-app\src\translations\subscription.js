/**
 * Subscription translations without the "subscription." prefix
 * This file contains the same translations as in ar.js and en.js but without the prefix
 */

export const enSubscription = {
  subscribeNow: 'Subscribe Now',
  subtitle: 'Unlock premium features to enhance your experience',
  monthlyTitle: 'Monthly',
  quarterlyTitle: 'Quarterly',
  yearlyTitle: 'Yearly',
  month: 'month',
  quarter: 'quarter',
  year: 'year',
  mostPopular: 'Most Popular',
  feature1: 'Create profile',
  feature2: 'Browse matches',
  feature3: 'Send likes',
  feature4: 'Advanced filters',
  feature5: 'Priority in search results',
  subscribe: 'Subscribe Now',
  premium: 'Premium',
  activeSubscription: 'Active Subscription',
  validUntil: 'Valid until',
  autoRenewal: 'Auto-renewal is enabled',
  noAutoRenewal: 'Auto-renewal is disabled',
  cancelSubscription: 'Cancel Subscription',
  premiumBenefits: 'Premium Benefits',
  benefit1Title: 'See Who Likes You',
  benefit1Description: 'See who has liked your profile before you like them',
  benefit2Title: 'Unlimited Messages',
  benefit2Description: 'Send unlimited messages to your matches',
  benefit3Title: 'Advanced Filters',
  benefit3Description: 'Use advanced filters to find your perfect match',
  securePayment: 'Secure payment processing',
  cancelTitle: 'Cancel Subscription',
  cancelConfirm: 'Are you sure you want to cancel your subscription?',
  cancelSuccess: 'Your subscription has been cancelled successfully',
  cancelError: 'There was an error cancelling your subscription',
  successMessage: 'Your subscription has been activated successfully',
  processingError: 'There was an error processing your subscription',
  initError: 'There was an error initializing the payment',
  paymentError: 'There was an error processing your payment',
  planNotFound: 'The selected subscription plan was not found',
  fetchError: 'There was an error fetching subscription plans',
  demoMode: 'Demo Mode',
  demoMessage: 'The payment endpoint is not available. For demonstration purposes, your subscription will be activated without payment processing.',
  demoSuccessMessage: 'Your subscription has been activated for demonstration purposes.',
  cancelled: 'Cancelled',
  cancelledSubscription: 'Subscription Cancelled',
  subscriptionCancelledInfo: 'Your subscription has been cancelled. You can resubscribe at any time.',
  resubscribe: 'Resubscribe',
};

export const arSubscription = {
  subscribeNow: 'اشترك الآن',
  subtitle: 'احصل على ميزات متقدمة لتحسين تجربتك',
  monthlyTitle: 'شهري',
  quarterlyTitle: 'ربع سنوي',
  yearlyTitle: 'سنوي',
  month: 'شهر',
  quarter: 'ربع سنة',
  year: 'سنة',
  mostPopular: 'الأكثر شعبية',
  feature1: 'إنشاء ملف شخصي',
  feature2: 'تصفح المطابقات',
  feature3: 'إرسال الإعجابات',
  feature4: 'فلاتر متقدمة',
  feature5: 'أولوية في نتائج البحث',
  subscribe: 'اشترك الآن',
  premium: 'مميز',
  activeSubscription: 'اشتراك نشط',
  validUntil: 'صالح حتى',
  autoRenewal: 'التجديد التلقائي مفعل',
  noAutoRenewal: 'التجديد التلقائي غير مفعل',
  cancelSubscription: 'إلغاء الاشتراك',
  premiumBenefits: 'مزايا الاشتراك المميز',
  benefit1Title: 'رؤية من أعجب بك',
  benefit1Description: 'معرفة من أعجب بملفك الشخصي قبل أن تعجب بهم',
  benefit2Title: 'رسائل غير محدودة',
  benefit2Description: 'إرسال رسائل غير محدودة لمطابقاتك',
  benefit3Title: 'فلاتر متقدمة',
  benefit3Description: 'استخدام فلاتر متقدمة للعثور على شريك حياتك المثالي',
  securePayment: 'معالجة دفع آمنة',
  cancelTitle: 'إلغاء الاشتراك',
  cancelConfirm: 'هل أنت متأكد من رغبتك في إلغاء اشتراكك؟',
  cancelSuccess: 'تم إلغاء اشتراكك بنجاح',
  cancelError: 'حدث خطأ أثناء إلغاء اشتراكك',
  successMessage: 'تم تفعيل اشتراكك بنجاح',
  processingError: 'حدث خطأ أثناء معالجة اشتراكك',
  initError: 'حدث خطأ أثناء تهيئة الدفع',
  paymentError: 'حدث خطأ أثناء معالجة الدفع',
  planNotFound: 'لم يتم العثور على خطة الاشتراك المحددة',
  fetchError: 'حدث خطأ أثناء جلب خطط الاشتراك',
  demoMode: 'وضع العرض التوضيحي',
  demoMessage: 'نقطة نهاية الدفع غير متوفرة. لأغراض العرض التوضيحي، سيتم تفعيل اشتراكك بدون معالجة الدفع.',
  demoSuccessMessage: 'تم تفعيل اشتراكك لأغراض العرض التوضيحي.',
  cancelled: 'ملغي',
  cancelledSubscription: 'تم إلغاء الاشتراك',
  subscriptionCancelledInfo: 'تم إلغاء اشتراكك. يمكنك إعادة الاشتراك في أي وقت.',
  resubscribe: 'إعادة الاشتراك',
};
