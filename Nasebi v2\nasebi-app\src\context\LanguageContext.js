import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { I18nManager, Alert, Platform } from 'react-native';
import { useTranslation } from 'react-i18next';
import * as Updates from 'expo-updates';

const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const { i18n } = useTranslation();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    loadSavedLanguage();
  }, []);

  const loadSavedLanguage = async () => {
    try {
      console.log('Loading saved language...');
      const savedLanguage = await AsyncStorage.getItem('user-language');
      console.log('Saved language from AsyncStorage:', savedLanguage);
      if (savedLanguage) {
        console.log('Using saved language:', savedLanguage);
        await changeLanguage(savedLanguage, false);
      } else {
        // If no saved language, set Arabic as default
        console.log('No saved language found, defaulting to Arabic');
        await changeLanguage('ar', false);
      }
      setIsReady(true);
    } catch (error) {
      console.error('Error loading language:', error);
      // In case of error, still try to set Arabic as default
      console.log('Error occurred, defaulting to Arabic');
      await changeLanguage('ar', false);
      setIsReady(true);
    }
  };

  const changeLanguage = async (language, showReloadPrompt = true) => {
    try {
      console.log('Changing language to:', language);
      await AsyncStorage.setItem('user-language', language);
      await i18n.changeLanguage(language);
      console.log('Language changed, i18n.language is now:', i18n.language);

      // Handle RTL layout
      const isRTL = language === 'ar';
      console.log('Is RTL:', isRTL, 'Current I18nManager.isRTL:', I18nManager.isRTL);
      
      // Check if RTL setting needs to change
      if (I18nManager.isRTL !== isRTL) {
        console.log('Updating RTL settings...');
        I18nManager.allowRTL(isRTL);
        I18nManager.forceRTL(isRTL);
        console.log('RTL settings updated, I18nManager.isRTL is now:', I18nManager.isRTL);
        
        // Show reload prompt if this is a user-initiated language change
        if (showReloadPrompt && Platform.OS !== 'web') {
          Alert.alert(
            language === 'ar' ? 'تحديث التطبيق' : 'App Reload Required',
            language === 'ar' 
              ? 'يجب إعادة تشغيل التطبيق لتطبيق التغييرات. هل تريد إعادة التشغيل الآن؟'
              : 'The app needs to reload to apply the language change. Reload now?',
            [
              {
                text: language === 'ar' ? 'إلغاء' : 'Cancel',
                style: 'cancel',
              },
              {
                text: language === 'ar' ? 'إعادة التشغيل' : 'Reload',
                onPress: async () => {
                  try {
                    // For Expo
                    if (Updates.reloadAsync) {
                      await Updates.reloadAsync();
                    }
                  } catch (error) {
                    console.error('Error reloading app:', error);
                  }
                },
              },
            ],
            { cancelable: false }
          );
        }
      }
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  const value = {
    language: i18n.language,
    changeLanguage,
    isRTL: i18n.language === 'ar',
    isReady,
  };

  // Only render children once language is ready
  if (!isReady) {
    return null; // Or return a loading spinner
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};