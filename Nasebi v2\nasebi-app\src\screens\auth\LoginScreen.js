import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import config from '../../config/config';
import { useRTL } from '../../hooks/useRTL';


const LoginScreen = ({ navigation }) => {
  const { login, isAuthenticated } = useAuth();
  const { colors } = useTheme();
  const { t } = useTranslationFlat();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [feedbackMessage, setFeedbackMessage] = useState('');

  useEffect(() => {
    // We're not using mock data or test credentials anymore
    // Users need to enter their real credentials
    console.log('Login screen loaded - using real authentication only');
  }, []);

  // Effect to handle automatic redirection after authentication
  useEffect(() => {
    // If user becomes authenticated while on this screen, handle it
    if (isAuthenticated) {
      console.log('User is authenticated, redirecting to Main app');
      // No need for explicit navigation here,
      // AppNavigator will automatically switch from Auth to Main stack
    }
  }, [isAuthenticated]);

  const handleLogin = async () => {
    if (!email || !password) {
      setError(t('auth.emailPasswordRequired'));
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(t('auth.invalidEmailFormat'));
      return;
    }

    setLoading(true);
    setError('');
    setFeedbackMessage(t('auth.loggingIn'));

    try {
      console.log('Attempting login for:', email);

      const result = await login(email, password);

      if (!result.success) {
        console.log('Login failed:', result.error);
        setError(result.error || t('auth.loginFailed'));

        // Provide helpful feedback for specific error types
        if (result.error && result.error.includes('Network Error')) {
          setFeedbackMessage(t('auth.checkConnection'));
        }
      } else {
        console.log('Login successful');
        setFeedbackMessage(t('auth.loginSuccessful'));

        // The redirection will be handled by the useEffect above when isAuthenticated changes
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(t('auth.unexpectedError'));
    } finally {
      setLoading(false);
    }
  };

  // No development helpers - using real authentication only

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            <Text style={[styles.title, { color: colors.text }]}>
              {t('auth.login')}
            </Text>

            {/* No dev mode indicators - using real authentication only */}

            {feedbackMessage ? (
              <Text style={[styles.feedbackText, { color: colors.primary }]}>
                {feedbackMessage}
              </Text>
            ) : null}

            <View style={styles.form}>
              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('auth.email')}
                </Text>
                <TextInput
                  style={[styles.input, { color: colors.text }]}
                  placeholder={t('auth.enterEmail')}
                  placeholderTextColor={colors.subtext}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t('auth.password')}
                </Text>
                <TextInput
                  style={[styles.input, { color: colors.text }]}
                  placeholder={t('auth.enterPassword')}
                  placeholderTextColor={colors.subtext}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                />
              </View>

              {error ? <Text style={styles.errorText}>{error}</Text> : null}

              <TouchableOpacity
                style={styles.forgotPassword}
                onPress={() => navigation.navigate('ForgotPassword')}
              >
                <Text style={[styles.forgotPasswordText, { color: colors.primary }]}>
                  {t('auth.forgotPassword')}
                </Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[
                styles.loginButton,
                { backgroundColor: colors.primary },
                loading && styles.disabledButton
              ]}
              onPress={handleLogin}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.loginButtonText}>
                  {t('auth.login')}
                </Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.registerLink}
              onPress={() => navigation.navigate('Onboarding')}
            >
              <Text style={[styles.registerLinkText, { color: colors.primary }]}>
                {t('auth.noAccount')} {t('auth.register')}
              </Text>
            </TouchableOpacity>

            {/* No development helpers - using real authentication only */}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 40,
    textAlign: 'center',
  },
  form: {
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  forgotPassword: {
    alignItems: 'flex-end',
    marginTop: 10,
  },
  forgotPasswordText: {
    fontSize: 14,
  },
  loginButton: {
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 20,
  },
  disabledButton: {
    opacity: 0.7,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  registerLink: {
    alignItems: 'center',
  },
  registerLinkText: {
    fontSize: 16,
  },
  errorText: {
    color: 'red',
    marginTop: 10,
    marginBottom: 10,
    textAlign: 'center',
  },
  feedbackText: {
    textAlign: 'center',
    marginTop: 5,
    marginBottom: 15,
    fontSize: 16,
  },
  devHelper: {
    marginTop: 20,
    padding: 10,
    alignSelf: 'center',
  },
  devHelperText: {
    fontSize: 12,
    opacity: 0.7,
  },
  devModeIndicator: {
    backgroundColor: '#FFF3CD',
    padding: 8,
    borderRadius: 5,
    marginBottom: 15,
    alignSelf: 'center',
  },
  devModeText: {
    color: '#856404',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default LoginScreen;