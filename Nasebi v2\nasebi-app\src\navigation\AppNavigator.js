import React from 'react';
import { Platform, View, ActivityIndicator, Text, StyleSheet, Image, TouchableOpacity, I18nManager } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useTranslationFlat, useRTL } from '../hooks';
import { useLanguage, useAuth, useTheme, useNotifications } from '../context';
import { SearchFiltersProvider } from '../context/SearchFiltersContext';

// Import components from barrel file
import { TabIcon, BackButton, NotificationIcon, FilterIcon } from '../components';

// Auth Screens
import {
  OnboardingScreen,
  LoginScreen,
  RegisterScreen,
  ForgotPasswordScreen
} from '../screens/auth';

// Main Screens
import HomeScreen from '../screens/main/HomeScreen';
import MatchesScreen from '../screens/main/MatchesScreen';
import MessagesScreen from '../screens/main/MessagesScreen';
import ProfileScreen from '../screens/main/ProfileScreen';
import EditProfile from '../screens/main/EditProfile';
import PreferencesScreen from '../screens/main/PreferencesScreen';
import ViewProfile from '../screens/main/ViewProfile';
import SubscriptionScreen from '../screens/main/SubscriptionScreen';
import SettingsScreen from '../screens/main/SettingsScreen';
import NotificationsScreen from '../screens/main/NotificationsScreen';
import PrivacyScreen from '../screens/main/PrivacyScreen';
import AboutScreen from '../screens/main/AboutScreen';
import ChangePasswordScreen from '../screens/main/ChangePasswordScreen';

// Chat Screens
import ChatScreen from '../screens/chat/ChatScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  const { t } = useTranslationFlat();
  const { isRTL } = useLanguage(); // Language direction
  const { colors } = useTheme(); // Only destructure colors, not theme
  const { notifications, unreadCount, handleNotificationPress } = useNotifications();
  const rtl = useRTL(); // RTL utilities

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused }) => {
          let iconSource;

          switch (route.name) {
            case 'Home':
              iconSource = require('../../assets/icons/logo.png');
              break;
            case 'Matches':
              iconSource = require('../../assets/icons/love-face.png');
              break;
            case 'Messages':
              iconSource = require('../../assets/icons/messagenotify.png');
              break;
            case 'Profile':
              iconSource = require('../../assets/icons/Heart.png');
              break;
          }

          return <TabIcon focused={focused} icon={iconSource} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.subtext,
        headerShown: true,
        tabBarStyle: {
          direction: isRTL ? 'rtl' : 'ltr',
          backgroundColor: colors.background,
          borderTopColor: colors.border,
          borderTopWidth: 0.5,
          height: 70,
          paddingTop: 10,
          paddingBottom: 10,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: -3 },
          shadowOpacity: 0.2,
          shadowRadius: 6,
          elevation: 8,
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          borderTopLeftRadius: isRTL ? 0 : 20,
          borderTopRightRadius: isRTL ? 20 : 0,
          marginHorizontal: 0,
          borderColor: colors.border,
          borderWidth: 0.5,
        },
        tabBarLabelStyle: {
          fontFamily: 'Poppins',
          fontSize: 12,
          fontWeight: '600',
          paddingBottom: 5,
          marginTop: 3,
          letterSpacing: 0.2,
          textTransform: 'capitalize',
        },
        tabBarItemStyle: {
          paddingVertical: 8,
          height: 60,
          marginHorizontal: 4,
        },
        headerStyle: {
          backgroundColor: colors.background,
          elevation: 2,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
          borderBottomWidth: 0.5,
          borderBottomColor: colors.border,
          height: 65,
        },
        headerTitleStyle: {
          color: colors.text,
          fontFamily: 'Poppins',
          fontSize: 18,
          fontWeight: '600',
          letterSpacing: 0.3,
          textTransform: 'capitalize',
        },
        headerTitleAlign: 'center',
        headerLeftContainerStyle: {
          paddingLeft: isRTL ? 0 : 10,
          paddingRight: isRTL ? 10 : 0,
          height: 50,
          justifyContent: 'center',
        },
        headerRightContainerStyle: {
          paddingRight: isRTL ? 0 : 10,
          paddingLeft: isRTL ? 10 : 0,
          height: 50,
          justifyContent: 'center',
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarLabel: t('discover'),
          headerTitle: () => (
            <Image
              source={require('../../assets/icons/name.png')}
              style={{ width: 80, resizeMode: 'contain' }}
            />
          ),
          headerRight: () => (
            <View style={styles.headerIcon}>
              {isRTL ? (
                <FilterIcon />
              ) : (
                <NotificationIcon
                  count={unreadCount}
                  notifications={notifications}
                  onPressNotification={handleNotificationPress}
                />
              )}
            </View>
          ),
          headerLeft: () => (
            <View style={styles.headerIcon}>
              {isRTL ? (
                <NotificationIcon
                  count={unreadCount}
                  notifications={notifications}
                  onPressNotification={handleNotificationPress}
                />
              ) : (
                <FilterIcon />
              )}
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Matches"
        component={MatchesScreen}
        options={{ tabBarLabel: t('matches') }}
      />
      <Tab.Screen
        name="Messages"
        component={MessagesScreen}
        options={{ tabBarLabel: t('messages') }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ tabBarLabel: t('profile') }}
      />
    </Tab.Navigator>
  );
};

const AuthStack = () => {
  const { t } = useTranslationFlat();
  const { isRTL } = useLanguage();
  const { colors } = useTheme();
  const rtl = useRTL(); // RTL utilities

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        headerStyle: {
          backgroundColor: colors.background,
          elevation: 2,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
          borderBottomWidth: 0.5,
          borderBottomColor: colors.border,
          height: 60,
        },
        headerLeftContainerStyle: {
          paddingLeft: isRTL ? 0 : 10,
          paddingRight: isRTL ? 10 : 0,
        },
        headerRightContainerStyle: {
          paddingRight: isRTL ? 0 : 10,
          paddingLeft: isRTL ? 10 : 0,
        },
        headerTintColor: colors.primary,
        headerTitleStyle: {
          color: colors.text,
          fontFamily: 'Poppins',
          fontSize: 18,
          fontWeight: '600',
          letterSpacing: 0.3,
          textTransform: 'capitalize',
        },
        cardStyle: {
          backgroundColor: colors.background
        },
        headerBackTitleVisible: false,
      }}
    >
      <Stack.Screen name="Onboarding" component={OnboardingScreen} />
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{ title: t('login') }}
      />
      <Stack.Screen
        name="Register"
        component={RegisterScreen}
        options={{ title: t('register') }}
      />
      <Stack.Screen
        name="ForgotPassword"
        component={ForgotPasswordScreen}
        options={{ title: t('forgotPassword') }}
      />
    </Stack.Navigator>
  );
};

const MainStack = () => {
  const { t } = useTranslationFlat();
  const { isRTL } = useLanguage();
  const { colors } = useTheme();
  const rtl = useRTL();

  return (
    <Stack.Navigator
      screenOptions={{
        headerTitleAlign: 'center',
        headerStyle: {
          backgroundColor: colors.background,
          elevation: 2,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
          borderBottomWidth: 0.5,
          borderBottomColor: colors.border,
          height: 60,
        },
        headerTintColor: colors.primary,
        headerTitleStyle: {
          fontSize: 18,
          fontFamily: 'Poppins',
          fontWeight: '600',
          color: colors.text,
          letterSpacing: 0.3,
          textTransform: 'capitalize',
        },
        cardStyle: {
          backgroundColor: colors.background
        },
        headerBackTitleVisible: false,
        headerLeftContainerStyle: {
          paddingLeft: isRTL ? 0 : 10,
          paddingRight: isRTL ? 10 : 0,
        },
        headerRightContainerStyle: {
          paddingRight: isRTL ? 0 : 10,
          paddingLeft: isRTL ? 10 : 0,
        },
        // Use custom back button
        headerBackImage: () => <BackButton />,
      }}
    >
      <Stack.Screen
        name="TabNavigator"
        component={TabNavigator}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Chat"
        component={ChatScreen}
        options={({ route }) => ({
          title: route.params.name,
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        })}
      />
      <Stack.Screen
        name="EditProfile"
        component={EditProfile}
        options={{
          title: t('editProfile'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
      <Stack.Screen
        name="Preferences"
        component={PreferencesScreen}
        options={{
          title: t('preferences'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
      <Stack.Screen
        name="ViewProfile"
        component={ViewProfile}
        options={{
          title: t('viewProfile'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
      <Stack.Screen
        name="Subscription"
        component={SubscriptionScreen}
        options={{
          title: t('premiumMembership'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: t('settings'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: t('notifications'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
      <Stack.Screen
        name="Privacy"
        component={PrivacyScreen}
        options={{
          title: t('privacy'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          title: t('about'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
      <Stack.Screen
        name="ChangePassword"
        component={ChangePasswordScreen}
        options={{
          title: t('changePassword'),
          headerBackTitleVisible: false,
          headerLayoutPreset: isRTL ? 'center' : 'left',
        }}
      />
    </Stack.Navigator>
  );
};

// Make sure to export the AppNavigator as default
export default function AppNavigator() {
  const { isAuthenticated, loading } = useAuth();
  const { colors } = useTheme();

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <SearchFiltersProvider>
      {isAuthenticated ? <MainStack /> : <AuthStack />}
    </SearchFiltersProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  headerIcons: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  headerIcon: {
    marginHorizontal: 12,
    padding: 8,
    borderRadius: 20,
    overflow: 'hidden',
    marginTop: 2,
    marginBottom: 2,
  }
});
