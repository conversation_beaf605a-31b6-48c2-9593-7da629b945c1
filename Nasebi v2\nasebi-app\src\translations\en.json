{"common": {"appName": "<PERSON><PERSON><PERSON>", "welcome": "Welcome to Nasebi", "findMatch": "Find your perfect match", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "yes": "Yes", "no": "No", "block": "Block", "report": "Report", "km": "km", "cm": "cm", "ok": "OK", "edit": "Edit", "delete": "Delete", "done": "Done", "search": "Search", "continue": "Continue", "english": "English", "englishShort": "EN", "arabic": "عربي", "depends": "Depends", "justNow": "Just now", "minutesAgo": "{{count}} mins ago", "hoursAgo": "{{count}} hours ago", "yesterday": "Yesterday", "daysAgo": "{{count}} days ago", "weeksAgo": "{{count}} weeks ago", "monthsAgo": "{{count}} months ago", "yearsAgo": "{{count}} years ago", "common_yes": "Yes", "common_no": "No", "unknown": "Unknown"}, "editProfile": "Edit Profile", "viewProfile": "View Profile", "preferences": "Preferences", "settings": {"title": "Settings", "account": "Account", "preferences": "Preferences", "info": "Information", "darkMode": "Dark Mode", "language": "Language", "matchPreferences": "Match Preferences", "subscription": "Membership", "about": "About App", "help": "Help", "privacy": "Privacy", "changePassword": "Change Password", "logoutTitle": "Logout", "logoutConfirm": "Are you sure you want to logout?", "hideOnlineStatus": "Hide Online Status", "appPermissions": "App Permissions", "allowLocationAccess": "Allow Location Access", "allowCameraAccess": "Allow Camera Access", "allowContactsAccess": "Allow Contacts Access", "advancedPrivacy": "Advanced Privacy", "incognitoMode": "Incognito Mode", "privacyPolicy": "Privacy Policy", "deleteAccount": "Delete Account", "privacyDisclaimer": "We respect your privacy and are committed to protecting your personal data in accordance with our Privacy Policy.", "visibilityTitle": "Visibility Settings"}, "notifications": {"title": "Notifications", "noNotifications": "No notifications yet", "markAllAsRead": "Mark all as read", "deleteAll": "Delete all", "newMatch": "New Match", "newLike": "New Like", "newMessage": "New Message", "newVisit": "Profile Viewed", "justNow": "Just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago"}, "privacy": "Privacy", "about": {"version": "Version", "description": "Find your perfect match with <PERSON><PERSON><PERSON>, the leading matrimonial app for Muslims.", "info": "Information", "followUs": "Follow Us", "technical": "Technical Information", "appId": "App ID", "deviceInfo": "Device Info", "website": "Website", "contactUs": "Contact Us", "termsOfService": "Terms of Service", "viewTerms": "View Terms", "privacyPolicy": "Privacy Policy", "viewPrivacy": "View Privacy", "rateApp": "Rate this App", "allRightsReserved": "All Rights Reserved", "developerInfo": "Developed by Nasebi Team"}, "changePassword": "Change Password", "premiumMembership": "Premium Membership", "discover": "Discover", "matches": {"title": "Matches", "matchesTab": "Matches", "likesTab": "<PERSON>s", "newMatches": "New Matches", "messages": "Messages", "seeAll": "See All", "justNow": "Just now", "hoursAgo": "h ago", "daysAgo": "d ago", "noMessages": "No messages yet", "moreAboutMe": "More About Me", "emptyMatchesTitle": "No matches yet", "emptyMatchesDesc": "Start swiping to find your matches!", "emptyLikesTitle": "No likes yet", "emptyLikesDesc": "When someone likes you, they'll appear here.", "emptyVisitedTitle": "No Visited Profiles", "emptyVisitedDesc": "You haven't visited any profiles yet. Start swiping to discover people.", "emptyLikedTitle": "No Liked Profiles", "emptyLikedDesc": "You haven't liked any profiles yet. Start swiping to find people you like.", "emptyProfilesTitle": "No Profiles", "emptyProfilesDesc": "No profiles to display. Start swiping to discover people.", "visited": "Visited", "youLiked": "You liked", "startSwiping": "Start Swiping", "likeHint": "They liked you! Start chatting now.", "noMatchesFound": "No matches found", "noMoreProfiles": "No more profiles to show right now", "tryAgainLater": "Please try again later", "search": "Search matches", "noMatches": "No matches yet. Keep swiping!", "startConversation": "Start a conversation", "preferences": "Preferences", "preferencesUpdated": "Preferences updated successfully", "preferencesTitle": "Matching Preferences", "ageRange": "Age Range", "heightRange": "Height Range", "maxDistance": "Maximum Distance", "locations": "Preferred Locations", "nationalities": "Preferred Nationalities", "ethnicities": "Preferred Ethnicities", "education": "Education Levels", "occupations": "Occupations", "acceptChildren": "Accept Partners with Children", "religiousPractices": "Religious Practices", "hijabStatus": "Hijab Status", "dealBreakers": "Deal Breakers"}, "messages": {"title": "Messages", "search": "Search messages", "noMessages": "No messages yet", "typeMessage": "Type a message...", "send": "Send", "tabLabel": "Messages", "sendImage": "Send Image", "shareLocation": "Share Location", "messageRead": "Read", "messageDelivered": "Delivered", "justNow": "Just now", "minuteAgo": "min ago", "minutesAgo": "mins ago", "hourAgo": "hour ago", "hoursAgo": "hours ago", "yesterday": "Yesterday", "dayAgo": "day ago", "daysAgo": "days ago", "searchPlaceholder": "Search conversations", "noMessagesTitle": "No messages yet", "noMessagesDesc": "When you match with someone, you can start a conversation here.", "viewMatches": "View Matches", "you": "You"}, "profile": {"edit": "Edit", "photos": "Photos", "addPhoto": "Add Photo", "basicInfo": "Basic Information", "name": "Full Name", "age": "Age", "location": "Location", "bio": "Bio", "occupation": "Occupation", "education": {"less_than_highschool": "Less than high school", "highschool": "High school", "college_degree": "College degree", "masters": "Master's degree", "doctorate": "Doctorate"}, "religiousInfo": "Religious Information", "religiousLevel": "Religious Level", "prayerLevel": "Prayer Level", "personalStatus": "Personal Status", "maritalStatus": {"single": "Single", "divorced": "Divorced", "widowed": "Widowed", "married": "Married"}, "lookingFor": "Looking For", "ageRange": "Age Range", "heightRange": "Height Range", "maxDistance": "Maximum Distance", "locationPreferences": "Location Preferences", "preferredLocations": "Preferred Locations", "enterPreferredLocations": "Enter preferred cities/countries", "preferredNationalities": "Preferred Nationalities", "enterPreferredNationalities": "Enter preferred nationalities", "preferredEthnicities": "Preferred Ethnicities", "enterPreferredEthnicities": "Enter preferred ethnicities", "preferredSkinColors": "Preferred Skin Colors", "enterPreferredSkinColors": "Enter preferred skin colors", "professionalPreferences": "Professional Preferences", "educationLevels": "Education Levels", "enterPreferredEducationLevels": "Enter preferred education levels", "occupations": "Occupations", "enterPreferredOccupations": "Enter preferred occupations", "enterPreferredMaritalStatus": "Enter preferred marital status", "acceptPartnersWithChildren": "Accept Partners with Children", "acceptSmokers": "Accept Smokers", "religiousPreferences": "Religious Preferences", "enterPreferredReligiousLevel": "Enter preferred religious level", "enterPreferredPrayerLevel": "Enter preferred prayer level", "religiousPractices": "Religious Practices", "enterPreferredPractices": "Enter preferred practices", "preferredLanguages": "Preferred Languages", "enterPreferredLanguages": "Enter preferred languages", "dealBreakers": "Deal Breakers", "enterDealBreakers": "Enter your deal breakers", "settings": "Settings", "hidePhotos": "Hide Photos", "notifications": "Notifications", "premium": "Premium Member", "active": "Active", "inactive": "Inactive", "logout": "Logout", "saveChanges": "Save Changes", "cancel": "Cancel", "editProfile": "Edit Profile", "tabLabel": "Profile", "viewProfile": "View Profile", "about": "About", "professional": "Professional", "religious": "Religious Background", "familyValues": "Family Values", "height": "Height", "nationality": "Nationality", "ethnicity": "Ethnicity", "company": "Company", "islamicEducation": "Islamic Education", "hasChildren": "Has Children", "wantsChildren": "Plans for Children", "blockUser": "Block User", "reportUser": "Report User", "blockConfirm": "Are you sure you want to block this user?", "reportReasons": {"inappropriateContent": "Inappropriate Content", "harassment": "Harassment", "fakeProfile": "Fake Profile", "other": "Other"}, "becomePremium": "Become Premium", "editModal": {"title": "Edit Profile", "name": "Name", "bio": "Bio", "email": "Email", "phone": "Phone", "profileImage": "Profile Image", "changeImage": "Change Image", "save": "Save Changes", "cancel": "Cancel"}, "hideMe": "<PERSON>de Me", "profileComplete": "Profile Complete", "profileIncomplete": "Profile Incomplete", "emailNotVerified": "Email verification pending", "updateSuccess": "Profile updated successfully", "updateError": "Failed to update profile", "loadError": "Failed to load profile data", "logoutTitle": "Logout", "logoutConfirm": "Are you sure you want to logout?", "notAuthenticated": "Please log in to view your profile", "bioPlaceholder": "Write something about yourself...", "partnerDescriptionPlaceholder": "Describe your ideal partner...", "namePlaceholder": "Enter your full name", "agePlaceholder": "Enter your age", "birthDate": "Date of Birth", "selectBirthDate": "Select your date of birth", "heightPlaceholder": "Enter your height in cm", "weightPlaceholder": "Enter your weight in kg", "nationalityPlaceholder": "Enter your nationality", "locationPlaceholder": "Enter your city, country", "specificLocationPlaceholder": "Enter neighborhood, area, or address", "religiousLevelPlaceholder": "Describe your religious level", "languagesPlaceholder": "Languages you prefer for chatting", "vipMembership": "VIP Membership", "vipDescription": "Upgrade to VIP now and get unlimited messages and exclusive features!", "subscribe": "Subscribe Now", "aboutMe": "About Me", "partnerDescription": "Ideal Partner Description", "educationAndWork": "Education & Work", "marriagePreferences": "Marriage Preferences", "basicSection": "Basic", "religiousSection": "Religious", "educationSection": "Education", "marriageSection": "Marriage", "bioSection": "Bio", "aboutYou": "About You", "weight": "Weight", "gender": {"male": "Male", "female": "Female", "Male": "Male", "Female": "Female"}, "countryOfResidence": "Country of Residence", "skinColor": {"very_fair": "Very Fair", "fair": "Fair", "medium": "Medium", "tan": "<PERSON>", "dark": "Dark", "very_dark": "Very Dark"}, "tribalAffiliation": "Tribal Affiliation", "city": "City", "specificLocation": "Specific Location", "selectCountry": "Select Country", "selectCountryOfResidence": "Select Country of Residence", "selectCity": "Select City", "selectCountryFirst": "Select Country of Residence First", "selectSkinColor": "Select Skin Color", "religiousSect": {"sunni": "Sunni", "shia": "Shia", "other": "Other"}, "prayerFrequency": "Prayer Frequency", "fastingFrequency": "Fasting Frequency", "hajjStatus": "Hajj Status", "educationLevel": "Education Level", "jobLevel": "Job Level", "incomeLevel": "Income Level", "chatLanguages": "Chat Languages", "marriageReadiness": {"immediately": "Ready immediately", "within_year": "Within a year", "after_two_years": "After 1-2 years", "not_decided": "Not decided yet"}, "preferredResidence": "Residence After Marriage", "allowsWifeToWork": "Wife's Work Status", "healthStatus": "Health Status", "smoking": {"yes": "Yes", "sometimes": "Sometimes", "no": "No"}, "prayer": {"daily": "Daily", "weekly": "Weekly", "sometimes": "Sometimes", "religious_occasions": "Religious occasions only", "never": "Never"}, "fasting": {"always": "Always", "sometimes": "Sometimes", "never": "Never", "prefer_not_to_say": "Prefer not to say"}, "hajj": {"completed": "Completed Hajj", "planning_soon": "Planning soon", "planning_future": "Planning in future", "not_planned": "Not planned"}, "job": {"student": "Student", "employee": "Employee", "senior_employee": "Senior Employee", "manager": "Manager", "unemployed": "Unemployed", "prefer_not_to_say": "Prefer not to say"}, "income": {"no_income": "No income", "low": "Low", "average": "Average", "high": "High"}, "residence": {"own_home": "Own home", "family_home": "Family home", "family_home_temporarily": "Family home temporarily", "undecided": "Undecided"}, "children": {"soon": "As soon as possible", "after_two_years": "After 2+ years", "depends": "Depends on circumstances", "no": "Don't want children"}, "work": {"yes": "Yes", "yes_from_home": "Yes, but from home", "depends": "Depends on circumstances", "no": "No"}, "health": {"good_health": "Good health", "special_needs": "Special needs", "chronic_disease": "Chronic disease", "infertile": "Infertile"}}, "skip": "<PERSON><PERSON>", "completeProfile": "Complete Your Profile", "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "name": "Full Name", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account? <PERSON>gin", "dontHaveAccount": "Don't have an account? Register", "iAmA": "I am a:", "male": "Male", "female": "Female", "preferredLanguage": "Preferred Language:", "english": "English", "arabic": "العربية", "continue": "Continue", "noAccount": "Don't have an account?", "hasAccount": "Already have an account?", "signUp": "Sign Up", "signIn": "Sign In", "resetPassword": "Reset Password", "resetPasswordInstructions": "Enter your email address and we'll send you instructions to reset your password.", "resetPasswordSent": "We've sent instructions to reset your password to your email.", "redirectingToLogin": "Redirecting to login...", "emailPasswordRequired": "Email and password are required", "fillAllFields": "Please fill in all fields", "invalidEmailFormat": "Please enter a valid email address", "passwordsDoNotMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters long", "loggingIn": "Logging in...", "loginSuccessful": "Login successful!", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "registrationFailed": "Registration failed. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again.", "creatingAccount": "Creating your account...", "accountCreatedSuccessfully": "Account created successfully!", "tryingAlternateLogin": "Trying alternative login...", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "enterFullName": "Enter your full name", "createPassword": "Create a password", "confirmYourPassword": "Confirm your password", "emailRequired": "Email is required", "resetPasswordError": "Failed to reset password. Please try again.", "success": "Success!", "redirecting": "Redirecting to the app...", "devModeActive": "Development mode active", "offlineModeActive": "Offline mode active"}, "home": {"noMoreProfiles": "No more profiles available at the moment", "refresh": "Find more profiles", "tabLabel": "Discover", "compatibility": "{{percentage}}% Compatible", "like": "LIKE", "nope": "NOPE", "loading": "Loading profiles...", "swipeLeftToDislike": "Swipe left to dislike", "swipeRightToLike": "Swipe right to like", "superLikeAlert": "You have used a Super Like!", "superLikeMessage": "A 'Hi' message will be sent to this profile", "tapToReveal": "Tap to view (3s)", "errorFetchingProfiles": "Error fetching profiles. Please try again."}, "superLike": {"messageSent": "Hi message sent!", "hiMessageWillBeSent": "A \"Hi\" message will be sent to this profile"}, "defaults": {"location": "Egypt", "bio": "I prefer to talk in person, but I'm from the east with part of my life in Cairo. Currently not working.", "religious": "Muslim", "education": "College degree", "height": "121 cm", "weight": "90 kg", "prayer": "Daily", "employment": "Unemployed", "fasting": "Always", "skinTone": "Light wheat", "marriageType": "Traditional", "wifeAfterMarriage": "Depends on circumstances", "workAfterMarriage": "Depends on circumstances", "financialContribution": "Shared but not obligatory", "premaritalItems": "As agreed", "relocationAfterMarriage": "If close to me", "healthStatus": "Good health", "hajjStatus": "Planning in the future, God willing", "fastingStatus": "Always", "partnerDescription": "A real man who can keep the family together, doesn't smoke, doesn't travel abroad permanently, normal housing not family house"}, "filter": {"title": "Filter", "apply": "Apply", "reset": "Reset", "ageRange": "Age Range", "distance": "Distance", "height": "Height", "weight": "Weight", "hasPhoto": "Has Photo", "online": "Online Now", "verified": "Verified", "education": "Education", "religion": "Religion", "maritalStatus": "Marital Status", "children": "Children", "smoking": "Smoking", "drinking": "Drinking", "lookingFor": "Looking For", "interests": "Interests", "showMe": "Show Me", "men": "Men", "women": "Women", "both": "Both", "ageRangeLabel": "Age Range: {{min}} - {{max}}", "distanceLabel": "Distance: Up to {{distance}}km", "heightLabel": "Height: {{min}}cm - {{max}}cm", "weightLabel": "Weight: {{min}}kg - {{max}}kg", "from": "From", "to": "To", "any": "Any", "noPreference": "No Preference", "saveFilters": "Save Filters", "resetFilters": "Reset Filters", "applyFilters": "Apply Filters", "filterResults": "Filter Results", "clearAll": "Clear All", "showResults": "Show Results", "resultsCount": "{{count}} Results", "noResults": "No Results", "tryDifferentFilters": "Try different filters", "advancedSearch": "Advanced Search", "basicInformation": "Basic Information", "age": "Age", "heightInCm": "Height (cm)", "weightInKg": "Weight (kg)", "hasProfilePhoto": "Has Profile Photo", "nationality": "Nationality", "enterNationality": "Enter nationality", "ethnicity": "Ethnicity", "enterEthnicity": "Enter ethnicity", "skinColor": "Skin Color", "chooseSkinColor": "Choose skin color", "educationAndCareer": "Education and Career", "educationLevel": "Education Level", "chooseEducationLevel": "Choose education level", "jobLevel": "Job Level", "chooseJobLevel": "Choose job level", "incomeLevel": "Income Level", "chooseIncomeLevel": "Choose income level", "religiousAttributes": "Religious Attributes", "religiousLevel": "Religious Level", "chooseReligiousLevel": "Choose religious level", "prayerLevel": "Prayer Level", "choosePrayerLevel": "Choose prayer level", "fastingLevel": "Fasting Level", "chooseFastingLevel": "Choose fasting level", "lifestyle": "Lifestyle", "chooseMaritalStatus": "Choose marital status", "chooseWantsChildren": "<PERSON><PERSON> wants children", "chooseHajjStatus": "Choose Hajj status", "preferTribalAffiliation": "Do you prefer tribal affiliation?", "chooseHealthStatus": "Choose health status", "chooseWifeWorkStatus": "Choose wife work status", "chooseResidence": "<PERSON><PERSON> preferred residence", "chooseMarriageReadiness": "Choose marriage readiness"}, "subscription": {"title": "Premium Membership", "subtitle": "Unlock premium features to enhance your experience", "monthlyTitle": "Basic Plan", "quarterlyTitle": "Standard Plan", "yearlyTitle": "Premium Plan", "monthlyShort": "mo", "quarterlyShort": "qtr", "yearlyShort": "yr", "feature1": "View who liked your profile", "feature2": "Unlimited messages", "feature3": "View all profile photos", "feature4": "Priority in search results", "feature5": "Advanced filtering options", "subscribe": "Subscribe Now", "subscribeNow": "Subscribe Now", "premium": "Premium", "mostPopular": "Most Popular", "premiumBenefits": "Premium Benefits", "benefit1Title": "See Who Likes You", "benefit1Description": "See who has liked your profile before you decide to like them back.", "benefit2Title": "Unlimited Messaging", "benefit2Description": "Connect with more potential matches with unlimited messaging.", "benefit3Title": "Priority Visibility", "benefit3Description": "Get shown to more people and appear at the top of search results.", "activeSubscription": "Active Subscription", "validUntil": "Valid until", "autoRenewal": "Auto-renewal is enabled", "noAutoRenewal": "Auto-renewal is disabled", "cancelSubscription": "Cancel Subscription", "cancelTitle": "Cancel Subscription", "cancelConfirm": "Are you sure you want to cancel your subscription? You'll still have access until your current subscription period ends.", "cancelSuccess": "Your subscription has been cancelled. You still have access until the end of your current billing period.", "cancelError": "There was an error cancelling your subscription. Please try again later.", "purchaseSuccess": "Your subscription was successful! You now have access to premium features.", "purchaseError": "There was an error processing your payment. Please try again later.", "paymentInitError": "There was an error initializing payment. Please try again later.", "paymentError": "There was an error processing your payment. Please try again.", "errorFetchingPlans": "Error fetching subscription plans. Please try again later.", "securePayment": "Secure payment processed by Stripe", "fetchError": "Error fetching subscription plans. Please try again later.", "initError": "There was an error initializing payment. Please try again later.", "successMessage": "Your subscription was successful! You now have access to premium features.", "processingError": "There was an error processing your payment. Please try again later.", "month": "month", "quarter": "quarter", "year": "year"}, "password": {"changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "passwordTooShort": "Password must be at least {{length}} characters long", "passwordsDoNotMatch": "New passwords do not match", "passwordChangedSuccess": "Password changed successfully!", "changePasswordFailed": "Failed to change password. Please try again."}, "education_less_than_highschool": "Less than high school", "education_highschool": "High school", "education_college_degree": "College degree", "education_bachelors": "Bachelor's degree", "education_masters": "Master's degree", "education_doctorate": "Doctorate", "education_religious_education": "Religious education", "religious_very_religious": "Very religious", "religious_religious": "Religious", "religious_somewhat_religious": "Somewhat religious", "religious_not_religious": "Not religious", "prayer_daily": "Daily", "prayer_weekly": "Weekly", "prayer_sometimes": "Sometimes", "prayer_religious_occasions": "Religious occasions only", "prayer_rarely": "Rarely", "prayer_never": "Never", "fasting_always": "Always", "fasting_sometimes": "Sometimes", "fasting_never": "Never", "fasting_prefer_not_to_say": "Prefer not to say", "hajj_completed": "Completed Hajj", "hajj_planning_soon": "Planning soon", "hajj_planning_future": "Planning in future", "hajj_not_planned": "Not planned", "maritalStatus_single": "Single", "maritalStatus_divorced": "Divorced", "maritalStatus_widowed": "Widowed", "maritalStatus_married": "Married", "marriageReadiness_immediately": "Ready immediately", "marriageReadiness_within_year": "Within a year", "marriageReadiness_after_two_years": "After 1-2 years", "marriageReadiness_not_decided": "Not decided yet", "residence_own_home": "Own home", "residence_family_home": "Family home", "residence_family_home_temporarily": "Family home temporarily", "residence_undecided": "Undecided", "children_soon": "As soon as possible", "children_after_two_years": "After 2+ years", "children_depends": "Depends on circumstances", "children_no": "Don't want children", "work_yes": "Yes", "work_yes_from_home": "Yes, but from home", "work_depends": "Depends on circumstances", "work_no": "No", "health_good_health": "Good health", "health_special_needs": "Special needs", "health_chronic_disease": "Chronic disease", "health_infertile": "Infertile", "smoking_yes": "Yes", "smoking_sometimes": "Sometimes", "smoking_no": "No", "job_student": "Student", "job_employee": "Employee", "job_senior_employee": "Senior Employee", "job_manager": "Manager", "job_unemployed": "Unemployed", "job_prefer_not_to_say": "Prefer not to say", "income_no_income": "No income", "income_low": "Low", "income_average": "Average", "income_high": "High", "religiousSect_sunni": "Sunni", "religiousSect_shia": "Shia", "religiousSect_other": "Other", "gender_male": "Male", "gender_female": "Female", "selectGender": "Select gender", "language_arabic": "Arabic", "language_english": "English", "language_french": "French", "language_spanish": "Spanish", "language_turkish": "Turkish", "language_urdu": "Urdu", "language_hindi": "Hindi", "language_persian": "Persian", "skinColor_very_fair": "Very Fair", "skinColor_fair": "Fair", "skinColor_medium": "Medium", "skinColor_tan": "<PERSON>", "skinColor_dark": "Dark", "skinColor_very_dark": "Very Dark"}