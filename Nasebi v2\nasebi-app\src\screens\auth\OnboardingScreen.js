import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  Platform,
  Animated,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslationFlat } from '../../hooks/useTranslationFlat';
import { useTheme } from '../../context/ThemeContext';
import i18n from '../../translations/i18n';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { useRTL } from '../../hooks/useRTL';


const { width, height } = Dimensions.get('window');

const OnboardingScreen = ({ navigation }) => {
  const rtl = useRTL();

  const { t } = useTranslationFlat();
  const { colors, theme } = useTheme();
  const [selectedGender, setSelectedGender] = useState(null);
  const [selectedLanguage, setSelectedLanguage] = useState('ar');
  const [logoAnim] = useState(new Animated.Value(0));
  const [contentAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Set language to Arabic when component mounts
    handleLanguageChange('ar');

    // Run animations when component mounts
    Animated.sequence([
      Animated.timing(logoAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(contentAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleLanguageChange = async (lang) => {
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setSelectedLanguage(lang);
    await i18n.changeLanguage(lang);
  };

  const handleGenderSelect = (gender) => {
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    // Convert "Male" and "Female" to "male" and "female" for database compatibility
    const dbGender = gender === 'Male' ? 'male' : gender === 'Female' ? 'female' : gender;
    setSelectedGender(dbGender);
  };

  const handleContinue = () => {
    if (selectedGender) {
      if (Platform.OS === 'ios') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      navigation.navigate('Register', { gender: selectedGender, language: selectedLanguage });
    }
  };

  // Animation styles
  const logoAnimStyle = {
    opacity: logoAnim,
    transform: [
      {
        translateY: logoAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [-50, 0],
        }),
      },
    ],
  };

  const contentAnimStyle = {
    opacity: contentAnim,
    transform: [
      {
        translateY: contentAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [50, 0],
        }),
      },
    ],
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar
        barStyle={theme === 'dark' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />

      <Animated.View style={[styles.header, logoAnimStyle]}>
        <Image
          source={require('../../../assets/icon.png')}
          style={styles.logo}
          resizeMode="contain"
        />
        <Text style={[styles.title, { color: colors.primary }]}>
          {t('appName')}
        </Text>
        <Text style={[styles.subtitle, { color: colors.subtext }]}>
          {t('findMatch')}
        </Text>
      </Animated.View>

      <Animated.View style={[styles.content, contentAnimStyle]}>
        <View style={styles.genderSelection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('auth.registerAs')}
          </Text>
          <View style={styles.genderButtons}>
            <TouchableOpacity
              style={[
                styles.genderButton,
                { borderColor: colors.border },
                selectedGender === 'male' && styles.selectedButton,
                selectedGender === 'male' && { backgroundColor: colors.primary },
              ]}
              onPress={() => handleGenderSelect('Male')}
            >
              <Image
                source={require('../../../assets/icons/Heart.png')}
                style={[
                  styles.icon,
                  selectedGender === 'male' && styles.selectedIcon,
                ]}
              />
              <Text style={[
                styles.genderButtonText,
                { color: colors.text },
                selectedGender === 'male' && styles.selectedButtonText,
                selectedGender === 'male' && { color: '#fff' },
              ]}>
                {t('auth.male')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.genderButton,
                { borderColor: colors.border },
                selectedGender === 'female' && styles.selectedButton,
                selectedGender === 'female' && { backgroundColor: colors.primary },
              ]}
              onPress={() => handleGenderSelect('Female')}
            >
              <Image
                source={require('../../../assets/icons/love-face.png')}
                style={[
                  styles.icon,
                  selectedGender === 'female' && styles.selectedIcon,
                ]}
              />
              <Text style={[
                styles.genderButtonText,
                { color: colors.text },
                selectedGender === 'female' && styles.selectedButtonText,
                selectedGender === 'female' && { color: '#fff' },
              ]}>
                {t('auth.female')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.languageSelection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('auth.preferredLanguage')}
          </Text>
          <View style={styles.languageButtons}>
            <TouchableOpacity
              style={[
                styles.languageButton,
                { borderColor: colors.border },
                selectedLanguage === 'en' && styles.selectedButton,
                selectedLanguage === 'en' && { backgroundColor: colors.primary },
              ]}
              onPress={() => handleLanguageChange('en')}
            >
              <Image
                source={require('../../../assets/icons/Location.png')}
                style={[
                  styles.icon,
                  selectedLanguage === 'en' && styles.selectedIcon,
                ]}
              />
              <Text style={[
                styles.languageButtonText,
                { color: colors.text },
                selectedLanguage === 'en' && styles.selectedButtonText,
                selectedLanguage === 'en' && { color: '#fff' },
              ]}>
                {t('english')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.languageButton,
                { borderColor: colors.border },
                selectedLanguage === 'ar' && styles.selectedButton,
                selectedLanguage === 'ar' && { backgroundColor: colors.primary },
              ]}
              onPress={() => handleLanguageChange('ar')}
            >
              <Image
                source={require('../../../assets/icons/Location.png')}
                style={[
                  styles.icon,
                  selectedLanguage === 'ar' && styles.selectedIcon,
                ]}
              />
              <Text style={[
                styles.languageButtonText,
                { color: colors.text },
                selectedLanguage === 'ar' && styles.selectedButtonText,
                selectedLanguage === 'ar' && { color: '#fff' },
              ]}>
                {t('arabic')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>

      <TouchableOpacity
        activeOpacity={0.8}
        style={[
          styles.continueButton,
          !selectedGender && styles.disabledButton,
          selectedGender && {
            shadowOpacity: 0.3,
            shadowRadius: 10,
            elevation: 5,
          }
        ]}
        onPress={handleContinue}
        disabled={!selectedGender}
      >
        <LinearGradient
          colors={selectedGender ? ['#93060d', '#ef9ac5'] : ['#CCCCCC', '#AAAAAA']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        >
          <Text style={styles.continueButtonText}>
            {t('continue')}
          </Text>
        </LinearGradient>
      </TouchableOpacity>

      <Image
        source={require('../../../assets/icons/Heart.png')}
        style={styles.backgroundImage}
        resizeMode="contain"

      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: height * 0.05,
    marginBottom: height * 0.06,
  },
  logo: {
    width: width * 0.3,
    height: width * 0.3,
    marginBottom: 15,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  content: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins',
    marginBottom: 20,
    textAlign: 'center',
  },
  genderSelection: {
    marginBottom: height * 0.04,
  },
  genderButtons: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
  },
  genderButton: {
    flex: 1,
    marginHorizontal: 10,
    paddingVertical: 20,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  selectedButton: {
    borderColor: 'transparent',
  },
  genderButtonText: {
    fontSize: 16,
    fontFamily: 'Roboto',
    marginTop: 8,
  },
  selectedButtonText: {
    fontFamily: 'Roboto-Bold',
  },
  languageSelection: {
    marginBottom: height * 0.04,
  },
  languageButtons: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
  },
  languageButton: {
    flex: 1,
    marginHorizontal: 10,
    paddingVertical: 20,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  icon: {
    width: 30,
    height: 30,
    tintColor: '#93060d',
  },
  selectedIcon: {
    tintColor: '#FFFFFF',
  },
  continueButton: {
    borderRadius: 30,
    overflow: 'hidden',
    marginBottom: 20,
  },
  gradient: {
    paddingVertical: 16,
    alignItems: 'center',
    borderRadius: 30,
  },
  disabledButton: {
    opacity: 0.7,
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins-Bold',
  },
  backgroundImage: {
    position: 'absolute',
    bottom: -50,
    [rtl.isRTL ? 'left' : 'right']: -100, // Use RTL-aware positioning
    width: width * 0.8,
    height: height * 0.4,
    opacity: 0.1,
    zIndex: -1,
  }
});

export default OnboardingScreen;