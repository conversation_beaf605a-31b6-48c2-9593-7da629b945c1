import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  Image,
  I18nManager,
} from 'react-native';
import { useStripe } from '@stripe/stripe-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome5, Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import api from '../../services/api';
import { formatTextByLanguage } from '../../utils/textUtils';
import useCustomTranslation from '../../hooks/useCustomTranslation';


const SubscriptionScreen = ({ navigation }) => {
  const { t, i18n } = useCustomTranslation();
  const { colors, theme } = useTheme();
  const { user, updateUser } = useAuth();
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const [loading, setLoading] = useState(false);
  // Initialize with null, will be set when user selects a plan
  const [selectedPlan, setSelectedPlan] = useState(null);
  // Store the selected plan object for use throughout the payment process
  const [selectedPlanObject, setSelectedPlanObject] = useState(null);

  // Reset selected plan when the component mounts or when subscription plans change
  useEffect(() => {
    setSelectedPlan(null);
    setSelectedPlanObject(null);
    console.log('Reset selected plan and plan object');
  }, [subscriptionPlans]);
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [userSubscription, setUserSubscription] = useState(null);

  useEffect(() => {
    fetchSubscriptionPlans();
    fetchUserSubscription();
  }, []);

  // Add a navigation listener to refresh data when the screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      console.log('SubscriptionScreen focused, refreshing data...');
      fetchUserSubscription();
    });

    return unsubscribe;
  }, [navigation]);

  // Debug log for subscription plans
  useEffect(() => {
    console.log('Current subscription plans:', subscriptionPlans);
  }, [subscriptionPlans]);

  // Debug log for user subscription
  useEffect(() => {
    console.log('Current user subscription:', userSubscription);
  }, [userSubscription]);

  // Debug log for selected plan
  useEffect(() => {
    console.log('Selected plan:', selectedPlan);
  }, [selectedPlan]);

  const fetchSubscriptionPlans = async () => {
    try {
      setLoading(true);
      console.log('Fetching subscription plans from database...');

      // Use the api service to fetch subscription plans directly from the database
      const response = await api.get('/api/subscriptions/plans');
      console.log('Subscription plans fetched successfully:', response.data);

      if (response.data && Array.isArray(response.data)) {
        // Process the data to ensure it has the correct structure
        const processedPlans = response.data.map(plan => ({
          id: plan.id,
          name: plan.name || '',
          description: plan.description || '',
          price: parseFloat(plan.price) || 0,
          durationMonths: parseInt(plan.duration_months) || 1,
          features: plan.features ?
            (typeof plan.features === 'string' ?
              plan.features.split(',').map(f => f.trim()) :
              Array.isArray(plan.features) ? plan.features : []
            ) : []
        }));

        console.log('Processed subscription plans:', processedPlans);
        setSubscriptionPlans(processedPlans);
      } else {
        console.warn('No subscription plans found or invalid data format');
        setSubscriptionPlans([]);
      }
    } catch (error) {
      console.error('Error fetching subscription plans:', error);

      // Set empty array instead of mock data
      setSubscriptionPlans([]);

      Alert.alert(
        t('error'),
        t('subscription.fetchError')
      );
    } finally {
      setLoading(false);
    }
  };

  const fetchUserSubscription = async () => {
    if (!user) return;

    try {
      console.log('Fetching user subscription from database...');

      // Use the api service to fetch user subscription directly from the database
      const response = await api.get(`/api/subscriptions/user/${user.id}`);
      console.log('User subscription fetched successfully:', response.data);

      if (response.data && response.data.subscription) {
        setUserSubscription(response.data.subscription);
      } else {
        console.log('No active subscription found for user');
        setUserSubscription(null);
      }
    } catch (error) {
      console.error('Error fetching user subscription:', error);
      // Set to null instead of using mock data
      setUserSubscription(null);
    }
  };

  const handleSubscribe = async () => {
    if (!user) {
      navigation.navigate('Auth');
      return;
    }

    try {
      setLoading(true);
      console.log('Initiating subscription process...');

      // Extract the actual plan ID from the selected plan string
      // The format is "plan-{id}-{index}"
      let selectedPlanId = null;
      if (selectedPlan) {
        const match = selectedPlan.match(/plan-(\d+)-\d+/);
        if (match && match[1]) {
          selectedPlanId = parseInt(match[1]);
        }
      }

      console.log('Extracted plan ID from selection:', selectedPlanId);

      // Find the selected plan object directly by ID
      let selectedPlanObj = selectedPlanObject; // Use the stored plan object if available

      // If we don't have a stored plan object, try to find it by ID
      if (!selectedPlanObj) {
        selectedPlanObj = subscriptionPlans.find(plan => plan.id === selectedPlanId);
        console.log('Found plan object by ID:', selectedPlanObj);
      } else {
        console.log('Using stored plan object:', selectedPlanObj);
      }

      const planId = selectedPlanObj?.id;

      if (!planId) {
        console.error('Plan not found for selection:', selectedPlan, 'Available plans:', subscriptionPlans);
        Alert.alert(
          t('error'),
          t('subscription.planNotFound')
        );
        setLoading(false);
        return;
      }

      console.log('Selected plan ID:', planId);

      // Try to use the api service to create a subscription

      // Variables to store payment data
      let paymentIntent, ephemeralKey, customer, publishableKey;

      try {
        console.log('Calling API to create subscription with planId:', planId);
        const response = await api.post(
          '/api/payments/create-subscription',
          {
            planId,
            userId: user.id,
          }
        );

        console.log('Subscription creation response:', response.data);

        // We no longer support simulated payments - all payments go through Stripe
        if (response.data.simulated) {
          console.log('Received simulated payment data, but simulated payments are no longer supported');

          // Show an error message to the user
          Alert.alert(
            t('error') || 'Error',
            t('subscription.realPaymentsOnly') || 'This app now requires real payment processing. Please try again.',
            [{ text: t('common.ok') || 'OK' }]
          );

          // Exit the function early
          setLoading(false);
          return;
        }

        // Extract the payment data from the response
        paymentIntent = response.data.paymentIntent;
        ephemeralKey = response.data.ephemeralKey;
        customer = response.data.customer;
        publishableKey = response.data.publishableKey;

        // Validate that we have all the required data
        if (!paymentIntent || !ephemeralKey || !customer) {
          console.error('Missing required payment data from API:', response.data);
          Alert.alert(
            t('error') || 'Error',
            t('subscription.missingPaymentData') || 'Missing required payment data from the server. Please try again later.',
            [{ text: t('common.ok') || 'OK' }]
          );
          setLoading(false);
          return;
        }

        console.log('Received payment data from API:', {
          paymentIntentReceived: !!paymentIntent,
          ephemeralKeyReceived: !!ephemeralKey,
          customerReceived: !!customer,
          publishableKeyReceived: !!publishableKey
        });

      } catch (apiError) {
        console.error('API Error creating subscription:', apiError);

        // Log detailed error information
        if (apiError.response) {
          console.log('API Error Details:');
          console.log('- Request:', apiError.config?.url);
          console.log('- Error Message:', apiError.message);
          console.log('- Status Code:', apiError.response.status);
          console.log('- Response Data:', JSON.stringify(apiError.response.data));
        }

        // Show detailed error to the user
        const errorMessage = apiError.response?.data?.error ||
                            apiError.response?.data?.message ||
                            apiError.message ||
                            'There was an error creating your subscription.';

        Alert.alert(
          t('error') || 'Error',
          errorMessage,
          [{ text: t('common.ok') || 'OK' }]
        );

        // Exit the function early
        setLoading(false);
        return;
      }

      // Initialize the Payment Sheet
      console.log('Initializing payment sheet with Stripe data');
      let initError;
      try {
        // Log the payment data for debugging
        console.log('Payment data for Stripe:', {
          paymentIntent: paymentIntent ? paymentIntent.substring(0, 10) + '...' : 'missing',
          ephemeralKey: ephemeralKey ? ephemeralKey.substring(0, 10) + '...' : 'missing',
          customer: customer ? customer.substring(0, 10) + '...' : 'missing'
        });

        // Check if we have a publishable key from the backend
        if (publishableKey) {
          console.log('Using publishable key from backend:', publishableKey.substring(0, 10) + '...');
        } else {
          console.log('No publishable key received from backend, using default from config');
        }

        // Initialize the payment sheet with minimal required parameters
        const result = await initPaymentSheet({
          paymentIntentClientSecret: paymentIntent,
          merchantDisplayName: 'Nasebi',
          style: theme === 'dark' ? 'dark' : 'light',
          // Only include these if they're available
          ...(ephemeralKey ? { customerEphemeralKeySecret: ephemeralKey } : {}),
          ...(customer ? { customerId: customer } : {}),
        });
        initError = result.error;
      } catch (error) {
        console.error('Exception during payment sheet initialization:', error);
        initError = {
          code: 'Exception',
          message: error.message || 'An unexpected error occurred'
        };
      }

      if (initError) {
        console.error('Error initializing payment sheet:', initError);
        Alert.alert(
          t('error') || 'Error',
          t('subscription.initError') || 'There was an error initializing the payment process. Please try again later.',
          [{ text: t('common.ok') || 'OK' }]
        );
        setLoading(false);
        return;
      }

      console.log('Payment sheet initialized successfully, presenting payment sheet');

      // Present the Payment Sheet
      let presentError;
      try {
        console.log('Presenting payment sheet to user');
        const result = await presentPaymentSheet();
        console.log('Payment sheet result:', result);
        presentError = result.error;

        // If there's no error, payment was successful
        if (!result.error) {
          console.log('Payment successful!');
        }
      } catch (error) {
        console.error('Exception during payment sheet presentation:', error);
        presentError = {
          code: 'Exception',
          message: error.message || 'An unexpected error occurred'
        };
      }

      if (presentError) {
        setLoading(false);

        if (presentError.code === 'Canceled') {
          console.log('User canceled the payment');
          // User canceled the payment - no need to show an error
          return;
        }

        console.error('Error presenting payment sheet:', presentError);
        Alert.alert(
          t('error') || 'Error',
          t('subscription.paymentError') || 'There was an error processing your payment. Please try again.',
          [{ text: t('common.ok') || 'OK' }]
        );
        return;
      }

      console.log('Payment successful, updating subscription status');
      console.log('Selected plan object at payment success:', selectedPlanObj);

      // Payment successful, update subscription
      try {
        console.log('Payment successful, updating subscription in database');

        // Call the API to confirm the subscription was created
        try {
          const confirmResponse = await api.post('/api/payments/confirm-subscription', {
            userId: user.id,
            planId: planId
          });

          console.log('Subscription confirmation response:', confirmResponse.data);
        } catch (confirmError) {
          console.error('Error confirming subscription (continuing anyway):', confirmError);
        }

        // Refresh subscription data from the server
        await fetchUserSubscription();

        // Get the selected plan object again to ensure we have the latest data
        let planObj = selectedPlanObject;
        if (!planObj) {
          // Try to find the plan by ID
          planObj = subscriptionPlans.find(plan => plan.id === planId);
        }

        // Get the duration months from the selected plan
        const durationMonths = planObj?.durationMonths || planObj?.duration_months || 1;
        console.log('Using duration months for subscription end date:', durationMonths, 'from plan:', planObj);

        // Update user context
        updateUser({
          isPremium: true,
          subscriptionPlan: planObj?.name || 'Premium',
          subscriptionEndDate: new Date(Date.now() + durationMonths * 30 * 24 * 60 * 60 * 1000)
        });

        // Show success message
        Alert.alert(
          t('success') || 'Success',
          t('subscription.successMessage') || 'Your subscription has been activated successfully!',
          [
            {
              text: t('common.ok') || 'OK',
              onPress: () => {
                navigation.navigate('Profile');
              },
            },
          ]
        );
      } catch (error) {
        console.error('Error updating subscription status after payment:', error);

        // Even if there's an error updating the local status, the payment was successful
        // So we still show a success message but with a note
        Alert.alert(
          t('success') || 'Success',
          t('subscription.partialSuccessMessage') || 'Your payment was successful, but there was an issue updating your subscription status. Please restart the app to see your updated subscription.',
          [
            {
              text: t('common.ok') || 'OK',
              onPress: () => {
                navigation.navigate('Profile');
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error('Subscription error:', error);
      Alert.alert(
        t('error'),
        t('subscription.processingError')
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!user || !userSubscription) return;

    // Check if this is a default subscription (has no ID)
    if (userSubscription.isDefault || !userSubscription.id) {
      console.log('This is a default subscription, cannot cancel');
      Alert.alert(
        t('error') || 'Error',
        t('subscription.cannotCancelDefault') || 'You cannot cancel the default subscription.'
      );
      return;
    }

    Alert.alert(
      t('subscription.cancelTitle'),
      t('subscription.cancelConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.confirm'),
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              console.log('Attempting to cancel subscription...');

              // Use the API service to cancel the subscription
              try {
                console.log('Cancelling subscription with ID:', userSubscription.id);

                // Verify that we have a valid subscription ID
                if (!userSubscription.id) {
                  throw new Error('Invalid subscription ID');
                }

                // Prepare the request data
                const requestData = {
                  subscriptionId: userSubscription.id,
                  userId: user.id
                };

                console.log('Sending cancellation request with data:', requestData);

                // Make the API call to cancel the subscription
                const response = await api.post(
                  '/api/subscriptions/cancel',
                  requestData
                );

                console.log('Subscription cancellation response:', response.data);

                // Real subscription cancelled successfully, refresh from the database
                console.log('Subscription cancelled successfully, refreshing data');
                await fetchUserSubscription();

                // Update the user context
                updateUser({
                  isPremium: false,
                  subscriptionPlan: null,
                  subscriptionEndDate: null
                });

                // Show success message
                Alert.alert(
                  t('success') || 'Success',
                  t('subscription.cancelSuccess') || 'Your subscription has been cancelled successfully.',
                  [
                    {
                      text: t('common.ok') || 'OK',
                      onPress: () => {
                        // Force refresh the screen
                        setUserSubscription(null);
                      }
                    }
                  ]
                );
              } catch (apiError) {
                console.error('Error cancelling subscription:', apiError);

                // Log detailed error information for debugging
                if (apiError.response) {
                  console.log('API Error Details:');
                  console.log('- Status:', apiError.response.status);
                  console.log('- Data:', JSON.stringify(apiError.response.data));
                }

                // Show error message to the user
                Alert.alert(
                  t('error') || 'Error',
                  t('subscription.cancelError') || 'There was an error cancelling your subscription. Please try again later.'
                );
              }

            } catch (error) {
              console.error('Error cancelling subscription:', error);
              Alert.alert(
                t('error'),
                t('subscription.cancelError')
              );
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const renderPlanCard = (title, price, duration, features, isPopular = false, key = null) => {
    // Create a unique identifier for this plan
    const planId = key || `plan-${duration}`;

    // Check if this specific plan is selected
    const isSelected = selectedPlan === planId;
    const currentLanguage = i18n.language;
    const isRTL = I18nManager.isRTL;

    // Log the selection status for debugging
    console.log(`Rendering plan ${planId}, isSelected: ${isSelected}, selectedPlan: ${selectedPlan}`);

    return (
      <TouchableOpacity
        key={planId}
        style={[
          styles.planCard,
          { backgroundColor: colors.card, borderColor: colors.border },
          isSelected && {
            borderColor: colors.primary,
            borderWidth: 2,
            backgroundColor: theme === 'dark' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(76, 175, 80, 0.05)'
          },
          isPopular && styles.popularPlan,
        ]}
        onPress={() => {
          console.log('Plan selected:', planId, 'with duration:', duration);
          // Set the selected plan with the unique identifier
          setSelectedPlan(planId);

          // Find the plan object in the subscription plans
          if (planId.startsWith('plan-')) {
            const match = planId.match(/plan-(\d+)-\d+/);
            if (match && match[1]) {
              const planIdNum = parseInt(match[1]);
              const planObj = subscriptionPlans.find(p => p.id === planIdNum);
              if (planObj) {
                console.log('Found plan object for selection:', planObj);
                setSelectedPlanObject(planObj);
              }
            }
          }
        }}
        disabled={loading}
      >
        {isPopular && (
          <View style={[styles.popularBadge, { backgroundColor: colors.secondary }]}>
            <Text style={styles.popularText}>{t('subscription.mostPopular')}</Text>
          </View>
        )}

        <Text style={[styles.planTitle, { color: colors.text }]}>{title}</Text>
        <View style={styles.priceContainer}>
          <Text style={[styles.currencySymbol, { color: colors.text }]}>AED</Text>
          <Text style={[styles.price, { color: colors.text }]}>{price || '0.00'}</Text>
          <Text style={[styles.duration, { color: colors.subtext }]}>/{duration === 'monthly' ? t('subscription.month') : duration === 'quarterly' ? t('subscription.quarter') : t('subscription.year')}</Text>
        </View>

        <View style={styles.featuresContainer}>
          {features.map((feature, index) => {
            // Format the feature text based on language
            const formattedFeature = formatTextByLanguage(feature, currentLanguage, isRTL);

            return (
              <View key={`${key || duration}-feature-${index}`} style={styles.featureRow}>
                <Ionicons
                  name="checkmark-circle"
                  size={22}
                  color={colors.primary}
                  style={styles.featureIcon}
                />
                <Text style={[
                  styles.featureText,
                  {
                    color: colors.text,
                    textAlign: isRTL ? 'right' : 'left'
                  }
                ]}>
                  {formattedFeature}
                </Text>
              </View>
            );
          })}
        </View>

        {/* Only show the selected indicator if this plan is selected */}
        {isSelected && (
          <View style={[styles.selectedIndicator, { backgroundColor: colors.primary + '20' }]}>
            <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
            <Text style={{ color: colors.primary, fontSize: 12, marginLeft: 4, fontWeight: 'bold' }}>
              {t('subscription.selected') || 'Selected'}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (loading && !subscriptionPlans.length) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            {t('common.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Check if subscription is active or cancelled
  const isSubscriptionActive = userSubscription?.status === 'active';
  const isSubscriptionCancelled = userSubscription?.status === 'cancelled' || userSubscription?.isCancelled === true;
  const isDefaultSubscription = userSubscription?.isDefault === true;

  console.log('Current subscription status:', {
    active: isSubscriptionActive,
    cancelled: isSubscriptionCancelled,
    isDefault: isDefaultSubscription,
    subscription: userSubscription
  });

  // If it's a default subscription, we should show the subscription plans
  if (isDefaultSubscription) {
    console.log('This is a default subscription, showing subscription plans');
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Image
            source={require('../../../assets/icons/starts.png')}
            style={styles.headerIcon}
          />
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {t('subscription.subscribeNow')}
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.subtext }]}>
            {t('subscription.subtitle')}
          </Text>
        </View>

        {isSubscriptionActive && !isDefaultSubscription ? (
          <View style={[styles.activeSubscription, { backgroundColor: colors.card }]}>
            <LinearGradient
              colors={isSubscriptionCancelled ? ['#F44336', '#C62828'] : ['#4CAF50', '#2E7D32']}
              style={styles.subscriptionBadge}
            >
              <FontAwesome5
                name={isSubscriptionCancelled ? "times-circle" : "star"}
                size={22}
                color="#FFF"
              />
              <Text style={styles.subscriptionBadgeText}>
                {isSubscriptionCancelled
                  ? t('subscription.cancelled')
                  : t('subscription.premium')}
              </Text>
            </LinearGradient>

            <Text style={[styles.activeTitle, { color: colors.text }]}>
              {isSubscriptionCancelled
                ? t('subscription.cancelledSubscription')
                : t('subscription.activeSubscription')}
            </Text>

            {!isSubscriptionCancelled && userSubscription.end_date && (
              <Text style={[styles.activeDetails, { color: colors.subtext }]}>
                {t('subscription.validUntil')}: {new Date(userSubscription.end_date).toLocaleDateString()}
              </Text>
            )}

            {isSubscriptionCancelled ? (
              <Text style={[styles.renewalInfo, { color: colors.subtext }]}>
                {t('subscription.subscriptionCancelledInfo')}
              </Text>
            ) : (
              <Text style={[styles.renewalInfo, { color: colors.subtext }]}>
                {userSubscription.auto_renew
                  ? t('subscription.autoRenewal')
                  : t('subscription.noAutoRenewal')}
              </Text>
            )}

            {!isSubscriptionCancelled && (
              <TouchableOpacity
                style={[styles.cancelButton, { borderColor: colors.error }]}
                onPress={handleCancelSubscription}
              >
                <Text style={[styles.cancelButtonText, { color: colors.error }]}>
                  {t('subscription.cancelSubscription')}
                </Text>
              </TouchableOpacity>
            )}

            {isSubscriptionCancelled && (
              <TouchableOpacity
                style={[styles.resubscribeButton, { borderColor: colors.primary }]}
                onPress={() => {
                  // Reset the subscription status to show the subscription plans
                  setUserSubscription(null);
                }}
              >
                <Text style={[styles.resubscribeButtonText, { color: colors.primary }]}>
                  {t('subscription.resubscribe')}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : isSubscriptionCancelled ? (
          <View style={[styles.activeSubscription, { backgroundColor: colors.card }]}>
            <LinearGradient
              colors={['#F44336', '#C62828']}
              style={styles.subscriptionBadge}
            >
              <FontAwesome5
                name="times-circle"
                size={22}
                color="#FFF"
              />
              <Text style={styles.subscriptionBadgeText}>
                {t('subscription.cancelled')}
              </Text>
            </LinearGradient>

            <Text style={[styles.activeTitle, { color: colors.text }]}>
              {t('subscription.cancelledSubscription')}
            </Text>

            <Text style={[styles.renewalInfo, { color: colors.subtext }]}>
              {t('subscription.subscriptionCancelledInfo')}
            </Text>

            <TouchableOpacity
              style={[styles.resubscribeButton, { borderColor: colors.primary }]}
              onPress={() => {
                // Reset the subscription status to show the subscription plans
                setUserSubscription(null);
              }}
            >
              <Text style={[styles.resubscribeButtonText, { color: colors.primary }]}>
                {t('subscription.resubscribe')}
              </Text>
            </TouchableOpacity>
          </View>
        ) : isDefaultSubscription ? (
          <>
            <View style={[styles.activeSubscription, { backgroundColor: colors.card }]}>
              <LinearGradient
                colors={['#2196F3', '#0D47A1']}
                style={styles.subscriptionBadge}
              >
                <FontAwesome5
                  name="info-circle"
                  size={22}
                  color="#FFF"
                />
                <Text style={styles.subscriptionBadgeText}>
                  {t('subscription.free') || 'Free'}
                </Text>
              </LinearGradient>

              <Text style={[styles.activeTitle, { color: colors.text }]}>
                {t('subscription.freeSubscription') || 'Free Basic Plan'}
              </Text>

              <Text style={[styles.renewalInfo, { color: colors.subtext }]}>
                {t('subscription.freeSubscriptionInfo') || 'You are currently on the free basic plan. Upgrade to premium for more features.'}
              </Text>

              <TouchableOpacity
                style={[styles.upgradeButton, { borderColor: colors.primary, backgroundColor: colors.primary }]}
                onPress={() => {
                  // Reset the subscription status to show the subscription plans
                  setUserSubscription(null);
                }}
              >
                <Text style={[styles.upgradeButtonText, { color: '#FFF' }]}>
                  {t('subscription.upgradeNow') || 'Upgrade Now'}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.plansContainer}>
              {subscriptionPlans.length > 0 ? (
                subscriptionPlans.map((plan, index) => {
                  try {
                    // Determine the duration based on the plan's durationMonths
                    const durationMonths = typeof plan.durationMonths === 'number' ? plan.durationMonths :
                                          parseInt(plan.durationMonths || plan.duration_months || '1');

                    const duration = durationMonths === 1 ? 'monthly' :
                                    durationMonths === 3 ? 'quarterly' : 'yearly';

                    // Determine if this is the popular plan (usually the quarterly plan)
                    const isPopular = durationMonths === 3;

                    // Use the plan name as the title
                    const title = plan.name || (
                      duration === 'monthly' ? t('subscription.monthlyTitle') :
                      duration === 'quarterly' ? t('subscription.quarterlyTitle') :
                      t('subscription.yearlyTitle')
                    );

                    // Format the price - handle different data types safely
                    let priceValue = 0;
                    if (typeof plan.price === 'number') {
                      priceValue = plan.price;
                    } else if (typeof plan.price === 'string') {
                      priceValue = parseFloat(plan.price) || 0;
                    }
                    const price = priceValue.toFixed(2);

                    // Get features from the plan or use default features
                    let features = [];
                    if (plan.features) {
                      if (Array.isArray(plan.features)) {
                        features = plan.features;
                      } else if (typeof plan.features === 'string') {
                        features = plan.features.split(',').map(f => f.trim());
                      }
                    }

                    // If no features are available, use default ones
                    if (features.length === 0) {
                      features = [
                        t('subscription.feature1'),
                        t('subscription.feature2'),
                        t('subscription.feature3'),
                        ...(duration !== 'monthly' ? [t('subscription.feature4')] : []),
                        ...(duration === 'yearly' ? [t('subscription.feature5')] : [])
                      ];
                    }

                    return renderPlanCard(
                      title,
                      price,
                      duration,
                      features,
                      isPopular,
                      `plan-${plan.id || 'unknown'}-${index}`
                    );
                  } catch (error) {
                    console.error('Error rendering plan card:', error, plan);
                    return null; // Skip this plan if there's an error
                  }
                }).filter(Boolean) // Remove any null values from the array
              ) : (
                // Fallback to hardcoded plans if no plans are fetched
                <>
                  {renderPlanCard(
                    t('subscription.monthlyTitle'),
                    '9.99',
                    'monthly',
                    [
                      t('subscription.feature1'),
                      t('subscription.feature2'),
                      t('subscription.feature3'),
                    ]
                  )}

                  {renderPlanCard(
                    t('subscription.quarterlyTitle'),
                    '24.99',
                    'quarterly',
                    [
                      t('subscription.feature1'),
                      t('subscription.feature2'),
                      t('subscription.feature3'),
                      t('subscription.feature4'),
                    ],
                    true
                  )}

                  {renderPlanCard(
                    t('subscription.yearlyTitle'),
                    '79.99',
                    'yearly',
                    [
                      t('subscription.feature1'),
                      t('subscription.feature2'),
                      t('subscription.feature3'),
                      t('subscription.feature4'),
                      t('subscription.feature5'),
                    ]
                  )}
                </>
              )}
            </View>
          </>
        ) : (
          <>
            <View style={styles.plansContainer}>
              {subscriptionPlans.length > 0 ? (
                subscriptionPlans.map((plan, index) => {
                  try {
                    // Determine the duration based on the plan's durationMonths
                    const durationMonths = typeof plan.durationMonths === 'number' ? plan.durationMonths :
                                          parseInt(plan.durationMonths || plan.duration_months || '1');

                    const duration = durationMonths === 1 ? 'monthly' :
                                    durationMonths === 3 ? 'quarterly' : 'yearly';

                    // Determine if this is the popular plan (usually the quarterly plan)
                    const isPopular = durationMonths === 3;

                    // Use the plan name as the title
                    const title = plan.name || (
                      duration === 'monthly' ? t('subscription.monthlyTitle') :
                      duration === 'quarterly' ? t('subscription.quarterlyTitle') :
                      t('subscription.yearlyTitle')
                    );

                    // Format the price - handle different data types safely
                    let priceValue = 0;
                    if (typeof plan.price === 'number') {
                      priceValue = plan.price;
                    } else if (typeof plan.price === 'string') {
                      priceValue = parseFloat(plan.price) || 0;
                    }
                    const price = priceValue.toFixed(2);

                    // Get features from the plan or use default features
                    let features = [];
                    if (plan.features) {
                      if (Array.isArray(plan.features)) {
                        features = plan.features;
                      } else if (typeof plan.features === 'string') {
                        features = plan.features.split(',').map(f => f.trim());
                      }
                    }

                    // If no features are available, use default ones
                    if (features.length === 0) {
                      features = [
                        t('subscription.feature1'),
                        t('subscription.feature2'),
                        t('subscription.feature3'),
                        ...(duration !== 'monthly' ? [t('subscription.feature4')] : []),
                        ...(duration === 'yearly' ? [t('subscription.feature5')] : [])
                      ];
                    }

                    return renderPlanCard(
                      title,
                      price,
                      duration,
                      features,
                      isPopular,
                      `plan-${plan.id || 'unknown'}-${index}`
                    );
                  } catch (error) {
                    console.error('Error rendering plan card:', error, plan);
                    return null; // Skip this plan if there's an error
                  }
                }).filter(Boolean) // Remove any null values from the array
              ) : (
                // Fallback to hardcoded plans if no plans are fetched
                <>
                  {renderPlanCard(
                    t('subscription.monthlyTitle'),
                    '9.99',
                    'monthly',
                    [
                      t('subscription.feature1'),
                      t('subscription.feature2'),
                      t('subscription.feature3'),
                    ]
                  )}

                  {renderPlanCard(
                    t('subscription.quarterlyTitle'),
                    '24.99',
                    'quarterly',
                    [
                      t('subscription.feature1'),
                      t('subscription.feature2'),
                      t('subscription.feature3'),
                      t('subscription.feature4'),
                    ],
                    true
                  )}

                  {renderPlanCard(
                    t('subscription.yearlyTitle'),
                    '79.99',
                    'yearly',
                    [
                      t('subscription.feature1'),
                      t('subscription.feature2'),
                      t('subscription.feature3'),
                      t('subscription.feature4'),
                      t('subscription.feature5'),
                    ]
                  )}
                </>
              )}
            </View>

            <View style={styles.benefitsContainer}>
              <Text style={[styles.benefitsTitle, { color: colors.text }]}>
                {t('subscription.premiumBenefits')}
              </Text>

              <View style={styles.benefitRow}>
                <View style={[styles.benefitIcon, { backgroundColor: colors.primary }]}>
                  <Ionicons name="eye" size={24} color="#FFF" />
                </View>
                <View style={styles.benefitContent}>
                  <Text style={[styles.benefitTitle, { color: colors.text, textAlign: I18nManager.isRTL ? 'right' : 'left' }]}>
                    {t('subscription.benefit1Title')}
                  </Text>
                  <Text style={[styles.benefitDescription, { color: colors.subtext, textAlign: I18nManager.isRTL ? 'right' : 'left' }]}>
                    {formatTextByLanguage(t('subscription.benefit1Description'), i18n.language, I18nManager.isRTL)}
                  </Text>
                </View>
              </View>

              <View style={styles.benefitRow}>
                <View style={[styles.benefitIcon, { backgroundColor: colors.primary }]}>
                  <Ionicons name="chatbubble-ellipses" size={24} color="#FFF" />
                </View>
                <View style={styles.benefitContent}>
                  <Text style={[styles.benefitTitle, { color: colors.text, textAlign: I18nManager.isRTL ? 'right' : 'left' }]}>
                    {t('subscription.benefit2Title')}
                  </Text>
                  <Text style={[styles.benefitDescription, { color: colors.subtext, textAlign: I18nManager.isRTL ? 'right' : 'left' }]}>
                    {formatTextByLanguage(t('subscription.benefit2Description'), i18n.language, I18nManager.isRTL)}
                  </Text>
                </View>
              </View>

              <View style={styles.benefitRow}>
                <View style={[styles.benefitIcon, { backgroundColor: colors.primary }]}>
                  <Ionicons name="trending-up" size={24} color="#FFF" />
                </View>
                <View style={styles.benefitContent}>
                  <Text style={[styles.benefitTitle, { color: colors.text, textAlign: I18nManager.isRTL ? 'right' : 'left' }]}>
                    {t('subscription.benefit3Title')}
                  </Text>
                  <Text style={[styles.benefitDescription, { color: colors.subtext, textAlign: I18nManager.isRTL ? 'right' : 'left' }]}>
                    {formatTextByLanguage(t('subscription.benefit3Description'), i18n.language, I18nManager.isRTL)}
                  </Text>
                </View>
              </View>
            </View>

            {!selectedPlan && (
              <Text style={[styles.selectPlanMessage, { color: colors.subtext }]}>
                {t('subscription.selectPlanMessage') || 'Please select a subscription plan to continue'}
              </Text>
            )}

            <TouchableOpacity
              style={[
                styles.subscribeButton,
                { shadowColor: colors.primary },
                loading && { opacity: 0.7 },
                !selectedPlan && { opacity: 0.5 }
              ]}
              onPress={handleSubscribe}
              disabled={loading || !selectedPlan}
            >
              <LinearGradient
                colors={['#4CAF50', '#2E7D32']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.gradientButton}
              >
                {loading ? (
                  <ActivityIndicator color="#FFF" />
                ) : (
                  <>
                    <FontAwesome5 name="crown" size={20} color="#FFF" style={styles.buttonIcon} />
                    <Text style={styles.subscribeButtonText}>
                      {selectedPlan
                        ? `${t('subscription.subscribe')} (${
                            (() => {
                              // Extract the plan ID from the selected plan
                              const match = selectedPlan.match(/plan-(\d+)-\d+/);
                              if (match && match[1]) {
                                const planId = parseInt(match[1]);
                                // Find the plan by ID
                                const plan = subscriptionPlans.find(p => p.id === planId);
                                // Return the plan name if found
                                return plan ? plan.name : '';
                              }
                              return '';
                            })()
                          })`
                        : t('subscription.subscribe')
                      }
                    </Text>
                  </>
                )}
              </LinearGradient>
            </TouchableOpacity>
          </>
        )}

        <View style={styles.secureInfoContainer}>
          <Ionicons name="lock-closed" size={16} color={colors.subtext} style={styles.secureIcon} />
          <Text style={[styles.secureInfoText, { color: colors.subtext }]}>
            {t('subscription.securePayment')}
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  headerIcon: {
    width: 60,
    height: 60,
    marginBottom: 15,
  },
  headerTitle: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: 'Roboto',
    textAlign: 'center',
    marginHorizontal: 20,
  },
  plansContainer: {
    marginBottom: 30,
  },
  planCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 20,
    marginBottom: 15,
    position: 'relative',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  popularPlan: {
    borderWidth: 2,
    borderColor: '#FFC107',
  },
  popularBadge: {
    position: 'absolute',
    top: -12,
    end: 20,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 20,
  },
  popularText: {
    color: '#fff',
    fontSize: 12,
    fontFamily: 'Roboto-Bold',
  },
  planTitle: {
    fontSize: 20,
    fontFamily: 'Roboto-Bold',
    marginBottom: 15,
  },
  priceContainer: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'flex-end',
    marginBottom: 20,
  },
  currencySymbol: {
    fontSize: 22,
    fontFamily: 'Roboto-Bold',
    marginBottom: 5,
  },
  price: {
    fontSize: 36,
    fontFamily: 'Poppins-Bold',
  },
  duration: {
    fontSize: 16,
    fontFamily: 'Roboto',
    marginBottom: 5,
    paddingStart: 2,
  },
  featuresContainer: {
    marginTop: 10,
  },
  featureRow: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureIcon: {
    marginEnd: 10,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
    padding: 2,
  },
  featureText: {
    fontSize: 14,
    fontFamily: 'Roboto',
    flex: 1,
  },
  benefitsContainer: {
    marginBottom: 30,
  },
  benefitsTitle: {
    fontSize: 22,
    fontFamily: 'Poppins-Bold',
    marginBottom: 20,
  },
  benefitRow: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    marginBottom: 20,
  },
  benefitIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginEnd: 15,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontFamily: 'Roboto-Bold',
    marginBottom: 5,
  },
  benefitDescription: {
    fontSize: 14,
    fontFamily: 'Roboto',
    lineHeight: 20,
  },
  subscribeButton: {
    borderRadius: 30,
    overflow: 'hidden',
    marginBottom: 30,
    ...Platform.select({
      ios: {
        shadowColor: '#4CAF50',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  gradientButton: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 30,
  },
  buttonIcon: {
    marginEnd: 10,
  },
  subscribeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins-Bold',
  },
  secureInfoContainer: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  secureIcon: {
    marginEnd: 6,
  },
  secureInfoText: {
    fontSize: 12,
    fontFamily: 'Roboto',
  },
  selectPlanMessage: {
    fontSize: 14,
    fontFamily: 'Roboto',
    textAlign: 'center',
    marginBottom: 15,
    fontStyle: 'italic',
  },
  activeSubscription: {
    borderRadius: 16,
    padding: 25,
    alignItems: 'center',
    marginBottom: 30,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  subscriptionBadge: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 20,
  },
  subscriptionBadgeText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Poppins-Bold',
    marginStart: 8,
  },
  activeTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    marginBottom: 15,
  },
  activeDetails: {
    fontSize: 16,
    fontFamily: 'Roboto',
    marginBottom: 10,
  },
  renewalInfo: {
    fontSize: 14,
    fontFamily: 'Roboto',
    marginBottom: 25,
  },
  cancelButton: {
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Roboto-Bold',
  },
  resubscribeButton: {
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 10,
  },
  resubscribeButtonText: {
    fontSize: 14,
    fontFamily: 'Roboto-Bold',
  },
  upgradeButton: {
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 10,
  },
  upgradeButtonText: {
    fontSize: 14,
    fontFamily: 'Roboto-Bold',
  },
});

export default SubscriptionScreen;