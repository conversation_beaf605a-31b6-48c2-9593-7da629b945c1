# Guide to Fixing RTL/LTR Issues in Nasebi App

This guide provides step-by-step instructions for identifying and fixing RTL/LTR layout issues in the Nasebi app.

## Understanding RTL/LTR Issues

Right-to-left (RTL) languages like Arabic require special handling in UI layouts. Common issues include:

1. **Directional properties**: Using `left`/`right` instead of `start`/`end`
2. **Text alignment**: Hardcoded text alignment instead of RTL-aware alignment
3. **Flex direction**: Hardcoded row direction instead of RTL-aware direction
4. **Absolute positioning**: Not accounting for RTL in absolute positioning
5. **Icon mirroring**: Not flipping icons in RTL mode

## Tools for Identifying Issues

### RTL Checker Script

Run the RTL checker script to scan all components and screens for potential RTL issues:

```bash
node scripts/rtl-checker.js
```

This will generate a report of files with potential RTL issues.

## Step-by-Step Fixing Process

### 1. Add the useRTL Hook

First, import the useRTL hook in your component:

```jsx
import { useRTL } from '../hooks/useRTL';

const MyComponent = () => {
  const rtl = useRTL();
  // ...
}
```

### 2. Replace Directional Properties

Replace directional properties with logical properties:

```jsx
// Before
const styles = StyleSheet.create({
  container: {
    marginLeft: 10,
    paddingRight: 15,
  }
});

// After
const styles = StyleSheet.create({
  container: {
    marginStart: 10,
    paddingEnd: 15,
  }
});
```

### 3. Fix Text Alignment

Use the rtl.align style for text alignment:

```jsx
// Before
<Text style={{ textAlign: 'left' }}>Hello</Text>

// After
<Text style={rtl.align}>Hello</Text>
```

Or use a conditional:

```jsx
<Text style={{ textAlign: rtl.isRTL ? 'right' : 'left' }}>Hello</Text>
```

### 4. Fix Flex Direction

Use the rtl.direction style for flex direction:

```jsx
// Before
<View style={{ flexDirection: 'row' }}>...</View>

// After
<View style={rtl.direction}>...</View>
```

### 5. Fix Absolute Positioning

Use the rtl.positionAbsolute function for absolute positioning:

```jsx
// Before
<View style={{ 
  position: 'absolute', 
  top: 10, 
  right: 20, 
  bottom: 30, 
  left: 40 
}}>...</View>

// After
<View style={rtl.positionAbsolute(10, 20, 30, 40)}>...</View>
```

Or use conditional positioning:

```jsx
<View style={{ 
  position: 'absolute', 
  top: 10, 
  [rtl.isRTL ? 'left' : 'right']: 20,
  bottom: 30, 
  [rtl.isRTL ? 'right' : 'left']: 40 
}}>...</View>
```

### 6. Fix Icon Mirroring

Use the rtl.iconTransform style to mirror icons in RTL mode:

```jsx
// Before
<Ionicons name="arrow-forward" />

// After
<Ionicons name="arrow-forward" style={rtl.iconTransform} />
```

## Common RTL Issues by Component Type

### Text Components

- Use `rtl.align` for text alignment
- Use `textAlign={rtl.isRTL ? 'right' : 'left'}` for explicit alignment
- Set `writingDirection={rtl.isRTL ? 'rtl' : 'ltr'}` for TextInput components

### Layout Components

- Use `rtl.direction` for flex direction
- Use `marginStart`/`marginEnd` instead of `marginLeft`/`marginRight`
- Use `paddingStart`/`paddingEnd` instead of `paddingLeft`/`paddingRight`

### Absolute Positioned Components

- Use `rtl.positionAbsolute(top, end, bottom, start)` for absolute positioning
- Use `start`/`end` instead of `left`/`right` for positioning

### Icons and Images

- Use `rtl.iconTransform` to mirror directional icons
- Consider if images need to be mirrored in RTL mode

## Testing RTL Layout

1. **Switch languages**: Test the app in both Arabic and English
2. **Check alignment**: Ensure text is properly aligned in both languages
3. **Check positioning**: Ensure elements are positioned correctly in both languages
4. **Check gestures**: Ensure swipe gestures work correctly in both languages

## Troubleshooting Common Issues

### Text Not Aligned Correctly

- Check if the component is using `rtl.align` or conditional text alignment
- Check if TextInput components have `textAlign` and `writingDirection` set

### Elements Positioned Incorrectly

- Check if absolute positioning is using `rtl.positionAbsolute` or logical properties
- Check if margins and paddings are using logical properties

### Flex Direction Issues

- Check if flex containers are using `rtl.direction` or conditional flex direction
- Check if nested flex containers are also RTL-aware

### Icons Not Mirrored

- Check if directional icons are using `rtl.iconTransform`
- Check if custom icons have RTL-specific versions

## Best Practices

1. **Use the useRTL hook**: Always use the useRTL hook in components with UI elements
2. **Use logical properties**: Always use logical properties instead of directional properties
3. **Test in both languages**: Always test the app in both Arabic and English
4. **Use RTL-aware components**: Use RTL-aware components like RTLText and RTLTextInput
5. **Run the RTL checker script**: Regularly run the RTL checker script to identify issues
