# Translation System

This document explains the translation system used in the Nasebi app.

## Overview

The app uses a flat translation system without dots in keys. This makes it easier to maintain and use translations throughout the app.

## Files

- `en_flat.json` - English translations with flat keys
- `ar_flat.json` - Arabic translations with flat keys
- `i18n.js` - Configuration for the i18n system
- `useTranslationFlat.js` - Custom hook for using flat translations

## How to Use

### In Components

```jsx
import { useTranslationFlat } from '../../hooks/useTranslationFlat';

const MyComponent = () => {
  const { t } = useTranslationFlat();
  
  return (
    <View>
      <Text>{t('appName')}</Text>
      <Text>{t('welcome')}</Text>
    </View>
  );
};
```

### Adding New Translations

1. Add the new key to both `en_flat.json` and `ar_flat.json` files
2. Use the key directly without any dots or prefixes
3. Make sure the key is unique across the entire app

## Transition from Old System

The app previously used a nested translation system with dots in keys (e.g., `common.appName`). The new system uses flat keys without dots (e.g., `appName`).

The `useTranslationFlat` hook handles the transition by automatically converting old dotted keys to flat keys.

## Best Practices

1. Use descriptive keys that are easy to understand
2. Keep keys consistent across the app
3. Group related keys together in the translation files
4. Always add translations for both languages
5. Test translations in both languages before deploying

## Example

Old way:
```jsx
const { t } = useTranslation();
<Text>{t('common.appName')}</Text>
```

New way:
```jsx
const { t } = useTranslationFlat();
<Text>{t('appName')}</Text>
```
