{"name": "nasebi-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "postinstall": "npx patch-package"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.17.11", "@react-native-community/datetimepicker": "^6.7.3", "@react-native-community/slider": "4.4.2", "@react-native-picker/picker": "2.4.8", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@stripe/stripe-react-native": "0.23.3", "axios": "^1.6.2", "connect": "3.7.0", "dotenv": "^16.4.1", "expo": "~48.0.18", "expo-blur": "~12.2.2", "expo-constants": "~14.2.1", "expo-device": "~5.2.1", "expo-file-system": "~15.2.2", "expo-font": "~11.1.1", "expo-haptics": "~12.2.1", "expo-image-picker": "~14.1.1", "expo-linear-gradient": "~12.1.2", "expo-localization": "~14.1.1", "expo-notifications": "~0.18.1", "expo-splash-screen": "^0.18.2", "expo-status-bar": "~1.4.4", "expo-updates": "~0.16.4", "i18next": "^25.0.0", "jwt-decode": "^4.0.0", "metro": "0.76.7", "metro-core": "0.76.7", "metro-react-native-babel-transformer": "^0.77.0", "metro-runtime": "0.76.7", "nativewind": "^4.1.23", "postcss": "^8.4.23", "react": "18.2.0", "react-i18next": "^15.4.1", "react-native": "0.71.14", "react-native-gesture-handler": "~2.9.0", "react-native-reanimated": "~2.14.4", "react-native-safe-area-context": "^4.5.0", "react-native-screens": "~3.20.0", "react-native-snap-carousel-v4": "^1.0.1", "tailwindcss": "^3.3.2", "use-latest-callback": "^0.2.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/preset-flow": "^7.25.9", "@babel/preset-typescript": "^7.27.0", "@react-native-community/cli": "^18.0.0", "@react-native/metro-config": "^0.79.1", "@types/react": "~18.0.27", "node-libs-react-native": "^1.2.1", "react-native-svg-transformer": "^1.5.0", "typescript": "^4.9.5"}, "private": true}