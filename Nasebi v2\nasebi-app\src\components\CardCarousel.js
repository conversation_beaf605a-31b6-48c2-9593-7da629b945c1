import React, { useRef, useState } from 'react';
import { useRTL } from '../hooks/useRTL';

import { 
  View, 
  Text, 
  StyleSheet, 
  Animated, 
  PanResponder,
  Dimensions
} from 'react-native';

const SCREEN_WIDTH = Dimensions.get('window').width;
const SWIPE_THRESHOLD = 0.25 * SCREEN_WIDTH;
const SWIPE_OUT_DURATION = 250;

const CardCarousel = ({ data, renderCard, onSwipeLeft, onSwipeRight, renderNoMoreCards }) => {
  const rtl = useRTL();

  const [index, setIndex] = useState(0);
  const position = useRef(new Animated.ValueXY()).current;
  const rotation = position.x.interpolate({
    inputRange: [-SCREEN_WIDTH / 2, 0, SCREEN_WIDTH / 2],
    outputRange: ['-10deg', '0deg', '10deg'],
    extrapolate: 'clamp'
  });

  // Add like/unlike indicators
  const likeOpacity = position.x.interpolate({
    inputRange: [0, SCREEN_WIDTH / 4],
    outputRange: [0, 1],
    extrapolate: 'clamp'
  });

  const unlikeOpacity = position.x.interpolate({
    inputRange: [-SCREEN_WIDTH / 4, 0],
    outputRange: [1, 0],
    extrapolate: 'clamp'
  });

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (event, gesture) => {
        position.setValue({ x: gesture.dx, y: gesture.dy });
      },
      onPanResponderRelease: (event, gesture) => {
        if (gesture.dx > SWIPE_THRESHOLD) {
          forceSwipe('right');
        } else if (gesture.dx < -SWIPE_THRESHOLD) {
          forceSwipe('left');
        } else {
          resetPosition();
        }
      }
    })
  ).current;

  const forceSwipe = (direction) => {
    const x = direction === 'right' ? SCREEN_WIDTH : -SCREEN_WIDTH;
    Animated.timing(position, {
      toValue: { x, y: 0 },
      duration: SWIPE_OUT_DURATION,
      useNativeDriver: false
    }).start(() => onSwipeComplete(direction));
  };

  const onSwipeComplete = (direction) => {
    const item = data[index];
    direction === 'right' ? onSwipeRight(item) : onSwipeLeft(item);
    position.setValue({ x: 0, y: 0 });
    setIndex(index + 1);
  };

  const resetPosition = () => {
    Animated.spring(position, {
      toValue: { x: 0, y: 0 },
      useNativeDriver: false
    }).start();
  };

  const getCardStyle = () => {
    const rotate = rotation;
    return {
      ...position.getLayout(),
      transform: [{ rotate }]
    };
  };

  if (index >= data.length) {
    return renderNoMoreCards ? renderNoMoreCards() : (
      <View style={styles.noMoreCards}>
        <Text>No more cards!</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {data.map((item, i) => {
        if (i < index) return null;

        if (i === index) {
          return (
            <Animated.View
              key={item.id}
              style={[styles.cardStyle, getCardStyle()]}
              {...panResponder.panHandlers}
            >
              {renderCard(item)}
              
              {/* Like indicator */}
              <Animated.View style={[styles.overlayLabel, styles.likeLabel, { opacity: likeOpacity }]}>
                <Text style={styles.overlayText}>LIKE</Text>
              </Animated.View>
              
              {/* Unlike indicator */}
              <Animated.View style={[styles.overlayLabel, styles.unlikeLabel, { opacity: unlikeOpacity }]}>
                <Text style={styles.overlayText}>NOPE</Text>
              </Animated.View>
            </Animated.View>
          );
        }

        // Return next card with slight offset
        return (
          <Animated.View
            key={item.id}
            style={[styles.cardStyle, { top: 10 * (i - index), zIndex: -i }]}
          >
            {renderCard(item)}
          </Animated.View>
        );
      }).reverse()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardStyle: {
    position: 'absolute',
    width: SCREEN_WIDTH * 0.9,
    height: SCREEN_WIDTH * 1.3,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
  },
  overlayLabel: {
    position: 'absolute',
    padding: 10,
    borderWidth: 3,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  likeLabel: {
    top: 25,
    start: 25,
    transform: [{ rotate: '-30deg' }],
    borderColor: '#4CAF50',
  },
  unlikeLabel: {
    top: 25,
    end: 25,
    transform: [{ rotate: '30deg' }],
    borderColor: '#F44336',
  },
  overlayText: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  noMoreCards: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default CardCarousel;