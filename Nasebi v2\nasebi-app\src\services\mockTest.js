import { mockApiHandler } from './mockApi';

// Test the conversation detail endpoint
async function testConversationDetail() {
  try {
    // Test with a valid ID (assuming '1' exists in the mock data)
    const response1 = await mockApiHandler({ 
      url: '/api/conversations/1', 
      method: 'GET'
    });
    console.log('Conversation detail response:', response1);
    
    // Test with an invalid ID
    const response2 = await mockApiHandler({
      url: '/api/conversations/999',
      method: 'GET'
    });
    console.log('Should not reach here:', response2);
  } catch (error) {
    console.error('Error test:', error.message);
  }
}

testConversationDetail(); 