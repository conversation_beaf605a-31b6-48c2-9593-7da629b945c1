import React, { useEffect, useRef } from 'react';
import { View, Image, StyleSheet, Animated } from 'react-native';
import { useTheme } from '../context';
import { useRTL } from '../hooks';

const TabIcon = ({ focused, icon, size = 28 }) => {
  const { colors, isDark } = useTheme();
  const { isRTL } = useRTL();

  // Animation refs
  const scaleAnim = useRef(new Animated.Value(focused ? 1.1 : 1)).current;
  const opacityAnim = useRef(new Animated.Value(focused ? 1 : 0.7)).current;

  // Define colors based on theme and focus state
  const activeTintColor = colors.primary;
  const inactiveTintColor = colors.subtext;

  // Run animation when focused state changes
  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: focused ? 1.1 : 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: focused ? 1 : 0.7,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, [focused, scaleAnim, opacityAnim]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={{
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        }}
      >
        <Image
          source={icon}
          style={[
            styles.icon,
            {
              width: size,
              height: size,
              tintColor: focused ? activeTintColor : inactiveTintColor,
              transform: [{ scaleX: isRTL ? -1 : 1 }],
            },
          ]}
          resizeMode="contain"
        />
      </Animated.View>

      {focused && (
        <View
          style={[
            styles.indicator,
            { backgroundColor: activeTintColor }
          ]}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    height: 48,
    position: 'relative',
  },
  icon: {
    width: 28,
    height: 28,
  },
  indicator: {
    position: 'absolute',
    bottom: 2,
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
});

export default TabIcon;