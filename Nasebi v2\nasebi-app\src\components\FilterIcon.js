import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Switch,
  TextInput,
  TouchableWithoutFeedback,
  Animated,
  FlatList
} from 'react-native';
import Slider from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useLanguage } from '../context/LanguageContext';
import { useTranslationFlat } from '../hooks/useTranslationFlat';
import { useSearchFilters } from '../context/SearchFiltersContext';
import { useRTL } from '../hooks/useRTL';


const FilterIcon = () => {
  const { colors, isDark } = useTheme();
  const { isRTL } = useLanguage();
  const { t } = useTranslationFlat();
  const { filters, updateFilters, resetFilters } = useSearchFilters();
  const [modalVisible, setModalVisible] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const [openDropdown, setOpenDropdown] = useState(null);
  const rtl = useRTL();

  // Local state for filters
  const [localFilters, setLocalFilters] = useState({ ...filters });

  // Update local filters when global filters change
  useEffect(() => {
    setLocalFilters({ ...filters });
  }, [filters]);

  const handleApplyFilters = () => {
    // Update global filters
    updateFilters(localFilters);

    // Close the modal and any open dropdowns
    setModalVisible(false);
    setOpenDropdown(null);
  };

  const handleResetFilters = () => {
    // Reset local filters
    setLocalFilters({ ...filters });

    // Reset global filters
    resetFilters();
  };

  const renderSlider = (value, setValue, min, max, step, label, valueLabel) => (
    <View style={styles.filterItem}>
      <View style={[
        styles.filterLabelContainer,
        { flexDirection: isRTL ? 'row-reverse' : 'row' }
      ]}>
        <Text style={[
          styles.filterLabel,
          {
            color: colors.text,
            textAlign: isRTL ? 'right' : 'left'
          }
        ]}>{label}</Text>
        <Text style={[styles.filterValue, { color: colors.primary }]}>{valueLabel}</Text>
      </View>
      <Slider
        style={[styles.slider, { transform: [{ scaleX: isRTL ? -1 : 1 }] }]}
        minimumValue={min}
        maximumValue={max}
        step={step}
        value={value}
        onValueChange={setValue}
        minimumTrackTintColor={colors.primary}
        maximumTrackTintColor={colors.border}
        thumbTintColor={colors.primary}
      />
    </View>
  );

  const renderRangeSlider = (minValue, maxValue, setMinValue, setMaxValue, min, max, step, label) => (
    <View style={styles.filterItem}>
      <Text style={[
        styles.filterLabel,
        {
          color: colors.text,
          textAlign: isRTL ? 'right' : 'left'
        }
      ]}>{label}</Text>
      <View style={[
        styles.rangeContainer,
        { flexDirection: isRTL ? 'row-reverse' : 'row' }
      ]}>
        <View style={styles.rangeValueContainer}>
          <Text style={[
            styles.rangeLabel,
            {
              color: colors.subtext,
              textAlign: isRTL ? 'right' : 'left'
            }
          ]}>{t('from')}</Text>
          <Text style={[styles.rangeValue, { color: colors.primary }]}>{minValue}</Text>
        </View>
        <View style={styles.rangeValueContainer}>
          <Text style={[
            styles.rangeLabel,
            {
              color: colors.subtext,
              textAlign: isRTL ? 'right' : 'left'
            }
          ]}>{t('to')}</Text>
          <Text style={[styles.rangeValue, { color: colors.primary }]}>{maxValue}</Text>
        </View>
      </View>
      <View style={styles.slidersContainer}>
        <Slider
          style={[styles.rangeSlider, { transform: [{ scaleX: isRTL ? -1 : 1 }] }]}
          minimumValue={min}
          maximumValue={max}
          step={step}
          value={minValue}
          onValueChange={(value) => {
            if (value <= maxValue) {
              setMinValue(value);
            }
          }}
          minimumTrackTintColor={colors.border}
          maximumTrackTintColor={colors.border}
          thumbTintColor={colors.primary}
        />
        <Slider
          style={[styles.rangeSlider, { transform: [{ scaleX: isRTL ? -1 : 1 }] }]}
          minimumValue={min}
          maximumValue={max}
          step={step}
          value={maxValue}
          onValueChange={(value) => {
            if (value >= minValue) {
              setMaxValue(value);
            }
          }}
          minimumTrackTintColor={colors.primary}
          maximumTrackTintColor={colors.border}
          thumbTintColor={colors.primary}
        />
      </View>
    </View>
  );

  const renderSwitch = (value, setValue, label) => (
    <View style={[
      styles.filterItem,
      styles.switchContainer,
      { flexDirection: isRTL ? 'row-reverse' : 'row' }
    ]}>
      <Text style={[
        styles.filterLabel,
        {
          color: colors.text,
          textAlign: isRTL ? 'right' : 'left'
        }
      ]}>{label}</Text>
      <Switch
        value={value}
        onValueChange={setValue}
        trackColor={{ false: colors.border, true: colors.primary + '80' }}
        thumbColor={value ? colors.primary : '#f4f3f4'}
      />
    </View>
  );

  const renderTextInput = (value, setValue, label, placeholder) => (
    <View style={styles.filterItem}>
      <Text style={[
        styles.filterLabel,
        {
          color: colors.text,
          textAlign: isRTL ? 'right' : 'left'
        }
      ]}>{label}</Text>
      <TextInput
        style={[
          styles.textInput,
          {
            color: colors.text,
            borderColor: colors.border,
            backgroundColor: colors.card,
            textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr'
          }
        ]}
        value={value}
        onChangeText={setValue}
        placeholder={placeholder}
        placeholderTextColor={colors.subtext}
      />
    </View>
  );

  const renderSelect = (value, setValue, label, options, placeholder, dropdownId) => {
    const isOpen = openDropdown === dropdownId;
    const selectRef = useRef(null);

    const toggleDropdown = () => {
      if (isOpen) {
        setOpenDropdown(null);
      } else {
        setOpenDropdown(dropdownId);
      }
    };

    return (
      <View style={styles.filterItem}>
        <Text style={[
          styles.filterLabel,
          {
            color: colors.text,
            textAlign: isRTL ? 'right' : 'left'
          }
        ]}>{label}</Text>
        <View style={{ position: 'relative', zIndex: isOpen ? 1000 : 1 }}>
          <TouchableOpacity
            style={[
              styles.selectInput,
              {
                color: colors.text,
                borderColor: isOpen ? colors.primary : colors.border,
                backgroundColor: colors.card,
                flexDirection: isRTL ? 'row-reverse' : 'row'
              }
            ]}
            onPress={toggleDropdown}
          >
            <Text style={{
              color: value ? colors.text : colors.subtext,
              textAlign: isRTL ? 'right' : 'left',
              flex: 1
            }}>
              {value ? options.find(opt => opt.value === value)?.label || value : placeholder}
            </Text>
            <Ionicons
              name={isOpen ? "chevron-up" : "chevron-down"}
              size={16}
              color={colors.text}
            />
          </TouchableOpacity>

          {isOpen && (
            <View style={[
              styles.optionsContainer,
              {
                backgroundColor: colors.card,
                borderColor: colors.border
              }
            ]}>
              <ScrollView nestedScrollEnabled={true} style={{ maxHeight: 150 }}>
                {options.map((item) => (
                  <TouchableOpacity
                    key={item.value}
                    style={[
                      styles.optionItem,
                      value === item.value && { backgroundColor: colors.primary + '20' }
                    ]}
                    onPress={() => {
                      setValue(item.value);
                      setOpenDropdown(null);
                    }}
                  >
                    <Text style={{
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left',
                      width: '100%'
                    }}>{item.label}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderTriStateSelect = (value, setValue, label, options) => {
    return (
      <View style={styles.filterItem}>
        <Text style={[
          styles.filterLabel,
          {
            color: colors.text,
            textAlign: isRTL ? 'right' : 'left'
          }
        ]}>{label}</Text>
        <View style={[
          styles.triStateContainer,
          { flexDirection: isRTL ? 'row-reverse' : 'row' }
        ]}>
          {options.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.triStateOption,
                value === option.value && {
                  backgroundColor: colors.primary,
                  borderColor: colors.primary
                },
                { borderColor: colors.border }
              ]}
              onPress={() => setValue(option.value)}
            >
              <Text style={{
                color: value === option.value ? 'white' : colors.text,
                fontWeight: value === option.value ? 'bold' : 'normal',
                textAlign: isRTL ? 'right' : 'left'
              }}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={() => setModalVisible(true)}>
        <Ionicons
          name="options-outline"
          size={30}
          color={colors.text}
          style={{ transform: [{ scaleX: isRTL ? -1 : 1 }] }}
        />
      </TouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          setModalVisible(false);
          setOpenDropdown(null);
        }}
      >
        <TouchableWithoutFeedback onPress={() => {
          setModalVisible(false);
          setOpenDropdown(null);
        }}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback onPress={(e) => {
              e.stopPropagation();
              // Close any open dropdown when tapping elsewhere in the modal
              if (openDropdown) {
                setOpenDropdown(null);
              }
            }}>
              <View style={[
                styles.filterContainer,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  [isRTL ? 'right' : 'left']: 20
                }
              ]}>
                <View style={[
                  styles.filterHeader,
                  {
                    borderBottomColor: colors.border,
                    flexDirection: isRTL ? 'row-reverse' : 'row'
                  }
                ]}>
                  <Text style={[
                    styles.filterTitle,
                    {
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}>
                    {t('advancedFilters')}
                  </Text>
                  <TouchableOpacity onPress={() => {
                    setModalVisible(false);
                    setOpenDropdown(null);
                  }}>
                    <Ionicons name="close" size={22} color={colors.text} />
                  </TouchableOpacity>
                </View>

                <ScrollView style={styles.filterContent}>
                  {/* Basic Information Section */}
                  <Text style={[
                    styles.sectionHeader,
                    {
                      borderBottomColor: colors.border,
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}>
                    {t('basicInfo')}
                  </Text>

                  {/* Age Range - Combined in one row */}
                  <View style={styles.filterItem}>
                    <Text style={[
                      styles.filterLabel,
                      {
                        color: colors.text,
                        textAlign: isRTL ? 'right' : 'left'
                      }
                    ]}>{t('ageRange')}</Text>
                    <View style={[
                      styles.ageRangeContainer,
                      { flexDirection: isRTL ? 'row-reverse' : 'row' }
                    ]}>
                      <View style={styles.ageRangeInputContainer}>
                        <Text style={[
                          styles.ageRangeLabel,
                          {
                            color: colors.subtext,
                            textAlign: isRTL ? 'right' : 'left'
                          }
                        ]}>{t('from')}</Text>
                        <View style={[
                          styles.ageRangeInput,
                          {
                            backgroundColor: colors.card,
                            borderColor: colors.border
                          }
                        ]}>
                          <Text style={{ color: colors.primary }}>{localFilters.ageRange[0]}</Text>
                          <View style={styles.ageRangeControls}>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.min(localFilters.ageRange[0] + 1, localFilters.ageRange[1]);
                                setLocalFilters({
                                  ...localFilters,
                                  ageRange: [newValue, localFilters.ageRange[1]]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-up" size={16} color={colors.text} />
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.max(localFilters.ageRange[0] - 1, 18);
                                setLocalFilters({
                                  ...localFilters,
                                  ageRange: [newValue, localFilters.ageRange[1]]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-down" size={16} color={colors.text} />
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>

                      <View style={styles.ageRangeInputContainer}>
                        <Text style={[
                          styles.ageRangeLabel,
                          {
                            color: colors.subtext,
                            textAlign: isRTL ? 'right' : 'left'
                          }
                        ]}>{t('to')}</Text>
                        <View style={[
                          styles.ageRangeInput,
                          {
                            backgroundColor: colors.card,
                            borderColor: colors.border
                          }
                        ]}>
                          <Text style={{ color: colors.primary }}>{localFilters.ageRange[1]}</Text>
                          <View style={styles.ageRangeControls}>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.min(localFilters.ageRange[1] + 1, 80);
                                setLocalFilters({
                                  ...localFilters,
                                  ageRange: [localFilters.ageRange[0], newValue]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-up" size={16} color={colors.text} />
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.max(localFilters.ageRange[1] - 1, localFilters.ageRange[0]);
                                setLocalFilters({
                                  ...localFilters,
                                  ageRange: [localFilters.ageRange[0], newValue]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-down" size={16} color={colors.text} />
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* Distance removed as requested */}

                  {/* Has Profile Photo */}
                  {renderSwitch(
                    localFilters.hasProfilePhoto,
                    (value) => setLocalFilters({
                      ...localFilters,
                      hasProfilePhoto: value
                    }),
                    t('hasProfilePhoto')
                  )}

                  {/* Height Range */}
                  <View style={styles.filterItem}>
                    <Text style={[
                      styles.filterLabel,
                      {
                        color: colors.text,
                        textAlign: isRTL ? 'right' : 'left'
                      }
                    ]}>{t('heightInCm')}</Text>
                    <View style={[
                      styles.ageRangeContainer,
                      { flexDirection: isRTL ? 'row-reverse' : 'row' }
                    ]}>
                      <View style={styles.ageRangeInputContainer}>
                        <Text style={[
                          styles.ageRangeLabel,
                          {
                            color: colors.subtext,
                            textAlign: isRTL ? 'right' : 'left'
                          }
                        ]}>{t('from')}</Text>
                        <View style={[
                          styles.ageRangeInput,
                          {
                            backgroundColor: colors.card,
                            borderColor: colors.border
                          }
                        ]}>
                          <Text style={{ color: colors.primary }}>{localFilters.height[0]}</Text>
                          <View style={styles.ageRangeControls}>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.min(localFilters.height[0] + 1, localFilters.height[1]);
                                setLocalFilters({
                                  ...localFilters,
                                  height: [newValue, localFilters.height[1]]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-up" size={16} color={colors.text} />
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.max(localFilters.height[0] - 1, 140);
                                setLocalFilters({
                                  ...localFilters,
                                  height: [newValue, localFilters.height[1]]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-down" size={16} color={colors.text} />
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>

                      <View style={styles.ageRangeInputContainer}>
                        <Text style={[
                          styles.ageRangeLabel,
                          {
                            color: colors.subtext,
                            textAlign: isRTL ? 'right' : 'left'
                          }
                        ]}>{t('to')}</Text>
                        <View style={[
                          styles.ageRangeInput,
                          {
                            backgroundColor: colors.card,
                            borderColor: colors.border
                          }
                        ]}>
                          <Text style={{ color: colors.primary }}>{localFilters.height[1]}</Text>
                          <View style={styles.ageRangeControls}>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.min(localFilters.height[1] + 1, 210);
                                setLocalFilters({
                                  ...localFilters,
                                  height: [localFilters.height[0], newValue]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-up" size={16} color={colors.text} />
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.max(localFilters.height[1] - 1, localFilters.height[0]);
                                setLocalFilters({
                                  ...localFilters,
                                  height: [localFilters.height[0], newValue]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-down" size={16} color={colors.text} />
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* Weight Range */}
                  <View style={styles.filterItem}>
                    <Text style={[
                      styles.filterLabel,
                      {
                        color: colors.text,
                        textAlign: isRTL ? 'right' : 'left'
                      }
                    ]}>{t('weightInKg')}</Text>
                    <View style={[
                      styles.ageRangeContainer,
                      { flexDirection: isRTL ? 'row-reverse' : 'row' }
                    ]}>
                      <View style={styles.ageRangeInputContainer}>
                        <Text style={[
                          styles.ageRangeLabel,
                          {
                            color: colors.subtext,
                            textAlign: isRTL ? 'right' : 'left'
                          }
                        ]}>{t('from')}</Text>
                        <View style={[
                          styles.ageRangeInput,
                          {
                            backgroundColor: colors.card,
                            borderColor: colors.border
                          }
                        ]}>
                          <Text style={{ color: colors.primary }}>{localFilters.weight[0]}</Text>
                          <View style={styles.ageRangeControls}>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.min(localFilters.weight[0] + 1, localFilters.weight[1]);
                                setLocalFilters({
                                  ...localFilters,
                                  weight: [newValue, localFilters.weight[1]]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-up" size={16} color={colors.text} />
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.max(localFilters.weight[0] - 1, 40);
                                setLocalFilters({
                                  ...localFilters,
                                  weight: [newValue, localFilters.weight[1]]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-down" size={16} color={colors.text} />
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>

                      <View style={styles.ageRangeInputContainer}>
                        <Text style={[
                          styles.ageRangeLabel,
                          {
                            color: colors.subtext,
                            textAlign: isRTL ? 'right' : 'left'
                          }
                        ]}>{t('to')}</Text>
                        <View style={[
                          styles.ageRangeInput,
                          {
                            backgroundColor: colors.card,
                            borderColor: colors.border
                          }
                        ]}>
                          <Text style={{ color: colors.primary }}>{localFilters.weight[1]}</Text>
                          <View style={styles.ageRangeControls}>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.min(localFilters.weight[1] + 1, 150);
                                setLocalFilters({
                                  ...localFilters,
                                  weight: [localFilters.weight[0], newValue]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-up" size={16} color={colors.text} />
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                const newValue = Math.max(localFilters.weight[1] - 1, localFilters.weight[0]);
                                setLocalFilters({
                                  ...localFilters,
                                  weight: [localFilters.weight[0], newValue]
                                });
                              }}
                              style={styles.ageRangeButton}
                            >
                              <Ionicons name="chevron-down" size={16} color={colors.text} />
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* Nationality */}
                  {renderTextInput(
                    localFilters.nationality,
                    (value) => setLocalFilters({
                      ...localFilters,
                      nationality: value
                    }),
                    t('nationality'),
                    t('enterNationality')
                  )}

                  {/* Ethnicity removed as requested */}

                  {/* Skin Color */}
                  {renderSelect(
                    localFilters.skinColor,
                    (value) => setLocalFilters({
                      ...localFilters,
                      skinColor: value
                    }),
                    t('skinColor'),
                    [
                      { label: t('skinColor_very_fair'), value: 'very_fair' },
                      { label: t('skinColor_fair'), value: 'fair' },
                      { label: t('skinColor_medium'), value: 'medium' },
                      { label: t('skinColor_tan'), value: 'tan' },
                      { label: t('skinColor_dark'), value: 'dark' },
                      { label: t('skinColor_very_dark'), value: 'very_dark' }
                    ],
                    t('chooseSkinColor'),
                    'skinColor'
                  )}

                  {/* Education and Career Section */}
                  <Text style={[
                    styles.sectionHeader,
                    {
                      borderBottomColor: colors.border,
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}>
                    {t('educationAndWork')}
                  </Text>

                  {/* Education Level */}
                  {renderSelect(
                    localFilters.educationLevel,
                    (value) => setLocalFilters({
                      ...localFilters,
                      educationLevel: value
                    }),
                    t('educationLevel'),
                    [
                      { label: t('education_less_than_highschool'), value: 'less_than_highschool' },
                      { label: t('education_highschool'), value: 'highschool' },
                      { label: t('education_college_degree'), value: 'college_degree' },
                      { label: t('education_masters'), value: 'masters' },
                      { label: t('education_doctorate'), value: 'doctorate' }
                    ],
                    t('chooseEducationLevel'),
                    'educationLevel'
                  )}

                  {/* Job Level */}
                  {renderSelect(
                    localFilters.jobLevel,
                    (value) => setLocalFilters({
                      ...localFilters,
                      jobLevel: value
                    }),
                    t('jobLevel'),
                    [
                      { label: t('job_student'), value: 'student' },
                      { label: t('job_employee'), value: 'employee' },
                      { label: t('job_senior_employee'), value: 'senior_employee' },
                      { label: t('job_manager'), value: 'manager' },
                      { label: t('job_unemployed'), value: 'unemployed' },
                      { label: t('job_prefer_not_to_say'), value: 'prefer_not_to_say' }
                    ],
                    t('chooseJobLevel'),
                    'jobLevel'
                  )}

                  {/* Income Level */}
                  {renderSelect(
                    localFilters.incomeLevel,
                    (value) => setLocalFilters({
                      ...localFilters,
                      incomeLevel: value
                    }),
                    t('incomeLevel'),
                    [
                      { label: t('income_no_income'), value: 'no_income' },
                      { label: t('income_low'), value: 'low' },
                      { label: t('income_average'), value: 'average' },
                      { label: t('income_high'), value: 'high' }
                    ],
                    t('chooseIncomeLevel'),
                    'incomeLevel'
                  )}

                  {/* Religious Attributes Section */}
                  <Text style={[
                    styles.sectionHeader,
                    {
                      borderBottomColor: colors.border,
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}>
                    {t('religiousInfo')}
                  </Text>

                  {/* Religious Level */}
                  {renderSelect(
                    localFilters.religiousLevel,
                    (value) => setLocalFilters({
                      ...localFilters,
                      religiousLevel: value
                    }),
                    t('religiousLevel'),
                    [
                      { label: t('religious_very_religious'), value: 'very_religious' },
                      { label: t('religious_religious'), value: 'religious' },
                      { label: t('religious_somewhat_religious'), value: 'somewhat_religious' },
                      { label: t('religious_not_religious'), value: 'not_religious' }
                    ],
                    t('chooseReligiousLevel'),
                    'religiousLevel'
                  )}

                  {/* Prayer Level */}
                  {renderSelect(
                    localFilters.prayerLevel,
                    (value) => setLocalFilters({
                      ...localFilters,
                      prayerLevel: value
                    }),
                    t('prayerLevel'),
                    [
                      { label: t('prayer_daily'), value: 'daily' },
                      { label: t('prayer_weekly'), value: 'weekly' },
                      { label: t('prayer_sometimes'), value: 'sometimes' },
                      { label: t('prayer_religious_occasions'), value: 'religious_occasions' },
                      { label: t('prayer_never'), value: 'never' }
                    ],
                    t('choosePrayerLevel'),
                    'prayerLevel'
                  )}

                  {/* Fasting Level */}
                  {renderSelect(
                    localFilters.fastingLevel,
                    (value) => setLocalFilters({
                      ...localFilters,
                      fastingLevel: value
                    }),
                    t('fastingLevel'),
                    [
                      { label: t('fasting_always'), value: 'always' },
                      { label: t('fasting_sometimes'), value: 'sometimes' },
                      { label: t('fasting_never'), value: 'never' },
                      { label: t('fasting_prefer_not_to_say'), value: 'prefer_not_to_say' }
                    ],
                    t('chooseFastingLevel'),
                    'fastingLevel'
                  )}

                  {/* Hajj Status */}
                  {renderSelect(
                    localFilters.hajjStatus,
                    (value) => setLocalFilters({
                      ...localFilters,
                      hajjStatus: value
                    }),
                    t('hajjStatus'),
                    [
                      { label: t('hajj_completed'), value: 'completed' },
                      { label: t('hajj_planning_soon'), value: 'planning_soon' },
                      { label: t('hajj_planning_future'), value: 'planning_future' },
                      { label: t('hajj_not_planned'), value: 'not_planned' }
                    ],
                    t('chooseHajjStatus'),
                    'hajjStatus'
                  )}

                  {/* Lifestyle Section */}
                  <Text style={[
                    styles.sectionHeader,
                    {
                      borderBottomColor: colors.border,
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}>
                    {t('lifestyle')}
                  </Text>

                  {/* Marital Status */}
                  {renderSelect(
                    localFilters.maritalStatus,
                    (value) => setLocalFilters({
                      ...localFilters,
                      maritalStatus: value
                    }),
                    t('maritalStatus'),
                    [
                      { label: t('maritalStatus_single'), value: 'single' },
                      { label: t('maritalStatus_divorced'), value: 'divorced' },
                      { label: t('maritalStatus_widowed'), value: 'widowed' },
                      { label: t('maritalStatus_married'), value: 'married' }
                    ],
                    t('chooseMaritalStatus'),
                    'maritalStatus'
                  )}

                  {/* Has Children */}
                  {renderTriStateSelect(
                    localFilters.hasChildren,
                    (value) => setLocalFilters({
                      ...localFilters,
                      hasChildren: value
                    }),
                    t('hasChildren'),
                    [
                      { label: t('common_yes'), value: true },
                      { label: t('common_no'), value: false },
                      { label: t('any'), value: null }
                    ]
                  )}

                  {/* Wants Children */}
                  {renderSelect(
                    localFilters.wantsChildren,
                    (value) => setLocalFilters({
                      ...localFilters,
                      wantsChildren: value
                    }),
                    t('wantsChildren'),
                    [
                      { label: t('children_soon'), value: 'soon' },
                      { label: t('children_after_two_years'), value: 'after_two_years' },
                      { label: t('children_depends'), value: 'depends' },
                      { label: t('children_no'), value: 'no' }
                    ],
                    t('chooseWantsChildren'),
                    'wantsChildren'
                  )}

                  {/* Smoking */}
                  {renderTriStateSelect(
                    localFilters.smoking,
                    (value) => setLocalFilters({
                      ...localFilters,
                      smoking: value
                    }),
                    t('smoking'),
                    [
                      { label: t('common_yes'), value: 'yes' },
                      { label: t('common_no'), value: 'no' },
                      { label: t('any'), value: null }
                    ]
                  )}

                  {/* Marriage Preferences Section */}
                  <Text style={[
                    styles.sectionHeader,
                    {
                      borderBottomColor: colors.border,
                      color: colors.text,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}>
                    {t('marriagePreferences')}
                  </Text>

                  {/* Marriage Readiness */}
                  {renderSelect(
                    localFilters.marriageReadiness,
                    (value) => setLocalFilters({
                      ...localFilters,
                      marriageReadiness: value
                    }),
                    t('marriageReadiness'),
                    [
                      { label: t('marriageReadiness_immediately'), value: 'immediately' },
                      { label: t('marriageReadiness_within_year'), value: 'within_year' },
                      { label: t('marriageReadiness_after_two_years'), value: 'after_two_years' },
                      { label: t('marriageReadiness_not_decided'), value: 'not_decided' }
                    ],
                    t('chooseMarriageReadiness'),
                    'marriageReadiness'
                  )}

                  {/* Preferred Residence */}
                  {renderSelect(
                    localFilters.preferredResidence,
                    (value) => setLocalFilters({
                      ...localFilters,
                      preferredResidence: value
                    }),
                    t('preferredResidence'),
                    [
                      { label: t('residence_own_home'), value: 'own_home' },
                      { label: t('residence_family_home'), value: 'family_home' },
                      { label: t('residence_family_home_temporarily'), value: 'family_home_temporarily' },
                      { label: t('residence_undecided'), value: 'undecided' }
                    ],
                    t('chooseResidence'),
                    'preferredResidence'
                  )}

                  {/* Allows Wife To Work */}
                  {renderSelect(
                    localFilters.allowsWifeToWork,
                    (value) => setLocalFilters({
                      ...localFilters,
                      allowsWifeToWork: value
                    }),
                    t('allowsWifeToWork'),
                    [
                      { label: t('work_yes'), value: 'yes' },
                      { label: t('work_yes_from_home'), value: 'yes_from_home' },
                      { label: t('work_depends'), value: 'depends' },
                      { label: t('work_no'), value: 'no' }
                    ],
                    t('chooseWifeWorkStatus'),
                    'allowsWifeToWork'
                  )}

                  {/* Tribal Affiliation */}
                  {renderTriStateSelect(
                    localFilters.tribalAffiliation,
                    (value) => setLocalFilters({
                      ...localFilters,
                      tribalAffiliation: value
                    }),
                    t('tribalAffiliation'),
                    [
                      { label: t('common_yes'), value: true },
                      { label: t('common_no'), value: false },
                      { label: t('any'), value: null }
                    ]
                  )}

                  {/* Health Status */}
                  {renderSelect(
                    localFilters.healthStatus,
                    (value) => setLocalFilters({
                      ...localFilters,
                      healthStatus: value
                    }),
                    t('healthStatus'),
                    [
                      { label: t('health_good_health'), value: 'good_health' },
                      { label: t('health_special_needs'), value: 'special_needs' },
                      { label: t('health_chronic_disease'), value: 'chronic_disease' },
                      { label: t('health_infertile'), value: 'infertile' }
                    ],
                    t('chooseHealthStatus'),
                    'healthStatus'
                  )}
                </ScrollView>

                <View style={[
                  styles.filterActions,
                  {
                    borderTopColor: colors.border,
                    flexDirection: isRTL ? 'row-reverse' : 'row'
                  }
                ]}>
                  <TouchableOpacity
                    style={[
                      styles.filterButton,
                      styles.resetButton,
                      {
                        borderColor: colors.border,
                        marginEnd: isRTL ? 0 : 8,
                        marginStart: isRTL ? 8 : 0
                      }
                    ]}
                    onPress={handleResetFilters}
                  >
                    <Text style={[styles.resetButtonText, { color: colors.text }]}>
                      {t('reset')}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.filterButton,
                      styles.applyButton,
                      {
                        backgroundColor: colors.primary,
                        marginStart: isRTL ? 0 : 8,
                        marginEnd: isRTL ? 8 : 0
                      }
                    ]}
                    onPress={handleApplyFilters}
                  >
                    <Text style={styles.applyButtonText}>
                      {t('apply')}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 10,
    marginHorizontal: 5,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
  },
  filterContainer: {
    position: 'absolute',
    top: 55,
    width: 320,
    maxHeight: 550,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  filterHeader: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterContent: {
    padding: 16,
  },
  filterItem: {
    marginBottom: 20,
    position: 'relative',
  },
  filterLabelContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  filterValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  slider: {
    width: '100%',
    height: 40,
  },
  switchContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  // Range slider styles
  rangeContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  rangeValueContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  rangeLabel: {
    fontSize: 14,
    marginEnd: 5,
  },
  rangeValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  slidersContainer: {
    height: 50,
    position: 'relative',
  },
  rangeSlider: {
    width: '100%',
    height: 40,
    position: 'absolute',
  },
  // Select dropdown styles
  selectInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionsContainer: {
    borderWidth: 1,
    borderRadius: 8,
    marginTop: 4,
    maxHeight: 150,
    zIndex: 10,
    position: 'absolute',
    start: 0,
    end: 0,
    top: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  optionItem: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  // Tri-state select styles
  triStateContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
  },
  triStateOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  // Section header
  sectionHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 15,
    paddingBottom: 8,
    borderBottomWidth: 1,
  },
  filterActions: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  resetButton: {
    borderWidth: 1,
  },
  applyButton: {
    // Margins are applied dynamically based on RTL
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  // Age range styles
  ageRangeContainer: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  ageRangeInputContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  ageRangeLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  ageRangeInput: {
    flexDirection: rtl.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  ageRangeControls: {
    flexDirection: 'column',
  },
  ageRangeButton: {
    padding: 2,
  }
});

export default FilterIcon;