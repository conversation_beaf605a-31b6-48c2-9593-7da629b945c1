// Mock conversation detail endpoint
mockResponses['/api/conversations/:id'] = (data, params) => {
  const id = params.id;
  const conversation = conversations.find(conv => conv.id === id);
  
  if (!conversation) {
    throw new Error(`Conversation with id ${id} not found`);
  }
  
  return {
    conversation: {
      ...conversation,
      messages: messages.filter(msg => msg.conversationId === id)
    }
  };
}; 