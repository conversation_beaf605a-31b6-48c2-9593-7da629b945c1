// Map English numbers to Arabic numbers
const arabicNumbers = {
  '0': '٠',
  '1': '١',
  '2': '٢',
  '3': '٣',
  '4': '٤',
  '5': '٥',
  '6': '٦',
  '7': '٧',
  '8': '٨',
  '9': '٩',
};

// Convert English numbers to Arabic numbers
export const toArabicNumbers = (str) => {
  if (typeof str !== 'string') {
    str = String(str);
  }
  return str.replace(/[0-9]/g, (match) => arabicNumbers[match]);
};

// Format numbers based on language
export const formatNumber = (number, language) => {
  if (language === 'ar') {
    return toArabicNumbers(number);
  }
  return number.toString();
};

// Handle Arabic text formatting
export const formatArabicText = (text, isRTL) => {
  if (!text) return '';
  
  // Add Arabic letter marks if needed
  const withMarks = text
    .replace(/ا/g, 'ـا')
    .replace(/د/g, 'ـد')
    .replace(/ذ/g, 'ـذ')
    .replace(/ر/g, 'ـر')
    .replace(/ز/g, 'ـز');

  // Handle numbers in the text
  return isRTL ? toArabicNumbers(withMarks) : text;
};

// Format date strings for Arabic
export const formatArabicDate = (date, language) => {
  const options = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };

  const dateString = new Date(date).toLocaleDateString(
    language === 'ar' ? 'ar-SA' : 'en-US',
    options
  );

  return language === 'ar' ? toArabicNumbers(dateString) : dateString;
};

// Format time strings for Arabic
export const formatArabicTime = (time, language) => {
  const options = {
    hour: '2-digit',
    minute: '2-digit',
  };

  const timeString = new Date(time).toLocaleTimeString(
    language === 'ar' ? 'ar-SA' : 'en-US',
    options
  );

  return language === 'ar' ? toArabicNumbers(timeString) : timeString;
};