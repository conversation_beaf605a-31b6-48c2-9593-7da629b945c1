import React, { createContext, useState, useEffect, useContext } from 'react';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { useAuth } from './AuthContext';
import { useTranslation } from 'react-i18next';
import api from '../services/api';

// Initialize Notifications configuration
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

const NotificationContext = createContext();

export const NotificationProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const { t } = useTranslation();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [expoPushToken, setExpoPushToken] = useState('');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  useEffect(() => {
    // Load notification settings
    const loadSettings = async () => {
      try {
        const enabled = await AsyncStorage.getItem('notifications-enabled');
        setNotificationsEnabled(enabled !== 'false');
      } catch (error) {
        console.error('Error loading notification settings:', error);
      }
    };

    loadSettings();

    // Register for push notifications
    if (user) {
      registerForPushNotifications();
    }

    // Listen for received notifications
    const notificationListener = Notifications.addNotificationReceivedListener(
      notification => {
        // Handle received notifications
        console.log('Notification received:', notification);
      }
    );

    // Listen for user interaction with notifications
    const responseListener = Notifications.addNotificationResponseReceivedListener(
      response => {
        // Handle notification response (when user taps on notification)
        console.log('Notification response:', response);
      }
    );

    return () => {
      Notifications.removeNotificationSubscription(notificationListener);
      Notifications.removeNotificationSubscription(responseListener);
    };
  }, [user]);

  // Register for push notifications
  const registerForPushNotifications = async () => {
    if (!Device.isDevice) {
      console.log('Push Notifications are not supported in the simulator');
      // Use a mock token in simulator
      setExpoPushToken('ExponentPushToken[simulator-token]');
      return;
    }

    try {
      // Check permissions first
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      // If not granted, request permission
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // If still not granted, use a mock token and return
      if (finalStatus !== 'granted') {
        console.log('Notification permission not granted');
        // Use a mock token when permissions are not granted
        if (__DEV__) {
          console.log('Using mock token in development mode');
          setExpoPushToken('ExponentPushToken[no-permission-token]');
        }
        return;
      }

      // Get the token
      try {
        // In development mode, use a mock token to avoid errors
        if (__DEV__) {
          console.log('Using mock token in development mode');
          setExpoPushToken('ExponentPushToken[dev-mode-token]');
          return;
        }

        // For production, use the actual token
        const projectId = Constants.expoConfig?.extra?.projectId || 'nasebi-app';

        console.log('Getting push token with projectId:', projectId);

        const token = await Notifications.getExpoPushTokenAsync({
          projectId: projectId,
        });

        setExpoPushToken(token.data);
        console.log('Successfully obtained push token:', token.data);
      } catch (tokenError) {
        console.log('Error getting push token:', tokenError);
        // Use a mock token when there's an error
        console.log('Using fallback token due to error');
        setExpoPushToken('ExponentPushToken[error-fallback-token]');
      }

      // Save token to server (should be implemented in API service)
      // We'll check for the token in a useEffect to ensure it's available
      // This is more reliable than checking immediately after trying to get it

      // Android specific channel setup
      if (Platform.OS === 'android') {
        Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#4CAF50',
        });
      }
    } catch (error) {
      console.error('Error getting push token:', error);
    }
  };

  // Toggle notifications
  const toggleNotifications = async (value) => {
    setNotificationsEnabled(value);
    try {
      await AsyncStorage.setItem('notifications-enabled', value ? 'true' : 'false');
    } catch (error) {
      console.error('Error saving notification settings:', error);
    }
  };

  // Schedule a local notification
  const scheduleLocalNotification = async (title, body, data = {}, trigger = null) => {
    if (!notificationsEnabled) return;

    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: true,
      },
      trigger: trigger || null,
    });
  };

  // Fetch notifications when user logs in
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchNotifications();
    } else {
      // Clear notifications if user logs out
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [isAuthenticated, user]);

  // Save push token to server when it becomes available
  useEffect(() => {
    if (user && expoPushToken) {
      // Save token to server
      console.log('Push token available, would save to server:', expoPushToken);
      // Uncomment when API endpoint is ready:
      // try {
      //   api.post('/api/users/push-token', { token: expoPushToken });
      // } catch (error) {
      //   console.log('Error saving push token to server:', error);
      // }
    }
  }, [user, expoPushToken]);

  // Sample notifications (for demonstration)
  const mockNotifications = [
    {
      id: 1,
      type: 'like',
      userId: '123',
      message: 'أعجب سارة بملفك الشخصي',
      time: 'منذ 5 دقائق',
      read: false,
    },
    {
      id: 2,
      type: 'message',
      userId: '456',
      message: 'أرسل محمد رسالة جديدة',
      time: 'منذ 30 دقيقة',
      read: false,
    },
    {
      id: 3,
      type: 'visit',
      userId: '789',
      message: 'زارت فاطمة ملفك الشخصي',
      time: 'منذ ساعتين',
      read: true,
    },
    {
      id: 4,
      type: 'like',
      userId: '101',
      message: 'أعجب أحمد بملفك الشخصي',
      time: 'منذ 3 ساعات',
      read: true,
    },
  ];

  // Fetch notifications from API
  const fetchNotifications = async () => {
    try {
      setLoading(true);

      // Fetch notifications from the API
      const response = await api.get('/api/notifications');

      if (response && response.data) {
        const notificationsData = response.data.notifications || [];
        setNotifications(notificationsData);

        // Set unread count from API response or calculate it
        const unreadCount = response.data.unreadCount !== undefined
          ? response.data.unreadCount
          : notificationsData.filter(n => !n.read).length;

        setUnreadCount(unreadCount);
        console.log(`Fetched ${notificationsData.length} notifications, ${unreadCount} unread`);
      } else {
        // Fallback to empty array if API fails
        console.log('No notifications data received from API');
        setNotifications([]);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      // Fallback to empty array if API fails
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  // Mark a notification as read
  const markAsRead = async (notificationId) => {
    try {
      // Call the API to mark notification as read
      await api.put(`/api/notifications/${notificationId}/read`);

      // Update locally
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, read: true }
            : notification
        )
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));

      console.log(`Marked notification ${notificationId} as read`);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      // Call the API to mark all notifications as read
      await api.put('/api/notifications/read-all');

      // Update locally
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );

      // Reset unread count
      setUnreadCount(0);

      console.log('Marked all notifications as read');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Delete a notification
  const deleteNotification = async (notificationId) => {
    try {
      // Call the API to delete the notification
      await api.delete(`/api/notifications/${notificationId}`);

      // Update locally
      const updatedNotifications = notifications.filter(
        notification => notification.id !== notificationId
      );

      setNotifications(updatedNotifications);

      // Update unread count if the deleted notification was unread
      const wasUnread = notifications.find(n => n.id === notificationId && !n.read);
      if (wasUnread) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }

      console.log(`Deleted notification ${notificationId}`);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // Handle notification click
  const handleNotificationPress = (notification) => {
    // Mark as read
    if (!notification.read) {
      markAsRead(notification.id);
    }

    // Handle different notification types
    // This would typically navigate to the appropriate screen
    switch (notification.type) {
      case 'like':
        // Navigate to the profile of the user who liked
        console.log(`Navigate to profile of user ${notification.userId}`);
        break;
      case 'message':
        // Navigate to chat with the user
        console.log(`Navigate to chat with user ${notification.userId}`);
        break;
      case 'visit':
        // Navigate to profile visitors or stats
        console.log(`Navigate to profile of user ${notification.userId}`);
        break;
      default:
        console.log('Unknown notification type');
    }
  };

  const value = {
    notifications,
    unreadCount,
    loading,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    handleNotificationPress,
    expoPushToken,
    notificationsEnabled,
    toggleNotifications,
    scheduleLocalNotification,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};