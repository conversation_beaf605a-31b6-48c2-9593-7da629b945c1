import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useRTL } from '../hooks/useRTL';
import { useLanguage } from '../context/LanguageContext';

/**
 * A wrapper View component that automatically applies RTL layout
 * based on current language settings
 */
const RTLView = ({
  children,
  style,
  row = false,
  reverse = false,
}) => {
  const rtl = useRTL();
  const { language } = useLanguage();
  
  // Determine default direction
  let direction = {};
  
  if (row) {
    // When row is true, apply RTL-aware flex direction
    const isReverse = reverse ? !rtl.isRTL : rtl.isRTL;
    direction = {
      flexDirection: isReverse ? 'row-reverse' : 'row'
    };
  }
  
  return (
    <View 
      style={[
        direction,
        style
      ]}
    >
      {children}
    </View>
  );
};

export default RTLView;