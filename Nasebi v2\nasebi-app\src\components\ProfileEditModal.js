import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Image,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useTranslationFlat } from '../hooks/useTranslationFlat';
import { useAuth } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import * as ImagePicker from 'expo-image-picker';
import { useRTL } from '../hooks/useRTL';

const ProfileEditModal = ({ visible, onClose, onSave }) => {
  const { theme, colors } = useTheme();
  const { t } = useTranslationFlat();
  const { userData, updateUser } = useAuth();
  const { isRTL } = useLanguage();
  const { isRTL: rtlEnabled, align } = useRTL();

  const [formData, setFormData] = useState({
    name: userData?.name || '',
    bio: userData?.bio || '',
    email: userData?.email || '',
    phone: userData?.phone || '',
    profileImage: userData?.profileImage || null,
  });

  // Default colors in case theme is not loaded yet
  const defaultColors = {
    background: '#FFFFFF',
    card: '#FFFFFF',
    text: '#333333',
    textSecondary: '#666666',
    border: '#EEEEEE',
    primary: '#93060d',
  };

  const currentColors = colors || defaultColors;

  const handleImagePick = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled) {
        setFormData(prev => ({
          ...prev,
          profileImage: result.assets[0].uri
        }));
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  const handleSave = async () => {
    try {
      console.log('Saving profile from modal:', JSON.stringify(formData, null, 2));

      // Create a complete profile object by merging with existing userData
      const completeProfileData = {
        ...userData,
        ...formData,
        // Ensure these fields are included if they exist in userData
        height: userData?.height,
        weight: userData?.weight,
        nationality: userData?.nationality,
        ethnicity: userData?.ethnicity,
        marital_status: userData?.marital_status,
        religious_level: userData?.religious_level,
        prayer_level: userData?.prayer_level,
        // Include photo if it was updated
        photos: formData.profileImage ? [
          {
            url: formData.profileImage,
            isPublic: true,
            isPrimary: true
          }
        ] : undefined
      };

      const result = await updateUser(completeProfileData);

      if (result && result.success) {
        console.log('Profile updated successfully from modal');
        onSave?.(formData);
        onClose();
      } else {
        console.error('Failed to update profile from modal:', result?.message);
      }
    } catch (error) {
      console.error('Error saving profile from modal:', error);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: currentColors.background }]}>
        <View style={[styles.modalContent, { backgroundColor: currentColors.card }]}>
          <Text style={[styles.modalTitle, { color: currentColors.text }]}>
            {t('editProfile')}
          </Text>

          <ScrollView style={styles.scrollView}>
            <TouchableOpacity
              style={styles.imageContainer}
              onPress={handleImagePick}
            >
              {formData.profileImage ? (
                <Image
                  source={{ uri: formData.profileImage }}
                  style={styles.profileImage}
                />
              ) : (
                <View style={[styles.imagePlaceholder, { backgroundColor: currentColors.border }]}>
                  <Text style={{ color: currentColors.text }}>
                    {t('addPhoto')}
                  </Text>
                </View>
              )}
            </TouchableOpacity>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: currentColors.text }]}>
                {t('name')}
              </Text>
              <TextInput
                style={[styles.input, {
                  backgroundColor: currentColors.background,
                  color: currentColors.text,
                  borderColor: currentColors.border
                }]}
                value={formData.name}
                onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
                placeholder={t('namePlaceholder')}
                placeholderTextColor={currentColors.textSecondary}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: currentColors.text }]}>
                {t('bio')}
              </Text>
              <TextInput
                style={[styles.input, styles.bioInput, {
                  backgroundColor: currentColors.background,
                  color: currentColors.text,
                  borderColor: currentColors.border
                }]}
                value={formData.bio}
                onChangeText={(text) => setFormData(prev => ({ ...prev, bio: text }))}
                placeholder={t('bioPlaceholder')}
                placeholderTextColor={currentColors.textSecondary}
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: currentColors.text }]}>
                {t('email')}
              </Text>
              <TextInput
                style={[styles.input, {
                  backgroundColor: currentColors.background,
                  color: currentColors.text,
                  borderColor: currentColors.border
                }]}
                value={formData.email}
                onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
                placeholder={t('emailPlaceholder')}
                placeholderTextColor={currentColors.textSecondary}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: currentColors.text }]}>
                {t('phone')}
              </Text>
              <TextInput
                style={[styles.input, {
                  backgroundColor: currentColors.background,
                  color: currentColors.text,
                  borderColor: currentColors.border
                }]}
                value={formData.phone}
                onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
                placeholder={t('phonePlaceholder')}
                placeholderTextColor={currentColors.textSecondary}
                keyboardType="phone-pad"
              />
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={[styles.buttonText, { color: currentColors.text }]}>
                {t('cancel')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.saveButton, { backgroundColor: currentColors.primary }]}
              onPress={handleSave}
            >
              <Text style={[styles.buttonText, { color: '#FFFFFF' }]}>
                {t('save')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  scrollView: {
    maxHeight: '80%',
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  imagePlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  bioInput: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 10,
  },
  buttonContainer: {
    flexDirection: 'row', // Remove RTL dependency
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    height: 50,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  saveButton: {
    backgroundColor: '#007AFF',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProfileEditModal;